from django.contrib.auth.models import User
from django.shortcuts import render, redirect, get_object_or_404
from .models import Investor
from django.contrib.auth import authenticate, login
from django.core.mail import send_mail
from django.conf import settings
import random
import re
import threading
from django.contrib.auth.decorators import login_required


# Function to send email asynchronously
def send_otp_email(email, otp):
    def send_email():
        send_mail(
            "Your OTP for Investor Signup",
            f"Your OTP is {otp}. It will expire in 10 minutes.",
            settings.EMAIL_HOST_USER,
            [email],
            fail_silently=False,
        )

    email_thread = threading.Thread(target=send_email)
    email_thread.start()


# View to handle sign-up and OTP generation
def investor_signup(request):
    if request.method == "POST":
        name = request.POST.get("name")
        username = request.POST.get("username")
        business_email = request.POST.get("business_email")
        entity_name = request.POST.get("entity_name")
        website = request.POST.get("website")
        type_ = request.POST.get("type")
        phone_number = request.POST.get("phone_number")
        password = request.POST.get("password")
        confirm_password = request.POST.get("confirm_password")

        # Validate username format - only allow letters, numbers, periods, hyphens, underscores
        if not re.match(r'^[a-zA-Z0-9._-]+$', username):
            return render(request, "ad_investor_signup.html", {
                "error": "Username can only contain letters, numbers, periods (.), hyphens (-), and underscores (_)."
            })

        if password == confirm_password:
            if not User.objects.filter(username=username).exists():
                # Generate a random 6-digit OTP
                otp = random.randint(100000, 999999)

                # Store OTP in session to verify later
                request.session["otp"] = otp
                request.session["signup_data"] = {
                    "name": name,
                    "username": username,
                    "business_email": business_email,
                    "entity_name": entity_name,
                    "website": website,
                    "type": type_,
                    "phone_number": phone_number,
                    "password": password,
                }

                # Send OTP via email
                send_otp_email(business_email, otp)

                # Redirect to OTP verification page
                return redirect("verify_otp")

    return render(request, "ad_investor_signup.html")


def verify_otp(request):
    if request.method == "POST":
        entered_otp = request.POST.get("otp")
        session_otp = request.session.get("otp")
        signup_data = request.session.get("signup_data")

        if int(entered_otp) == session_otp:
            # OTP is correct; now register the user
            user = User.objects.create_user(
                username=signup_data["username"],
                password=signup_data["password"],
                email=signup_data["business_email"],
            )
            Investor.objects.create(
                user=user,
                name=signup_data["name"],
                business_email=signup_data["business_email"],
                entity_name=signup_data["entity_name"],
                website=signup_data["website"],
                type=signup_data["type"],
                phone_number=signup_data["phone_number"],
            )
            # Log the user in after successful registration
            login(request, user)

            # Clear session data
            request.session.pop("otp")
            request.session.pop("signup_data")

            return redirect("investor_dashboard")  # Redirect to dashboard
        else:
            return render(
                request, "ad_investor_verify_otp.html", {"error": "Invalid OTP"}
            )

    return render(request, "ad_investor_verify_otp.html")


def resend_otp(request):
    signup_data = request.session.get("signup_data")
    if signup_data:
        otp = random.randint(100000, 999999)
        request.session["otp"] = otp
        send_otp_email(signup_data["business_email"], otp)
        return redirect("verify_otp")
    return redirect("verify_otp")


def investor_login(request):
    if request.method == "POST":
        username = request.POST.get("username")
        password = request.POST.get("password")
        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            return redirect("investor_dashboard")
        else:
            return render(
                request, "ad_investor_login.html", {"error": "Invalid credentials"}
            )

    return render(request, "ad_investor_login.html")


@login_required
def investor_dashboard(request):
    # You can fetch dynamic data here to pass to the template
    context = {
        "total_investment": 250000,  # Example value, fetch from your model
        "current_portfolio_value": 300000,  # Example value, fetch from your model
        "recent_investments": [
            "Investment in Startup A - $50,000",
            "Investment in Startup B - $30,000",
            "Investment in Startup C - $20,000",
            "Investment in Startup D - $25,000",
            "Investment in Startup E - $40,000",
        ],
    }
    return render(request, "ad_investor_dashboard.html", context)


@login_required
def investor_update(request):
    investor = get_object_or_404(Investor, user=request.user)
    if request.method == "POST":
        investor.name = request.POST.get("name")
        investor.business_email = request.POST.get("business_email")
        investor.entity_name = request.POST.get("entity_name")
        investor.website = request.POST.get("website")
        investor.type = request.POST.get("type")
        investor.save()
        return redirect(
            "investor_dashboard"
        )  # Redirect to the dashboard after updating
    return render(request, "investor/update.html", {"investor": investor})


@login_required
def investor_delete(request):
    investor = get_object_or_404(Investor, user=request.user)
    if request.method == "POST":
        investor.user.delete()  # This will also delete the associated Investor profile
        return redirect("home")  # Redirect to the home page after deletion
    return render(request, "investor/delete.html", {"investor": investor})
