<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create New Advertisement</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.8.1/font/bootstrap-icons.min.css"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    


    <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    

    <style>

        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 20%;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .heading{
            margin-top: 20px;
        }
        .sidebar.collapsed {
            width: 0;
            overflow: hidden;
            padding: 0;
        }

        .sidebar.collapsed .sidebar-content {
            display: none;
        }

        .sidebar .sidebar-content {
            padding: 1rem;
        }

        .sidebar a {
            text-decoration: none;
        }

        .sidebar a:hover {
            text-decoration: none;
        }

        .content {
            margin-left: 20%;
            transition: margin-left 0.3s ease;
        }

        .content.expanded {
            margin-left: 0;
        }

        .sidebar-toggle {
            position: fixed;
            /* bottom: 0; */
            top: 0;
            left: 0;
            z-index: 1000;
            cursor: pointer;
            border: none;
            /* background-color: aqua; */
        }

        .advertisement-line {
            border: 1px solid white;
            width: 100%;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        h2{
            margin-top: 30px;
        }
        .header-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .status-container {
            display: flex;
            align-items: center;
        }

        .status-label {
            margin-right: 10px;
        }

        .custom-switch {
            padding-bottom: 10px;
        }

        #priority-value {
            margin-top: 5px;
        }

        .bid-section {
            margin-top: 20px;
        }

        .upload-image {
            border: 2px solid #0A6A6D;
            border-radius: 20px;
            padding: 15px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .btn-primary{
            background-color: #0A6A6D;
        }
        .btn-primary:hover{
            background-color: #0A6A6D;
        }
    </style>
</head>

<body>
    <div class="sidebar bg-dark text-white">
        <div class="sidebar-content">
            <h2>Advertisement</h2>
            <hr class="advertisement-line">
            <ul class="list-unstyled mt-5">
                <li class="mb-4"><a href="#" class="text-white"><i class="bi bi-speedometer2"></i> Dashboard</a></li>
                <li class="mb-4"><a href="create-advertisement.html" class="text-white"><i
                            class="bi bi-plus-circle"></i> Create New Advertisement</a></li>
                <li class="mb-4"><a href="list-advertisements.html" class="text-white"><i class="bi bi-list-ul"></i>
                        List of Advertisements</a></li>
                <li class="mb-4"><a href="#" class="text-white"><i class="bi bi-credit-card"></i> Payment History</a>
                </li>
                <li class="mb-4"><a href="#" class="text-white"><i class="bi bi-question-circle"></i> Help</a></li>
            </ul>
        </div>
    </div>

    <div class="content flex-grow-1 p-4">
        <div class="sidebar-toggle">
            <button class="btn btn-primary" onclick="toggleSidebar()"><i class="bi bi-list"></i></button>
        </div>

        <div class="header-section">
            <h1>Create a New Advertisement</h1>
            <div class="status-container">
                <label for="status" class="status-label">Off</label>
                <div class="custom-control custom-switch">
                    <input type="checkbox" id="status" class="custom-control-input">
                    <label class="custom-control-label" for="status">On</label>
                </div>
            </div>
        </div>

        <form id="adForm">
            <div class="form-group">
                <label for="name">
                    <i class="fa-solid fa-rectangle-ad"></i> Name
                </label>
                <input type="text" id="name" class="form-control" placeholder="Enter your name" required>
            </div>


            <div class="row upload-image">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="image1">
                            <i class="fas fa-image"></i> Upload Horizontal Image
                        </label>
                        <input type="file" id="image1" class="form-control-file" accept="image/*"
                            onchange="validateImage(this)" required>
                        <small class="form-text text-muted">Upload image up to 30KB. | Image Resolution
                            720*360px</small>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label for="image2">
                            <i class="fas fa-image"></i> Upload Vertical Image
                        </label>
                        <input type="file" id="image2" class="form-control-file" accept="image/*"
                            onchange="validateImage(this)" required>
                        <small class="form-text text-muted">Upload image up to 30KB. | Image Resolution
                            360*720px</small>
                    </div>
                </div>
            </div>


            <div class="form-group">
                <label for="priority">
                    <i class="fas fa-arrow-up"></i> Priority
                </label>
                <input type="range" id="priority" class="form-control-range" min="1" max="10"
                    oninput="updatePriorityValue()">
                <div id="priority-value">5</div>
            </div>

            <div class="form-group">
                <label for="amount">
                    <i class="fa-solid fa-indian-rupee-sign"></i> Amount
                </label>
                <input type="number" id="amount" class="form-control" required>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="startDate">
                            <i class="fas fa-calendar-alt"></i> Start Date
                        </label>
                        <input type="date" id="startDate" class="form-control" required>
                    </div>
                </div>


                <div class="col-md-6">
                    <div class="form-group">
                        <label for="endDate">
                            <i class="fas fa-calendar-alt"></i> End Date
                        </label>
                        <input type="date" id="endDate" class="form-control" required>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="subject">
                            <i class="fas fa-bookmark"></i> Subject
                        </label>
                        <input type="text" id="subject" class="form-control" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="age">
                            <i class="fas fa-calendar-day"></i> Age
                        </label>
                        <input type="number" id="age" class="form-control" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="gender">
                            <i class="fas fa-genderless"></i> Gender
                        </label>
                        <select id="gender" class="form-control" required>
                            <option value="" disabled selected>Select Gender</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="city">
                    <i class="fas fa-city"></i> Location
                </label>
                <input type="text" id="city" class="form-control" required>
            </div>

            <!-- Bid Section -->
            <div class="bid-section">
                <label><i class="fas fa-balance-scale"></i> Bidding</label>
                <div class="form-group d-flex align-items-center" style="height: auto;">
                    <div class="custom-control custom-radio">
                        <input type="radio" id="autoBid" name="bidType" class="custom-control-input" value="auto"
                            checked>
                        <label class="custom-control-label" for="autoBid">Auto</label>
                    </div>
                    <div class="custom-control custom-radio ml-2">
                        <input type="radio" id="manualBid" name="bidType" class="custom-control-input"
                            value="manual">
                        <label class="custom-control-label" for="manualBid">Manual</label>
                    </div>
                    <div class="form-group d-none ml-2" id="manualBidInput">
                        <label for="manualBidAmount" class="sr-only"><i
                                class="fa-solid fa-indian-rupee-sign"></i></label>
                        <input type="number" id="manualBidAmount" class="form-control"
                            placeholder="Manual Bid Amount">
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-success mb-4">
                <i class="fas fa-check"></i> Submit
            </button>
        </form>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const content = document.querySelector('.content');

            sidebar.classList.toggle('collapsed');
            content.classList.toggle('expanded');
        }
    </script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const autoBid = document.getElementById('autoBid');
        const manualBid = document.getElementById('manualBid');
        const manualBidInput = document.getElementById('manualBidInput');

        function toggleBidInput() {
            if (manualBid.checked) {
                manualBidInput.classList.remove('d-none');
            } else {
                manualBidInput.classList.add('d-none');
            }
        }

        // Initialize the display based on the default selection
        toggleBidInput();

        // Attach event listeners to toggle the inputs when options are selected
        autoBid.addEventListener('change', toggleBidInput);
        manualBid.addEventListener('change', toggleBidInput);
    });
</script>
<script>
    // Update priority value display
    function updatePriorityValue() {
        const priority = document.getElementById('priority').value;
        document.getElementById('priority-value').textContent = priority;
    }

    // Toggle bid type inputs based on selection
    document.querySelectorAll('input[name="bidType"]').forEach(function (elem) {
        elem.addEventListener('change', function (event) {
            const bidType = event.target.value;
            if (bidType === 'auto') {
                document.getElementById('autoBidInput').classList.remove('d-none');
                document.getElementById('manualBidInput').classList.add('d-none');
            } else {
                document.getElementById('autoBidInput').classList.add('d-none');
                document.getElementById('manualBidInput').classList.remove('d-none');
            }
        });
    });

    // Validate image size
    function validateImage(input) {
        const file = input.files[0];
        if (file && file.size > 30 * 1024) { // 30KB
            alert("Image size should be less than 30KB");
            input.value = '';
        }
    }

    // Handle form submission
    document.getElementById('adForm').addEventListener('submit', function (event) {
        event.preventDefault();

        // Your form submission logic here

        alert("Successfully Submitted");
        this.reset(); // Clear the form after submission
    });
</script>

<!-- Bootstrap 5.3.3 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

</body>

</html>
