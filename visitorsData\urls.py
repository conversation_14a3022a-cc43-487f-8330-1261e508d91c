from django.urls import path
from . import views


urlpatterns = [
    path("", views.visitor_list, name="blog_data"),
    path("create/", views.add_visitor, name="create"),
    path("<slug:slug>/", views.visitor_detail, name="blog_data"),
    path("update/<slug:slug>/", views.edit_visitor, name="update"),
    # path("doupdate/<int:id>", views.blog_doupdate, name="doupdate"),
    path("delete/<slug:slug>/", views.delete_visitor, name="delete"),
    path("update-status/<int:visitor_id>/", views.update_visitor_status, name="update_visitor_status"),
    path("delete-visitor/<int:visitor_id>/", views.delete_visitor_by_id, name="delete_visitor_by_id"),
    path("get-visitor-slug/<int:visitor_id>/", views.get_visitor_slug, name="get_visitor_slug"),
]
