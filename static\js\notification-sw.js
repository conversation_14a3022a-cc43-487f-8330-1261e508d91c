/**
 * Service Worker for handling background notifications
 * Ensures notifications work even when the site is not active
 */

// Service Worker version for cache busting
const SW_VERSION = 'v1.0.0';
const CACHE_NAME = `lms-notifications-${SW_VERSION}`;

// Install event
self.addEventListener('install', (event) => {
    console.log('Notification Service Worker installing...', SW_VERSION);
    
    // Cache essential notification resources
    event.waitUntil(
        caches.open(CACHE_NAME).then((cache) => {
            return cache.addAll([
                '/static/images/notification-icon.png',
                '/static/images/badge-icon.png',
                '/static/sounds/notification.mp3'
            ]).catch((error) => {
                console.log('Cache add failed (some resources may not exist):', error);
            });
        })
    );
    
    // Force activation of new service worker
    self.skipWaiting();
});

// Activate event
self.addEventListener('activate', (event) => {
    console.log('Notification Service Worker activating...', SW_VERSION);
    
    // Clean up old caches
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames.map((cacheName) => {
                    if (cacheName.startsWith('lms-notifications-') && cacheName !== CACHE_NAME) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
    
    // Take control of all clients
    self.clients.claim();
});

// Handle background messages (when app is not in foreground)
self.addEventListener('message', (event) => {
    console.log('Service Worker received message:', event.data);
    
    if (event.data && event.data.type === 'BACKGROUND_MESSAGE') {
        const notificationData = event.data.payload;
        showNotification(notificationData);
    }
});

// Handle push events from FCM
self.addEventListener('push', (event) => {
    console.log('Push event received:', event);
    
    let notificationData = {};
    
    if (event.data) {
        try {
            notificationData = event.data.json();
        } catch (error) {
            console.error('Error parsing push data:', error);
            notificationData = {
                notification: {
                    title: 'New Notification',
                    body: 'You have a new notification'
                }
            };
        }
    }
    
    event.waitUntil(showNotification(notificationData));
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
    console.log('Notification clicked:', event.notification);
    
    event.notification.close();
    
    const notificationData = event.notification.data || {};
    const actionUrl = notificationData.action_url || '/';
    
    // Handle notification actions
    if (event.action) {
        switch (event.action) {
            case 'view':
                openUrl(actionUrl);
                break;
            case 'dismiss':
                // Just close the notification (already done above)
                break;
            default:
                openUrl(actionUrl);
        }
    } else {
        // Default click action
        openUrl(actionUrl);
    }
    
    // Track notification click
    trackNotificationEvent('click', notificationData);
});

// Handle notification close
self.addEventListener('notificationclose', (event) => {
    console.log('Notification closed:', event.notification);
    
    const notificationData = event.notification.data || {};
    trackNotificationEvent('close', notificationData);
});

/**
 * Show notification with enhanced options
 */
async function showNotification(data) {
    const notification = data.notification || {};
    const fcmData = data.data || {};
    
    const title = notification.title || 'LMS Notification';
    const body = notification.body || 'You have a new notification';
    
    // Detect if this is a mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    const options = {
        body: body,
        icon: notification.icon || '/static/images/notification-icon.png',
        badge: '/static/images/badge-icon.png',
        tag: fcmData.event_type || 'lms_notification',
        data: fcmData,
        requireInteraction: isMobile ? false : true, // Mobile users prefer auto-dismiss
        silent: false,
        timestamp: Date.now(),
        renotify: true, // Allow re-notification with same tag
        actions: isMobile ? [
            // Simplified actions for mobile
            {
                action: 'view',
                title: 'View'
            }
        ] : [
            // Full actions for desktop
            {
                action: 'view',
                title: 'View',
                icon: '/static/images/view-icon.png'
            },
            {
                action: 'dismiss',
                title: 'Dismiss',
                icon: '/static/images/dismiss-icon.png'
            }
        ]
    };
    
    // Add category-specific customizations
    if (fcmData.category) {
        switch (fcmData.category.toLowerCase()) {
            case 'registration':
                options.icon = '/static/images/registration-icon.png';
                options.actions[0].title = 'View Registration';
                break;
            case 'financial':
                options.icon = '/static/images/financial-icon.png';
                options.actions[0].title = 'View Invoice';
                break;
            case 'facility':
                options.icon = '/static/images/facility-icon.png';
                options.actions[0].title = 'View Booking';
                break;
        }
    }
    
    // Add priority-based customizations
    if (fcmData.priority === 'urgent') {
        options.requireInteraction = !isMobile; // Mobile: auto-dismiss, Desktop: require interaction
        options.silent = false;
        options.vibrate = isMobile ? [300, 100, 300, 100, 300] : [200, 100, 200, 100, 200];
    } else if (fcmData.priority === 'high') {
        options.requireInteraction = !isMobile;
        options.vibrate = isMobile ? [250, 100, 250] : [200, 100, 200];
    } else {
        // Normal priority
        options.vibrate = isMobile ? [150] : [100];
    }

    // Mobile-specific enhancements
    if (isMobile) {
        // Add mobile-friendly settings
        options.dir = 'auto';
        options.lang = navigator.language || 'en';

        // For iOS, we need to be more conservative
        if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
            options.requireInteraction = false;
            options.actions = []; // iOS doesn't support notification actions well
            options.vibrate = []; // iOS doesn't support vibration in notifications
        }
    }
    
    try {
        await self.registration.showNotification(title, options);
        console.log('Notification shown successfully');
        
        // Track notification display
        trackNotificationEvent('display', fcmData);
        
    } catch (error) {
        console.error('Error showing notification:', error);
    }
}

/**
 * Open URL in existing or new window/tab
 */
function openUrl(url) {
    return self.clients.matchAll({
        type: 'window',
        includeUncontrolled: true
    }).then((clientList) => {
        // Try to focus existing window with the URL
        for (let client of clientList) {
            if (client.url.includes(url.split('?')[0]) && 'focus' in client) {
                return client.focus();
            }
        }
        
        // Open new window if no existing window found
        if (self.clients.openWindow) {
            return self.clients.openWindow(url);
        }
    });
}

/**
 * Track notification events for analytics
 */
function trackNotificationEvent(eventType, notificationData) {
    try {
        // Send tracking data to server
        fetch('/librarian/track-notification-event/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                event_type: eventType,
                notification_data: notificationData,
                timestamp: Date.now(),
                user_agent: navigator.userAgent
            })
        }).catch((error) => {
            console.log('Could not track notification event:', error);
        });
    } catch (error) {
        console.log('Error tracking notification event:', error);
    }
}

/**
 * Handle fetch events (for caching notification resources)
 */
self.addEventListener('fetch', (event) => {
    // Only handle notification-related resources
    if (event.request.url.includes('/static/images/') && 
        (event.request.url.includes('notification') || 
         event.request.url.includes('badge') ||
         event.request.url.includes('icon'))) {
        
        event.respondWith(
            caches.match(event.request).then((response) => {
                return response || fetch(event.request);
            })
        );
    }
});

// Handle sync events (for offline notification queuing)
self.addEventListener('sync', (event) => {
    if (event.tag === 'notification-sync') {
        event.waitUntil(syncNotifications());
    }
});

/**
 * Sync queued notifications when back online
 */
async function syncNotifications() {
    try {
        // This would sync any queued notifications when back online
        console.log('Syncing queued notifications...');
        
        // Implementation would depend on your offline storage strategy
        // For now, just log that sync is available
        
    } catch (error) {
        console.error('Error syncing notifications:', error);
    }
}

console.log('Notification Service Worker loaded successfully', SW_VERSION);
