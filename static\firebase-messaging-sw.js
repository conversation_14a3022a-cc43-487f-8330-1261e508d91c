console.log('🔧 Firebase Service Worker loading...');

importScripts("https://www.gstatic.com/firebasejs/8.6.3/firebase-app.js");
importScripts("https://www.gstatic.com/firebasejs/8.6.3/firebase-messaging.js");

console.log('📦 Firebase scripts loaded');

// Firebase configuration - using librainian-app project
const firebaseConfig = {
  apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
  authDomain: "librainian-app.firebaseapp.com",
  projectId: "librainian-app",
  storageBucket: "librainian-app.firebasestorage.app",
  messagingSenderId: "623132670328",
  appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
  measurementId: "G-XNDKJL6JWH"
};

console.log('🔧 Initializing Firebase in service worker with config:', firebaseConfig);
firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();
console.log('✅ Firebase messaging initialized in service worker');

messaging.setBackgroundMessageHandler(function (payload) {
  console.log("Received background message ", payload);

  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: payload.notification.icon,
  };

  return self.registration.showNotification(notificationTitle, notificationOptions);
});
