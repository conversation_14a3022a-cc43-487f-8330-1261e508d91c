from django import template

register = template.Library()

@register.filter
def div(value, arg):
    """Divide value by arg"""
    try:
        return float(value) / float(arg)
    except (ValueError, ZeroDivisionError, TypeError):
        return 0

@register.filter
def mul(value, arg):
    """Multiply value by arg"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def subtract(value, arg):
    """Subtract arg from value"""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0
    
@register.filter
def add_float(value, arg):
    """Add arg to value (float version)"""
    try:
        return float(value) + float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def calculate_daily_rate(monthly_rate):
    """Calculate daily rate from monthly rate"""
    try:
        return round(float(monthly_rate) / 30)
    except (ValueError, TypeError):
        return 0

@register.filter
def calculate_weekly_rate(monthly_rate):
    """Calculate weekly rate from monthly rate"""
    try:
        return round(float(monthly_rate) / 4.33)
    except (ValueError, TypeError):
        return 0

@register.filter
def calculate_half_weekly_rate(monthly_rate):
    """Calculate half-weekly rate with 10% premium"""
    try:
        weekly_rate = float(monthly_rate) / 4.33
        half_rate = weekly_rate / 2
        return round(half_rate + (half_rate * 0.1))
    except (ValueError, TypeError):
        return 0

@register.filter
def calculate_half_monthly_rate(monthly_rate):
    """Calculate half-monthly rate with 10% premium"""
    try:
        half_rate = float(monthly_rate) / 2
        return round(half_rate + (half_rate * 0.1))
    except (ValueError, TypeError):
        return 0
