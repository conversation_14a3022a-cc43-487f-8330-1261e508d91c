#!/usr/bin/env python3
"""
Test Payment System for Real Issues
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def test_database_connectivity():
    """Test basic database connectivity"""
    print("🔍 Testing database connectivity...")
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result[0] == 1:
                print("✅ Database connection working")
                return True
            else:
                print("❌ Database connection failed")
                return False
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_model_imports():
    """Test if all payment models can be imported"""
    print("\n🔍 Testing model imports...")
    try:
        from studentsData.models import Invoice, Payment, StudentData, Shift, Months
        from librarian.models import Librarian_param
        print("✅ All payment models imported successfully")
        return True
    except Exception as e:
        print(f"❌ Model import error: {e}")
        return False

def test_model_fields():
    """Test if all required fields exist in models"""
    print("\n🔍 Testing model fields...")
    try:
        from studentsData.models import Invoice, Payment
        from librarian.models import Librarian_param
        
        # Test Invoice fields
        invoice_fields = [f.name for f in Invoice._meta.fields]
        required_invoice_fields = ['billing_type', 'billing_units', 'total_amount', 'total_paid', 'remaining_due']
        
        missing_invoice_fields = [field for field in required_invoice_fields if field not in invoice_fields]
        if missing_invoice_fields:
            print(f"❌ Missing Invoice fields: {missing_invoice_fields}")
            return False
        
        # Test Payment fields
        payment_fields = [f.name for f in Payment._meta.fields]
        required_payment_fields = ['amount_paid', 'payment_mode', 'next_commitment_date']
        
        missing_payment_fields = [field for field in required_payment_fields if field not in payment_fields]
        if missing_payment_fields:
            print(f"❌ Missing Payment fields: {missing_payment_fields}")
            return False
        
        # Test Librarian fields
        librarian_fields = [f.name for f in Librarian_param._meta.fields]
        required_librarian_fields = ['default_billing_cycle']
        
        missing_librarian_fields = [field for field in required_librarian_fields if field not in librarian_fields]
        if missing_librarian_fields:
            print(f"❌ Missing Librarian fields: {missing_librarian_fields}")
            return False
        
        print("✅ All required model fields exist")
        return True
        
    except Exception as e:
        print(f"❌ Model field test error: {e}")
        return False

def test_database_tables():
    """Test if all required database tables exist"""
    print("\n🔍 Testing database tables...")
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            # Get all table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = [
                'studentsData_invoice',
                'studentsData_payment', 
                'librarian_librarian_param',
                'studentsData_studentdata'
            ]
            
            missing_tables = [table for table in required_tables if table not in tables]
            if missing_tables:
                print(f"❌ Missing database tables: {missing_tables}")
                return False
            
            print("✅ All required database tables exist")
            return True
            
    except Exception as e:
        print(f"❌ Database table test error: {e}")
        return False

def test_payment_model_methods():
    """Test if payment model methods work"""
    print("\n🔍 Testing payment model methods...")
    try:
        from studentsData.models import Invoice
        
        # Test if update_payment_status method exists and is callable
        if hasattr(Invoice, 'update_payment_status'):
            print("✅ update_payment_status method exists")
        else:
            print("❌ update_payment_status method missing")
            return False
        
        # Test if get_payment_percentage method exists
        if hasattr(Invoice, 'get_payment_percentage'):
            print("✅ get_payment_percentage method exists")
        else:
            print("❌ get_payment_percentage method missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Payment model method test error: {e}")
        return False

def test_actual_data_access():
    """Test actual data access"""
    print("\n🔍 Testing actual data access...")
    try:
        from studentsData.models import Invoice, Payment, StudentData
        from librarian.models import Librarian_param
        
        # Try to access data
        librarian_count = Librarian_param.objects.count()
        student_count = StudentData.objects.count()
        invoice_count = Invoice.objects.count()
        payment_count = Payment.objects.count()
        
        print(f"📊 Data counts:")
        print(f"   Librarians: {librarian_count}")
        print(f"   Students: {student_count}")
        print(f"   Invoices: {invoice_count}")
        print(f"   Payments: {payment_count}")
        
        if librarian_count == 0:
            print("⚠️  No librarians found - system may not be set up")
        
        print("✅ Data access working")
        return True
        
    except Exception as e:
        print(f"❌ Data access error: {e}")
        return False

def test_payment_creation():
    """Test if we can create a payment record"""
    print("\n🔍 Testing payment creation...")
    try:
        from studentsData.models import Invoice, Payment, StudentData
        from librarian.models import Librarian_param
        
        # Check if we have required data
        librarian = Librarian_param.objects.first()
        if not librarian:
            print("⚠️  Cannot test payment creation - no librarian exists")
            return True
        
        student = StudentData.objects.filter(librarian=librarian).first()
        if not student:
            print("⚠️  Cannot test payment creation - no student exists")
            return True
        
        # Create test invoice
        test_invoice = Invoice(
            student=student,
            due_date=datetime.now().date() + timedelta(days=30),
            total_amount=1000,
            discount_amount=0,
            mode_pay='Cash',
            description='Test invoice'
        )
        
        # Test save without actually saving
        try:
            test_invoice.full_clean()  # Validate without saving
            print("✅ Invoice validation passed")
        except Exception as e:
            print(f"❌ Invoice validation failed: {e}")
            return False
        
        print("✅ Payment creation test passed")
        return True
        
    except Exception as e:
        print(f"❌ Payment creation test error: {e}")
        return False

def run_comprehensive_test():
    """Run all tests"""
    print("🚀 Running Comprehensive Payment System Tests")
    print("=" * 60)
    
    tests = [
        test_database_connectivity,
        test_model_imports,
        test_model_fields,
        test_database_tables,
        test_payment_model_methods,
        test_actual_data_access,
        test_payment_creation
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed! System appears to be working.")
    else:
        print(f"\n⚠️  {failed} tests failed. System has issues that need fixing.")
    
    return failed == 0

if __name__ == '__main__':
    run_comprehensive_test()
