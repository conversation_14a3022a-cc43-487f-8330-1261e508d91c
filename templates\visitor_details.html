{% extends "base.html" %}

{% block title %}{{ visitor.name }} - Visitor Details{% endblock %}

{% block page_title %}Visitor Details{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="/visitors/">Visitors</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ visitor.name }}</li>
{% endblock %}
{% block extra_css %}
<style>
    /* Visitor Details Glass Theme */
    .visitor-details-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: calc(100vh - var(--topbar-height));
        padding: 2rem;
        position: relative;
    }

    .visitor-details-content::before {
        content: '';
        position: fixed;
        top: var(--topbar-height);
        left: 0;
        width: 100%;
        height: calc(100% - var(--topbar-height));
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    .visitor-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset,
            0 2px 4px rgba(255, 255, 255, 0.1) inset;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .visitor-card:hover {
        transform: translateY(-2px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .visitor-card-header {
        padding: 2rem 2rem 1rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        text-align: center;
    }

    .visitor-card-body {
        padding: 2rem;
    }

    .visitor-name {
        font-family: 'Plus Jakarta Sans', sans-serif;
        font-weight: 700;
        font-size: 2rem;
        color: white;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .visitor-id {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }
    .visitor-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .visitor-info-item {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }

    .visitor-info-item:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
    }

    .info-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.7);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-value {
        font-size: 1.125rem;
        font-weight: 500;
        color: white;
        word-break: break-word;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .info-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        font-size: 0.75rem;
    }

    .action-btn {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        color: white;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0.25rem;
    }

    .action-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .action-btn.btn-primary {
        background: rgba(99, 102, 241, 0.3);
        border-color: rgba(99, 102, 241, 0.5);
    }

    .action-btn.btn-warning {
        background: rgba(245, 158, 11, 0.3);
        border-color: rgba(245, 158, 11, 0.5);
    }

    .action-btn.btn-danger {
        background: rgba(239, 68, 68, 0.3);
        border-color: rgba(239, 68, 68, 0.5);
    }

    .contact-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .contact-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        padding: 0.5rem;
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
    }

    .contact-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: scale(1.1);
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: capitalize;
        background: rgba(16, 185, 129, 0.2);
        color: #10b981;
        border: 1px solid rgba(16, 185, 129, 0.3);
    }

    .shift-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .shift-badge {
        background: rgba(99, 102, 241, 0.2);
        color: #6366f1;
        border: 1px solid rgba(99, 102, 241, 0.3);
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .actions-container {
        text-align: center;
        margin-top: 2rem;
    }

    /* Mobile Responsive */
    @media (max-width: 991.98px) {
        .visitor-details-content {
            padding: 1rem;
            padding-bottom: calc(1rem + var(--bottom-menu-height));
        }
    }

    @media (max-width: 767.98px) {
        .visitor-details-content {
            padding: 0.75rem;
            padding-bottom: calc(0.75rem + var(--bottom-menu-height));
        }

        .visitor-card-header,
        .visitor-card-body {
            padding: 1.5rem;
        }

        .visitor-name {
            font-size: 1.5rem;
        }

        .visitor-info-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .visitor-info-item {
            padding: 1rem;
        }

        .action-btn {
            padding: 0.625rem 1.25rem;
            font-size: 0.875rem;
            width: 100%;
            margin: 0.25rem 0;
            justify-content: center;
        }

        .contact-actions {
            justify-content: center;
            margin-top: 1rem;
        }
    }

    /* Disable zoom and text selection */
    body {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        touch-action: manipulation;
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent;
    }

    /* Animation for page load */
    .visitor-card {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="visitor-details-content">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-10 col-xl-8">
                <!-- Visitor Card -->
                <div class="visitor-card">
                    <!-- Card Header -->
                    <div class="visitor-card-header">
                        <h1 class="visitor-name">{{ visitor.name }}</h1>
                        <p class="visitor-id">Visitor ID: {{ visitor.inqid }}</p>
                    </div>

                    <!-- Card Body -->
                    <div class="visitor-card-body">
                        <!-- Visitor Information Grid -->
                        <div class="visitor-info-grid">
                            <!-- Visit Date -->
                            <div class="visitor-info-item">
                                <div class="info-label">
                                    <div class="info-icon">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    Visit Date
                                </div>
                                <div class="info-value">{{ visitor.date }}</div>
                            </div>

                            <!-- Contact Information -->
                            <div class="visitor-info-item">
                                <div class="info-label">
                                    <div class="info-icon">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    Mobile Number
                                </div>
                                <div class="info-value">
                                    {{ visitor.contact }}
                                    <div class="contact-actions">
                                        <a href="tel:{{ visitor.contact }}" class="contact-btn" title="Call">
                                            <i class="fas fa-phone-alt"></i>
                                        </a>
                                        <a href="javascript:void(0);" onclick="copyToClipboard('{{ visitor.contact }}')" class="contact-btn" title="Copy">
                                            <i class="fas fa-copy"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="visitor-info-item">
                                <div class="info-label">
                                    <div class="info-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    Email Address
                                </div>
                                <div class="info-value">
                                    {{ visitor.email }}
                                    <div class="contact-actions">
                                        <a href="https://mail.google.com/mail/?view=cm&fs=1&to={{ visitor.email }}" target="_blank" class="contact-btn" title="Send Email">
                                            <i class="fas fa-envelope"></i>
                                        </a>
                                        <a href="javascript:void(0);" onclick="copyToClipboard('{{ visitor.email }}')" class="contact-btn" title="Copy">
                                            <i class="fas fa-copy"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Shifts -->
                            <div class="visitor-info-item">
                                <div class="info-label">
                                    <div class="info-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    Interested Shifts
                                </div>
                                <div class="info-value">
                                    <div class="shift-badges">
                                        {% for shift in visitor.shift.all %}
                                            <span class="shift-badge">{{ shift.name }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="visitor-info-item">
                                <div class="info-label">
                                    <div class="info-icon">
                                        <i class="fas fa-sticky-note"></i>
                                    </div>
                                    Notes
                                </div>
                                <div class="info-value">{{ visitor.notes|default:"No notes available" }}</div>
                            </div>

                            <!-- Callback Date -->
                            <div class="visitor-info-item">
                                <div class="info-label">
                                    <div class="info-icon">
                                        <i class="fas fa-calendar-check"></i>
                                    </div>
                                    Callback Date
                                </div>
                                <div class="info-value">{{ visitor.callback|default:"Not scheduled" }}</div>
                            </div>

                            <!-- Status -->
                            <div class="visitor-info-item">
                                <div class="info-label">
                                    <div class="info-icon">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    Status
                                </div>
                                <div class="info-value">
                                    <span class="status-badge">
                                        <i class="fas fa-circle"></i>
                                        {{ visitor.status }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="actions-container">
                            <a href="/visitors/" class="action-btn btn-primary">
                                <i class="fas fa-arrow-left"></i>
                                Back to Visitor List
                            </a>
                            <a href="/visitors/update/{{visitor.slug}}/" class="action-btn btn-warning">
                                <i class="fas fa-edit"></i>
                                Edit Visitor
                            </a>
                            <button onclick="confirmDelete()" class="action-btn btn-danger">
                                <i class="fas fa-trash-alt"></i>
                                Delete Visitor
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Visitor Details Page Functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize page animations
        initializeAnimations();

        // Removed page load success message
    });

    function confirmDelete() {
        // Modern confirmation dialog
        if (window.modernDashboard && window.modernDashboard.showConfirm) {
            window.modernDashboard.showConfirm(
                'Delete Visitor',
                'Are you sure you want to delete this visitor? This action cannot be undone.',
                'danger',
                function() {
                    // Redirect to delete URL
                    window.location.href = '/visitors/delete/{{ visitor.slug }}/';
                }
            );
        } else {
            // Fallback to native confirm
            if (confirm('Are you sure you want to delete this visitor? This action cannot be undone.')) {
                window.location.href = '/visitors/delete/{{ visitor.slug }}/';
            }
        }
    }

    function copyToClipboard(text) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text).then(function() {
                // Show success toast
                if (window.modernDashboard) {
                    window.modernDashboard.showToast(
                        'Copied!',
                        `${text} copied to clipboard.`,
                        'success'
                    );
                } else {
                    console.log('Copied to clipboard: ' + text);
                }
            }, function(err) {
                console.error('Could not copy text: ', err);
                // Fallback for older browsers
                fallbackCopyToClipboard(text);
            });
        } else {
            // Fallback for older browsers
            fallbackCopyToClipboard(text);
        }
    }

    function fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Copied!',
                    `${text} copied to clipboard.`,
                    'success'
                );
            }
        } catch (err) {
            console.error('Fallback: Could not copy text: ', err);
        }

        document.body.removeChild(textArea);
    }

    function initializeAnimations() {
        // Add staggered animation to info items
        const infoItems = document.querySelectorAll('.visitor-info-item');
        infoItems.forEach((item, index) => {
            item.style.animationDelay = `${index * 0.1}s`;
            item.classList.add('animate-in');
        });
    }

    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
        .animate-in {
            animation: fadeInUp 0.6s ease-out forwards;
            opacity: 0;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(style);
</script>
{% endblock %}