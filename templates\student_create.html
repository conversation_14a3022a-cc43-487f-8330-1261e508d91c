{% extends "base.html" %}

{% block title %}Add Student - Librainian{% endblock %}

{% block page_title %}Add Student{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="/{{ role }}/students/">Students</a></li>
<li class="breadcrumb-item active" aria-current="page">Add Student</li>
{% endblock %}

{% block extra_css %}
    <style>
        /* Full Page Registration Form - Glassmorphism Theme with Dark Mode Support */

        /* Page Layout */
        .registration-page {
            min-height: 100vh;
            padding: 2rem 0;
            background: transparent;
        }

        .registration-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .registration-card {
            background: transparent; /* No background in light mode */
            border: none; /* No border in light mode */
            border-radius: var(--border-radius-lg, 20px);
            box-shadow: none; /* No shadow in light mode */
            padding: 2.5rem;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Dark Mode Support */
        body.dark-mode .registration-card {
            background: var(--glass-bg, rgba(255, 255, 255, 0.05));
            backdrop-filter: var(--glass-blur, blur(20px));
            -webkit-backdrop-filter: var(--glass-blur, blur(20px));
            border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.1));
            box-shadow: var(--glass-shadow-lg, 0 8px 32px rgba(0, 0, 0, 0.3));
        }

        /* Form Container Adjustments */
        .registration-card {
            padding-top: 2rem; /* Reduced top padding since no header */
        }

        /* Form Groups */
        .form-group {
            margin-bottom: 2rem;
            position: relative;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-primary, #ffffff);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            font-size: 1rem;
        }

        .form-label.required::after {
            content: " *";
            color: var(--danger, #ef4444);
            margin-left: 0.25rem;
        }

        .form-label i {
            margin-right: 0.75rem;
            color: var(--primary, #6366f1);
            font-size: 1rem;
            width: 18px;
            text-align: center;
        }

        .form-control, .form-select {
            background: #ffffff !important; /* White background in light mode */
            border: 1px solid #e5e7eb !important; /* Light border in light mode */
            border-radius: var(--border-radius, 12px);
            padding: 1rem 1.25rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
            color: #111827 !important; /* Dark text in light mode */
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary, #6366f1) !important;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2) !important;
            outline: none;
            background: #ffffff !important;
            color: #111827 !important;
        }

        .form-control::placeholder {
            color: #9ca3af !important; /* Light gray placeholder in light mode */
        }

        .form-label {
            color: #111827 !important; /* Dark text in light mode */
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        /* Select dropdown options styling */
        .form-select option {
            background: #ffffff !important;
            color: #111827 !important;
        }

        /* Dark Mode Form Controls */
        body.dark-mode .form-control,
        body.dark-mode .form-select {
            background: rgba(255, 255, 255, 0.05) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: var(--text-primary, #ffffff) !important;
        }

        body.dark-mode .form-control:focus,
        body.dark-mode .form-select:focus {
            background: rgba(255, 255, 255, 0.1) !important;
            border-color: var(--primary, #6366f1) !important;
            color: var(--text-primary, #ffffff) !important;
        }

        body.dark-mode .form-control::placeholder {
            color: var(--text-muted, rgba(255, 255, 255, 0.5)) !important;
        }

        body.dark-mode .form-label {
            color: var(--text-primary, #ffffff) !important;
        }

        body.dark-mode .form-select option {
            background: #1f2937 !important;
            color: #ffffff !important;
        }

        /* Button Styles */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary, #6366f1), var(--primary-dark, #4f46e5));
            border: none;
            border-radius: var(--border-radius, 12px);
            padding: 1rem 2.5rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3);
            width: 100%;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
            background: linear-gradient(135deg, var(--primary-dark, #4f46e5), var(--primary, #6366f1));
            color: white;
        }

        .btn-secondary {
            background: #f3f4f6; /* Light gray background in light mode */
            border: 1px solid #d1d5db; /* Light border in light mode */
            border-radius: var(--border-radius, 12px);
            padding: 1rem 2.5rem;
            font-weight: 600;
            font-size: 1rem;
            color: #374151; /* Dark text in light mode */
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            background: #e5e7eb; /* Darker gray on hover in light mode */
            border-color: #9ca3af;
            color: #374151;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        /* Dark Mode Buttons */
        body.dark-mode .btn-secondary {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: var(--text-primary, #ffffff);
        }

        body.dark-mode .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
        }

        /* File Input Styling */
        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .file-input-label {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius, 12px);
            padding: 1rem 1.25rem;
            display: block;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: var(--text-primary, #ffffff);
            text-align: center;
        }

        .file-input-label:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        /* Dark Mode File Input */
        body.dark-mode .file-input-label {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
            color: var(--text-primary, #ffffff);
        }

        body.dark-mode .file-input-label:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
        }

        /* Form Actions */
        .form-actions {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .form-actions {
            border-top-color: rgba(255, 255, 255, 0.05);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .registration-page {
                padding: 1rem 0;
            }

            .registration-card {
                padding: 2rem 1.5rem;
                margin: 0 0.5rem;
                border-radius: var(--border-radius, 16px);
            }

            .form-title {
                font-size: 2rem;
            }

            .form-group {
                margin-bottom: 1.5rem;
            }

            .btn-primary,
            .btn-secondary {
                padding: 0.875rem 2rem;
            }
        }

        @media (max-width: 480px) {
            .registration-card {
                padding: 1.5rem 1rem;
                margin: 0 0.25rem;
            }

            .form-title {
                font-size: 1.75rem;
            }

            .form-control,
            .form-select,
            .file-input-label {
                padding: 0.875rem 1rem;
            }

            .btn-primary,
            .btn-secondary {
                padding: 0.75rem 1.5rem;
                font-size: 0.95rem;
            }
        }

        /* Dark/Light Mode Toggle Support */
        .mode-toggle {
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .mode-toggle:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: scale(1.05);
        }

        .mode-toggle i {
            color: var(--text-primary, #ffffff);
            font-size: 1.2rem;
        }

        body.dark-mode .mode-toggle {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .mode-toggle:hover {
            background: rgba(255, 255, 255, 0.1);
        }
    </style>
{% endblock %}

{% block content %}
<!-- Dark/Light Mode Toggle -->
<div class="mode-toggle" onclick="toggleDarkMode()" title="Toggle Dark/Light Mode">
    <i class="fas fa-moon" id="mode-icon"></i>
</div>

<div class="registration-page">
    <div class="registration-container">
        <div class="registration-card">
            <form action="" method="POST" enctype="multipart/form-data" class="student-form">
                    {% csrf_token %}

                    <!-- Security Deposit Notice -->
                    {% if librarian.security_deposit_enabled %}
                    <div class="alert alert-info mb-3" style="background: rgba(13, 202, 240, 0.1); border: 1px solid rgba(13, 202, 240, 0.3); border-radius: 12px; padding: 12px; margin-bottom: 20px;">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-info-circle" style="color: #0dcaf0; font-size: 20px;"></i>
                            <div>
                                <strong style="color: var(--text-primary);">Security Deposit Required</strong>
                                <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;">
                                    This library requires a security deposit of ₹{{ librarian.security_deposit_amount }} for all students.
                                </p>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="form-group">
                        <label for="courseName" class="form-label required">
                            <i class="fas fa-book"></i>
                            Course Name
                        </label>
                        <select class="form-select" id="courseName" name="course" required>
                            <option value="">Select a course</option>
                            {% for course in courses %}
                            <option value="{{ course.id }}">{{ course.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name" class="form-label required">
                                    <i class="fas fa-user"></i>
                                    Full Name
                                </label>
                                <input type="text" class="form-control" id="name" name="name"
                                       placeholder="Enter your full name" required pattern="[A-Za-z ]+"
                                       title="Please enter only alphabets">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="fatherName" class="form-label">
                                    <i class="fas fa-user-tie"></i>
                                    Father's Name
                                </label>
                                <input type="text" class="form-control" id="fatherName" name="fathername"
                                       placeholder="Enter your father's name" pattern="[A-Za-z ]+"
                                       title="Please enter only alphabets">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="age" class="form-label">
                                    <i class="fas fa-birthday-cake"></i>
                                    Age
                                </label>
                                <input type="number" class="form-control" id="age" name="age"
                                       placeholder="Enter your age" min="1" max="99">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="gender" class="form-label required">
                                    <i class="fas fa-venus-mars"></i>
                                    Gender
                                </label>
                                <select class="form-select" id="gender" name="gender" required>
                                    <option value="">Select Gender</option>
                                    <option value="male">Male</option>
                                    <option value="female">Female</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope"></i>
                                    Email Address
                                </label>
                                <input type="email" class="form-control" id="email" name="email"
                                       placeholder="Enter your email (optional)">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone" class="form-label required">
                                    <i class="fas fa-phone"></i>
                                    Phone Number
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       placeholder="Enter your phone number" required maxlength="10"
                                       pattern="\d{10}" title="Please enter a 10-digit phone number">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="locality" class="form-label required">
                                    <i class="fas fa-map-marker-alt"></i>
                                    Locality
                                </label>
                                <input type="text" class="form-control" id="locality" name="locality"
                                       placeholder="Enter your locality" required pattern="[A-Za-z ]+"
                                       title="Please enter only alphabets">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="city" class="form-label required">
                                    <i class="fas fa-city"></i>
                                    City
                                </label>
                                <input type="text" class="form-control" id="city" name="city"
                                       placeholder="Enter your city" required pattern="[A-Za-z ]+"
                                       title="Please enter only alphabets">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="states" class="form-label required">
                            <i class="fas fa-map"></i>
                            State
                        </label>
                        <select class="form-select" id="states" name="state" required>
                            <option value="">Select a state</option>
                            {% for state in states %}
                            <option value="{{ state.id }}">{{ state.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="rdate" class="form-label required">
                                    <i class="fas fa-calendar"></i>
                                    Registration Date
                                </label>
                                <input type="date" class="form-control" id="rdate" name="rdate" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="regfees" class="form-label required">
                                    <i class="fas fa-money-bill"></i>
                                    Registration Fee
                                </label>
                                <input type="number" class="form-control" id="regfees" name="regfees"
                                       placeholder="Enter registration fee" required>
                            </div>
                        </div>
                    </div>

                    <!-- Proof of Identity Section (conditional) -->
                    {% if librarian.proof_of_identity_enabled %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="identity_type" class="form-label">
                                    <i class="fas fa-id-card"></i>
                                    Identity Document Type
                                </label>
                                <select class="form-select" id="identity_type" name="identity_type">
                                    <option value="">Select identity type (Optional)</option>
                                    <option value="pan">PAN Card</option>
                                    <option value="aadhaar">Aadhaar Card</option>
                                    <option value="driving_license">Driving License</option>
                                    <option value="voter_id">Voter ID Card</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="identity_number" class="form-label">
                                    <i class="fas fa-hashtag"></i>
                                    Identity Document Number
                                </label>
                                <input type="text" class="form-control" id="identity_number" name="identity_number"
                                       maxlength="50" placeholder="Enter document number" onchange="validateIdentityNumber()">
                                <div class="help-text">This information will be masked for privacy protection</div>
                                <div id="identity_validation_message" style="font-size: 0.8rem; margin-top: 4px; display: none;"></div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="form-group">
                        <label for="image" class="form-label">
                            <i class="fas fa-camera"></i>
                            Student Image
                        </label>
                        <div class="file-input-wrapper">
                            <input type="file" id="image" name="image" accept="image/*">
                            <label for="image" class="file-input-label">
                                <i class="fas fa-upload me-2"></i>
                                Choose student image...
                            </label>
                        </div>
                    </div>

                <div class="form-actions">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>
                                Register Student
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-secondary" onclick="window.history.back();">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back
                            </button>
                        </div>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>

    <!-- Bootstrap 5.3.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Dark Mode Toggle Functionality
        function toggleDarkMode() {
            const body = document.body;
            const modeIcon = document.getElementById('mode-icon');

            body.classList.toggle('dark-mode');

            // Update icon
            if (body.classList.contains('dark-mode')) {
                modeIcon.className = 'fas fa-sun';
                localStorage.setItem('darkMode', 'enabled');
            } else {
                modeIcon.className = 'fas fa-moon';
                localStorage.setItem('darkMode', 'disabled');
            }
        }

        // Initialize dark mode from localStorage
        function initializeDarkMode() {
            const darkMode = localStorage.getItem('darkMode');
            const body = document.body;
            const modeIcon = document.getElementById('mode-icon');

            if (darkMode === 'enabled') {
                body.classList.add('dark-mode');
                modeIcon.className = 'fas fa-sun';
            } else {
                body.classList.remove('dark-mode');
                modeIcon.className = 'fas fa-moon';
            }
        }

        // Modern form validation and interaction
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize dark mode
            initializeDarkMode();
            const form = document.querySelector('.student-form');
            const fileInput = document.getElementById('image');
            const fileLabel = document.querySelector('.file-input-label');

            // File input handling
            if (fileInput && fileLabel) {
                fileInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    const maxSize = 20 * 1024; // 20 KB in bytes

                    if (file) {
                        if (file.size > maxSize) {
                            alert('Image size must not exceed 20 KB. Please choose a smaller file.');
                            this.value = '';
                            fileLabel.innerHTML = '<i class="fas fa-upload me-2"></i>Choose student image...';
                        } else {
                            fileLabel.innerHTML = `<i class="fas fa-check me-2"></i>${file.name}`;
                            fileLabel.style.color = 'var(--secondary)';
                        }
                    } else {
                        fileLabel.innerHTML = '<i class="fas fa-upload me-2"></i>Choose student image...';
                        fileLabel.style.color = '';
                    }
                });
            }

            // Input validation functions
            function allowOnlyAlphabets(input) {
                input.value = input.value.replace(/[^A-Za-z ]/g, '');
            }

            function allowOnlyNumbers(input, maxLength = null) {
                input.value = input.value.replace(/\D/g, '');
                if (maxLength && input.value.length > maxLength) {
                    input.value = input.value.slice(0, maxLength);
                }
            }

            // Apply validations
            const nameField = document.getElementById('name');
            const fatherNameField = document.getElementById('fatherName');
            const localityField = document.getElementById('locality');
            const cityField = document.getElementById('city');
            const phoneField = document.getElementById('phone');
            const ageField = document.getElementById('age');

            if (nameField) {
                nameField.addEventListener('input', function() {
                    allowOnlyAlphabets(this);
                });
            }

            if (fatherNameField) {
                fatherNameField.addEventListener('input', function() {
                    allowOnlyAlphabets(this);
                });
            }

            if (localityField) {
                localityField.addEventListener('input', function() {
                    allowOnlyAlphabets(this);
                });
            }

            if (cityField) {
                cityField.addEventListener('input', function() {
                    allowOnlyAlphabets(this);
                });
            }

            if (phoneField) {
                phoneField.addEventListener('input', function() {
                    allowOnlyNumbers(this, 10);
                });
            }

            if (ageField) {
                ageField.addEventListener('input', function() {
                    allowOnlyNumbers(this, 2);
                    if (parseInt(this.value) > 99) {
                        this.value = '99';
                    }
                });
            }

            // Form submission handling
            if (form) {
                form.addEventListener('submit', function(e) {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Registering...';
                        submitBtn.disabled = true;
                    }
                });
            }

            // Real-time validation feedback
            const requiredFields = form.querySelectorAll('[required]');
            requiredFields.forEach(field => {
                field.addEventListener('blur', function() {
                    if (!this.value.trim()) {
                        this.style.borderColor = 'var(--danger)';
                    } else {
                        this.style.borderColor = 'var(--secondary)';
                    }
                });

                field.addEventListener('input', function() {
                    if (this.value.trim()) {
                        this.style.borderColor = '';
                    }
                });
            });
        });

        // Identity document validation
        function validateIdentityNumber() {
            const identityType = document.getElementById('identity_type').value;
            const identityNumber = document.getElementById('identity_number').value.trim();
            const messageDiv = document.getElementById('identity_validation_message');

            if (!identityType || !identityNumber) {
                messageDiv.style.display = 'none';
                return true;
            }

            let isValid = false;
            let message = '';

            switch(identityType) {
                case 'aadhaar':
                    // Aadhaar: 12 digits
                    isValid = /^\d{12}$/.test(identityNumber);
                    message = isValid ? '✓ Valid Aadhaar format' : '✗ Aadhaar must be 12 digits';
                    break;
                case 'pan':
                    // PAN: 10 alphanumeric (5 letters, 4 digits, 1 letter)
                    isValid = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(identityNumber.toUpperCase());
                    message = isValid ? '✓ Valid PAN format' : '✗ PAN must be 10 characters (**********)';
                    break;
                case 'driving_license':
                    // Driving License: 15 alphanumeric
                    isValid = /^[A-Z0-9]{15}$/.test(identityNumber.toUpperCase());
                    message = isValid ? '✓ Valid Driving License format' : '✗ Driving License must be 15 alphanumeric characters';
                    break;
                case 'voter_id':
                    // Voter ID: 10 alphanumeric
                    isValid = /^[A-Z0-9]{10}$/.test(identityNumber.toUpperCase());
                    message = isValid ? '✓ Valid Voter ID format' : '✗ Voter ID must be 10 alphanumeric characters';
                    break;
            }

            messageDiv.textContent = message;
            messageDiv.style.color = isValid ? '#28a745' : '#dc3545';
            messageDiv.style.display = 'block';

            return isValid;
        }

        // Add event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const identityTypeSelect = document.getElementById('identity_type');
            const identityNumberInput = document.getElementById('identity_number');

            if (identityTypeSelect && identityNumberInput) {
                identityTypeSelect.addEventListener('change', validateIdentityNumber);
                identityNumberInput.addEventListener('input', validateIdentityNumber);
            }
        });
    </script>

{% endblock %}