# QR Registration Notification System Usage Guide

This document explains how to use the QR registration notification system that sends push notifications to PWA, Chrome mobile, and other browsers when someone fills a registration form using QR code.

## Overview

The notification system provides:
- **QR Registration notifications** when someone submits registration via QR code
- **Push notifications** via Firebase Cloud Messaging (FCM)
- **User preferences** and quiet hours
- **Notification history** and tracking
- **API endpoints** for programmatic access
- **Admin interface** for management

## Quick Start

### 1. Setup Default Templates
```bash
python manage.py setup_notification_templates
```

### 2. Basic Usage in Code

```python
from librarian.qr_notification_helper import send_qr_registration_notification

# Send QR registration notification (automatic via signals)
# This happens automatically when TempStudentData is created

# Or send manually
send_qr_registration_notification(temp_student)

# Test the notification system
from librarian.qr_notification_helper import test_qr_notification
test_qr_notification(user_id=1)
```

## Available Event Types

### Registration Notifications
- `qr_registration` - New student registration via QR code

## Detailed Usage Examples

### 1. Automatic Notifications (via Signals)

The system automatically sends notifications when someone submits registration via QR code:

```python
# This is triggered automatically when TempStudentData is created
temp_student = TempStudentData.objects.create(...)  # Triggers qr_registration notification
```

### 2. Manual Notifications

```python
from librarian.qr_notification_helper import send_qr_registration_notification

# Send QR registration notification manually
send_qr_registration_notification(temp_student)

# Or use the notification service directly
from librarian.notification_service import notification_service
notification_service.qr_registration_submitted(temp_student)
```

### 3. Integration in Your Registration View

```python
# In your registration form view (e.g., studentsData/views.py)
from librarian.qr_notification_helper import send_qr_registration_notification

def register_student_via_qr(request):
    if request.method == 'POST':
        # Process form data
        temp_student = TempStudentData.objects.create(
            name=request.POST.get('name'),
            email=request.POST.get('email'),
            mobile=request.POST.get('mobile'),
            course=course,
            librarian=librarian,
            # ... other fields
        )

        # Send notification (optional - already handled by signals)
        send_qr_registration_notification(temp_student)

        return redirect('success_page')
```

### 4. Testing the System

```bash
# Test the notification system
python manage.py test_notifications --list-events
python manage.py test_notifications --user-id 1 --event-type qr_registration

# Or test programmatically
from librarian.qr_notification_helper import test_qr_notification
test_qr_notification(user_id=1)
```

## API Usage

### Send Single Notification
```bash
curl -X POST /notifications/api/send/ \
  -H "Content-Type: application/json" \
  -d '{
    "event_type": "announcement",
    "recipient_id": 123,
    "context_data": {
      "announcement_title": "Test",
      "announcement_message": "This is a test"
    }
  }'
```

### Send Bulk Notification
```bash
curl -X POST /notifications/api/send-bulk/ \
  -H "Content-Type: application/json" \
  -d '{
    "event_type": "reminder",
    "recipient_ids": [123, 456, 789],
    "context_data": {
      "reminder_title": "Fee Due",
      "reminder_message": "Please pay your fees"
    }
  }'
```

### Get Notification Templates
```bash
curl /notifications/api/templates/
```

### Check Notification Status
```bash
curl /notifications/api/status/123/
```

## Template Variables

Templates support Django template syntax with these variables:

### Common Variables (available in all templates)
- `user_name` - User's first name or username
- `user_full_name` - User's full name
- `current_date` - Current date (YYYY-MM-DD)
- `current_time` - Current time (HH:MM)

### QR Registration Event Variables
- `student_name` - Name of the student who registered
- `student_email` - Email of the student
- `student_mobile` - Mobile number of the student
- `course` - Course name selected
- `library_name` - Name of the library
- `registration_date` - Date of registration
- `temp_student_id` - Temporary student ID for reference

## User Preferences

Users can control their notification preferences:

```python
from librarian.models import UserNotificationPreference

# Get or create preferences
preferences, created = UserNotificationPreference.objects.get_or_create(user=user)

# Update preferences
preferences.push_notifications = True
preferences.email_notifications = False
preferences.quiet_hours_start = '22:00'
preferences.quiet_hours_end = '08:00'
preferences.save()

# Set notification categories
from librarian.models import NotificationCategory
financial_category = NotificationCategory.objects.get(name='Financial')
preferences.categories.add(financial_category)
```

## Admin Interface

### Bulk Notifications
1. Go to Django Admin
2. Navigate to "Send Bulk Notification"
3. Select recipient group
4. Enter title and message
5. Send notification

### View Statistics
1. Go to Django Admin
2. Navigate to "Notification Statistics"
3. View delivery rates, popular categories, etc.

### Manage Templates
1. Go to Django Admin
2. Navigate to "Notification Templates"
3. Create/edit templates with variables

## Monitoring and Debugging

### Check Notification History
```python
from librarian.models import NotificationHistory

# Recent notifications
recent = NotificationHistory.objects.order_by('-sent_at')[:10]

# Failed notifications
failed = NotificationHistory.objects.filter(status='failed')

# Notifications for specific user
user_notifications = NotificationHistory.objects.filter(recipient=user)
```

### Debug Failed Notifications
```python
failed_notifications = NotificationHistory.objects.filter(status='failed')
for notification in failed_notifications:
    print(f"Failed: {notification.title} - {notification.error_message}")
```

## Best Practices

1. **Use appropriate event types** - Choose the most specific event type
2. **Respect user preferences** - Don't use `force_send=True` unless necessary
3. **Provide meaningful content** - Use clear titles and actionable messages
4. **Include action URLs** - Help users take next steps
5. **Monitor delivery rates** - Check admin statistics regularly
6. **Test thoroughly** - Use dry-run mode for bulk operations
7. **Handle errors gracefully** - Check return values and log errors

## Troubleshooting

### Common Issues

1. **No notifications sent**
   - Check if user has device tokens registered
   - Verify Firebase configuration
   - Check user notification preferences

2. **Template not found**
   - Run `python manage.py setup_notification_templates`
   - Check if template is active

3. **Permission denied**
   - Ensure user has proper permissions for API endpoints
   - Check if user is staff or librarian for admin functions

4. **Firebase errors**
   - Verify Firebase credentials
   - Check if FCM service is properly initialized
   - Validate device tokens

### Logs
Check Django logs for notification-related errors:
```bash
tail -f logs/django.log | grep notification
```
