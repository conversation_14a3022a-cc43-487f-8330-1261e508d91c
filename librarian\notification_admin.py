"""
Admin interface for notification management
"""

from django.contrib import admin
from django.contrib.auth.models import User
from django.shortcuts import render, redirect
from django.contrib import messages
from django.urls import path
from django.http import HttpResponseRedirect
from .models import NotificationHistory, NotificationTemplate, NotificationCategory
from .notification_signals import send_bulk_announcement, send_bulk_reminder
from .notification_service import notification_service


class NotificationAdminMixin:
    """Mixin to add notification functionality to admin"""
    
    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('send-bulk-notification/', self.admin_site.admin_view(self.send_bulk_notification_view), name='send_bulk_notification'),
            path('notification-stats/', self.admin_site.admin_view(self.notification_stats_view), name='notification_stats'),
        ]
        return custom_urls + urls
    
    def send_bulk_notification_view(self, request):
        """View for sending bulk notifications"""
        if request.method == 'POST':
            notification_type = request.POST.get('notification_type')
            title = request.POST.get('title')
            message = request.POST.get('message')
            action_url = request.POST.get('action_url', '')
            recipient_type = request.POST.get('recipient_type')
            
            # Get recipients based on type
            if recipient_type == 'all_users':
                recipients = User.objects.filter(is_active=True)
            elif recipient_type == 'librarians':
                recipients = User.objects.filter(
                    librarian_param__isnull=False,
                    is_active=True
                ).distinct()
            elif recipient_type == 'students':
                recipients = User.objects.filter(
                    studentdata__isnull=False,
                    is_active=True
                ).distinct()
            else:
                recipients = []
            
            if recipients and title and message:
                try:
                    if notification_type == 'announcement':
                        results = send_bulk_announcement(list(recipients), title, message, action_url)
                    else:
                        results = send_bulk_reminder(list(recipients), title, message, action_url)
                    
                    messages.success(request, f'Successfully sent {len(results)} notifications!')
                except Exception as e:
                    messages.error(request, f'Error sending notifications: {str(e)}')
            else:
                messages.error(request, 'Please fill in all required fields and select recipients.')
            
            return HttpResponseRedirect(request.path)
        
        context = {
            'title': 'Send Bulk Notification',
            'notification_templates': NotificationTemplate.objects.filter(
                event_type__in=['announcement', 'reminder'],
                is_active=True
            ),
            'total_users': User.objects.filter(is_active=True).count(),
            'total_librarians': User.objects.filter(
                librarian_param__isnull=False,
                is_active=True
            ).distinct().count(),
            'total_students': User.objects.filter(
                studentdata__isnull=False,
                is_active=True
            ).distinct().count(),
        }
        
        return render(request, 'admin/notification_bulk_send.html', context)
    
    def notification_stats_view(self, request):
        """View for notification statistics"""
        from django.db.models import Count, Q
        from django.utils import timezone
        from datetime import timedelta
        
        # Get stats for the last 30 days
        thirty_days_ago = timezone.now() - timedelta(days=30)
        
        stats = {
            'total_sent': NotificationHistory.objects.count(),
            'sent_last_30_days': NotificationHistory.objects.filter(
                sent_at__gte=thirty_days_ago
            ).count(),
            'successful_deliveries': NotificationHistory.objects.filter(
                status='delivered'
            ).count(),
            'failed_deliveries': NotificationHistory.objects.filter(
                status='failed'
            ).count(),
            'clicked_notifications': NotificationHistory.objects.filter(
                status='clicked'
            ).count(),
        }
        
        # Notifications by category
        category_stats = NotificationHistory.objects.values(
            'template__category__name'
        ).annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Notifications by event type
        event_stats = NotificationHistory.objects.values(
            'template__event_type'
        ).annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Recent notifications
        recent_notifications = NotificationHistory.objects.select_related(
            'template', 'recipient'
        ).order_by('-sent_at')[:20]
        
        context = {
            'title': 'Notification Statistics',
            'stats': stats,
            'category_stats': category_stats,
            'event_stats': event_stats,
            'recent_notifications': recent_notifications,
        }
        
        return render(request, 'admin/notification_stats.html', context)


# Apply the mixin to the admin site
class CustomAdminSite(admin.AdminSite, NotificationAdminMixin):
    site_header = 'LMS Administration'
    site_title = 'LMS Admin'
    index_title = 'Welcome to LMS Administration'


# Custom admin actions
@admin.action(description='Send test notification to selected users')
def send_test_notification(modeladmin, request, queryset):
    """Send test notification to selected users"""
    count = 0
    for user in queryset:
        try:
            notification_service.send_notification(
                'welcome',
                user,
                library_name='Test Library'
            )
            count += 1
        except Exception as e:
            messages.error(request, f'Failed to send notification to {user.username}: {str(e)}')
    
    if count > 0:
        messages.success(request, f'Successfully sent test notifications to {count} users.')


@admin.action(description='Send feedback request to selected users')
def send_feedback_request(modeladmin, request, queryset):
    """Send feedback request to selected users"""
    count = 0
    for user in queryset:
        try:
            notification_service.feedback_request(user)
            count += 1
        except Exception as e:
            messages.error(request, f'Failed to send feedback request to {user.username}: {str(e)}')
    
    if count > 0:
        messages.success(request, f'Successfully sent feedback requests to {count} users.')


# Add custom actions to User admin
class UserAdminWithNotifications(admin.ModelAdmin):
    actions = [send_test_notification, send_feedback_request]
    list_display = ['username', 'email', 'first_name', 'last_name', 'is_active', 'date_joined']
    list_filter = ['is_active', 'is_staff', 'date_joined']
    search_fields = ['username', 'email', 'first_name', 'last_name']


# Unregister the default User admin and register our custom one
try:
    admin.site.unregister(User)
except admin.sites.NotRegistered:
    pass

admin.site.register(User, UserAdminWithNotifications)
