<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase FCM Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>🔧 Firebase FCM Debug Tool</h1>
    
    <div class="debug-section">
        <h2>1. Firebase App Initialization</h2>
        <div id="firebase-status" class="status info">Checking Firebase initialization...</div>
        <button class="btn-primary" onclick="testFirebaseInit()">Test Firebase Init</button>
    </div>
    
    <div class="debug-section">
        <h2>2. VAPID Key Configuration</h2>
        <div id="vapid-status" class="status info">Checking VAPID key...</div>
        <button class="btn-primary" onclick="testVapidKey()">Test VAPID Key</button>
    </div>
    
    <div class="debug-section">
        <h2>3. Service Worker Registration</h2>
        <div id="sw-status" class="status info">Checking service worker...</div>
        <button class="btn-primary" onclick="testServiceWorker()">Test Service Worker</button>
    </div>
    
    <div class="debug-section">
        <h2>4. Notification Permissions</h2>
        <div id="permission-status" class="status info">Checking permissions...</div>
        <button class="btn-warning" onclick="requestPermissions()">Request Permissions</button>
    </div>
    
    <div class="debug-section">
        <h2>5. FCM Token Generation</h2>
        <div id="token-status" class="status info">Ready to generate token...</div>
        <button class="btn-success" onclick="generateToken()">Generate FCM Token</button>
        <div id="token-display" class="code" style="display:none; margin-top:10px;"></div>
    </div>
    
    <div class="debug-section">
        <h2>6. Token Registration with Server</h2>
        <div id="server-status" class="status info">Ready to register token...</div>
        <button class="btn-success" onclick="registerWithServer()" disabled id="registerBtn">Register with Server</button>
    </div>
    
    <div class="debug-section">
        <h2>🔍 Debug Console</h2>
        <div id="debug-console" class="code" style="height: 200px; overflow-y: auto; background: #000; color: #0f0; padding: 10px;"></div>
        <button class="btn-danger" onclick="clearConsole()">Clear Console</button>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-messaging.js"></script>

    <script>
        // Debug console
        function log(message, type = 'info') {
            const console = document.getElementById('debug-console');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#0ff',
                success: '#0f0',
                error: '#f00',
                warning: '#ff0'
            };
            console.innerHTML += `<div style="color: ${colors[type]}">[${timestamp}] ${message}</div>`;
            console.scrollTop = console.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('debug-console').innerHTML = '';
        }
        
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
        }

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
            authDomain: "librainian-app.firebaseapp.com",
            projectId: "librainian-app",
            storageBucket: "librainian-app.firebasestorage.app",
            messagingSenderId: "623132670328",
            appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
            measurementId: "G-XNDKJL6JWH"
        };

        const vapidKey = "BFm8KEWYXyt703OsjQ4338IbyV72W3m6nndMoZhzRV9SlSj0UHMv4INixoql0AJLWh6LJKC1CrP3r_M8YqsGrAY";

        let app, messaging, currentToken;

        // 1. Test Firebase Initialization
        function testFirebaseInit() {
            log('Testing Firebase initialization...', 'info');
            try {
                app = firebase.initializeApp(firebaseConfig);
                messaging = firebase.messaging();
                
                log('✅ Firebase app initialized successfully', 'success');
                log(`📱 Project ID: ${app.options.projectId}`, 'info');
                log(`🔑 API Key: ${app.options.apiKey.substring(0, 20)}...`, 'info');
                
                updateStatus('firebase-status', '✅ Firebase initialized successfully', 'success');
                return true;
            } catch (error) {
                log(`❌ Firebase initialization failed: ${error.message}`, 'error');
                updateStatus('firebase-status', `❌ Firebase initialization failed: ${error.message}`, 'error');
                return false;
            }
        }

        // 2. Test VAPID Key
        function testVapidKey() {
            log('Testing VAPID key configuration...', 'info');
            
            if (!vapidKey) {
                log('❌ VAPID key not found', 'error');
                updateStatus('vapid-status', '❌ VAPID key not configured', 'error');
                return false;
            }
            
            log(`✅ VAPID key found: ${vapidKey.substring(0, 20)}...`, 'success');
            log(`📏 VAPID key length: ${vapidKey.length} characters`, 'info');
            
            // Validate VAPID key format (should be base64url)
            const vapidRegex = /^[A-Za-z0-9_-]{87}$/;
            if (vapidRegex.test(vapidKey)) {
                log('✅ VAPID key format is valid', 'success');
                updateStatus('vapid-status', '✅ VAPID key configured and valid', 'success');
                return true;
            } else {
                log('⚠️ VAPID key format may be invalid', 'warning');
                updateStatus('vapid-status', '⚠️ VAPID key format may be invalid', 'warning');
                return false;
            }
        }

        // 3. Test Service Worker
        function testServiceWorker() {
            log('Testing service worker registration...', 'info');
            
            if (!('serviceWorker' in navigator)) {
                log('❌ Service Worker not supported', 'error');
                updateStatus('sw-status', '❌ Service Worker not supported', 'error');
                return false;
            }
            
            navigator.serviceWorker.register('/firebase-messaging-sw.js')
                .then(function(registration) {
                    log('✅ Service Worker registered successfully', 'success');
                    log(`📍 Scope: ${registration.scope}`, 'info');
                    log(`🔄 State: ${registration.installing ? 'installing' : registration.waiting ? 'waiting' : registration.active ? 'active' : 'unknown'}`, 'info');
                    
                    updateStatus('sw-status', '✅ Service Worker registered successfully', 'success');
                    
                    // Link service worker to messaging
                    if (messaging) {
                        messaging.useServiceWorker(registration);
                        log('🔗 Service Worker linked to Firebase messaging', 'success');
                    }
                    
                    return true;
                })
                .catch(function(error) {
                    log(`❌ Service Worker registration failed: ${error.message}`, 'error');
                    updateStatus('sw-status', `❌ Service Worker registration failed: ${error.message}`, 'error');
                    return false;
                });
        }

        // 4. Test Notification Permissions
        function requestPermissions() {
            log('Requesting notification permissions...', 'info');
            
            if (!('Notification' in window)) {
                log('❌ Notifications not supported', 'error');
                updateStatus('permission-status', '❌ Notifications not supported', 'error');
                return false;
            }
            
            log(`📋 Current permission: ${Notification.permission}`, 'info');
            
            if (Notification.permission === 'granted') {
                log('✅ Permissions already granted', 'success');
                updateStatus('permission-status', '✅ Notification permissions granted', 'success');
                return true;
            }
            
            Notification.requestPermission().then(function(permission) {
                log(`📋 Permission result: ${permission}`, 'info');
                
                if (permission === 'granted') {
                    log('✅ Permissions granted successfully', 'success');
                    updateStatus('permission-status', '✅ Notification permissions granted', 'success');
                    return true;
                } else {
                    log('❌ Permissions denied', 'error');
                    updateStatus('permission-status', '❌ Notification permissions denied', 'error');
                    return false;
                }
            }).catch(function(error) {
                log(`❌ Permission request failed: ${error.message}`, 'error');
                updateStatus('permission-status', `❌ Permission request failed: ${error.message}`, 'error');
                return false;
            });
        }

        // 5. Generate FCM Token
        function generateToken() {
            log('Generating FCM token...', 'info');
            
            if (!messaging) {
                log('❌ Firebase messaging not initialized', 'error');
                updateStatus('token-status', '❌ Firebase messaging not initialized', 'error');
                return;
            }
            
            if (Notification.permission !== 'granted') {
                log('❌ Notification permissions not granted', 'error');
                updateStatus('token-status', '❌ Notification permissions required', 'error');
                return;
            }
            
            messaging.getToken({ vapidKey: vapidKey }).then((token) => {
                if (token) {
                    currentToken = token;
                    log('✅ FCM token generated successfully', 'success');
                    log(`🎫 Token: ${token}`, 'info');
                    
                    document.getElementById('token-display').style.display = 'block';
                    document.getElementById('token-display').textContent = token;
                    document.getElementById('registerBtn').disabled = false;
                    
                    updateStatus('token-status', '✅ FCM token generated successfully', 'success');
                } else {
                    log('❌ No FCM token available', 'error');
                    updateStatus('token-status', '❌ No FCM token available', 'error');
                }
            }).catch((error) => {
                log(`❌ FCM token generation failed: ${error.message}`, 'error');
                updateStatus('token-status', `❌ FCM token generation failed: ${error.message}`, 'error');
            });
        }

        // 6. Register with Server
        function registerWithServer() {
            if (!currentToken) {
                log('❌ No token to register', 'error');
                return;
            }
            
            log('Registering token with server...', 'info');
            
            fetch('/librarian/save-device-token/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: `token=${encodeURIComponent(currentToken)}&device_type=web&device_name=Debug Browser`
            })
            .then(response => {
                log(`📡 Server response: ${response.status}`, 'info');
                return response.json();
            })
            .then(data => {
                log(`✅ Server response: ${JSON.stringify(data)}`, 'success');
                updateStatus('server-status', '✅ Token registered with server successfully', 'success');
            })
            .catch(error => {
                log(`❌ Server registration failed: ${error.message}`, 'error');
                updateStatus('server-status', `❌ Server registration failed: ${error.message}`, 'error');
            });
        }

        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Auto-run initial tests
        window.addEventListener('load', function() {
            log('🚀 Firebase FCM Debug Tool loaded', 'info');
            log('Click buttons above to run individual tests', 'info');
            
            // Auto-check current status
            setTimeout(() => {
                log('📊 Current browser status:', 'info');
                log(`- Notification support: ${'Notification' in window ? '✅' : '❌'}`, 'info');
                log(`- Service Worker support: ${'serviceWorker' in navigator ? '✅' : '❌'}`, 'info');
                log(`- Current permission: ${Notification.permission}`, 'info');
            }, 500);
        });
    </script>
</body>
</html>
