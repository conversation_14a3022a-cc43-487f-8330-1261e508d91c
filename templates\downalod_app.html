{% extends "base.html" %}

{% block title %}Download App - Librainian{% endblock %}

{% block page_title %}Download App{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Download App</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Download App Styles */
    .download-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    .download-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 3rem 2rem;
        color: white;
        margin-bottom: 3rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .download-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .download-icon {
        width: 100px;
        height: 100px;
        margin: 0 auto 2rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 3px solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 1;
        animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .download-icon i {
        font-size: 3rem;
        color: white;
    }

    .download-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        position: relative;
        z-index: 1;
    }

    .download-header p {
        font-size: 1.25rem;
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
        line-height: 1.6;
    }

    .app-features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(99, 102, 241, 0.05) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .feature-card:hover::before {
        opacity: 1;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        margin: 0 auto 1.5rem;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        position: relative;
        z-index: 1;
    }

    .feature-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 1rem;
        position: relative;
        z-index: 1;
    }

    .feature-description {
        color: #6b7280;
        line-height: 1.6;
        position: relative;
        z-index: 1;
    }

    .download-section {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border-radius: 20px;
        padding: 3rem 2rem;
        text-align: center;
        color: white;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .download-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .download-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        position: relative;
        z-index: 1;
    }

    .download-subtitle {
        font-size: 1.125rem;
        opacity: 0.9;
        margin-bottom: 2rem;
        position: relative;
        z-index: 1;
    }

    .version-info {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 1;
    }

    .version-number {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .version-date {
        font-size: 0.875rem;
        opacity: 0.8;
    }

    .download-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 1rem 2rem;
        color: white;
        font-weight: 700;
        font-size: 1.25rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        position: relative;
        z-index: 1;
    }

    .download-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .download-btn i {
        font-size: 1.5rem;
    }

    .app-info {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 2rem;
        margin-top: 2rem;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }

    .info-item {
        text-align: center;
    }

    .info-label {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .info-value {
        font-size: 1.125rem;
        color: #1f2937;
        font-weight: 700;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .download-container {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .download-header {
            padding: 2rem 1rem;
        }

        .download-header h1 {
            font-size: 2rem;
        }

        .app-features {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .feature-card {
            padding: 1.5rem;
        }

        .download-section {
            padding: 2rem 1rem;
        }

        .download-title {
            font-size: 1.5rem;
        }
    }

    /* Animation */
    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="download-container fade-in">
    <!-- Download Header -->
    <div class="download-header">
        <div class="download-icon">
            <i class="fas fa-mobile-alt"></i>
        </div>
        <h1>Download Librainian App</h1>
        <p>Increase your library's productivity with our powerful, easy-to-use mobile solution</p>
    </div>

    <!-- App Features -->
    <div class="app-features">
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-tachometer-alt"></i>
            </div>
            <div class="feature-title">Enhanced Performance</div>
            <div class="feature-description">Lightning-fast library management with optimized performance and smooth user experience.</div>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="feature-title">Secure & Reliable</div>
            <div class="feature-description">Bank-level security with encrypted data transmission and reliable backup systems.</div>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <div class="feature-title">Mobile Optimized</div>
            <div class="feature-description">Fully responsive design that works perfectly on all devices and screen sizes.</div>
        </div>
    </div>

    <!-- Download Section -->
    <div class="download-section">
        <div class="download-title">Latest Version Available</div>
        <div class="download-subtitle">Get the most recent version with all the latest features and improvements</div>

        <div class="version-info">
            <div class="version-number">Version 2.34.11</div>
            <div class="version-date">Released: {{ current_date|default:"January 2024" }}</div>
        </div>

        <a href="https://drive.google.com/uc?export=download&id=1ynXkKNj36T8QH8BieKXkT6oSOKKcKZy9"
           class="download-btn"
           onclick="trackDownload()">
            <i class="fas fa-download"></i>
            Download Now
        </a>
    </div>

    <!-- App Information -->
    <div class="app-info">
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">File Size</div>
                <div class="info-value">~25 MB</div>
            </div>
            <div class="info-item">
                <div class="info-label">Platform</div>
                <div class="info-value">Android</div>
            </div>
            <div class="info-item">
                <div class="info-label">Requirements</div>
                <div class="info-value">Android 5.0+</div>
            </div>
            <div class="info-item">
                <div class="info-label">Last Updated</div>
                <div class="info-value">{{ current_date|default:"Jan 2024" }}</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Download tracking and enhancement
    function trackDownload() {
        // Track download event
        if (typeof gtag !== 'undefined') {
            gtag('event', 'download', {
                'event_category': 'app',
                'event_label': 'librainian_app_v2.34.11'
            });
        }

        // Show download started notification
        showNotification('Download started! Check your downloads folder.', 'success');

        // Update download count (if needed)
        updateDownloadCount();
    }

    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="fas fa-download me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    function updateDownloadCount() {
        // Optional: Send download count to server
        fetch('/api/track-download/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
            },
            body: JSON.stringify({
                'app': 'librainian',
                'version': '2.34.11',
                'timestamp': new Date().toISOString()
            })
        }).catch(err => console.log('Download tracking failed:', err));
    }

    // Add smooth scrolling for any anchor links
    document.addEventListener('DOMContentLoaded', function() {
        // Add download button animation on page load
        const downloadBtn = document.querySelector('.download-btn');
        if (downloadBtn) {
            setTimeout(() => {
                downloadBtn.style.animation = 'pulse 2s ease-in-out infinite';
            }, 1000);
        }
    });
</script>
{% endblock %}
