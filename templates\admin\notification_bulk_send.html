{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="module">
    <h1>{{ title }}</h1>
    
    <div class="form-row">
        <div class="field-box">
            <h3>Recipient Statistics</h3>
            <ul>
                <li><strong>Total Active Users:</strong> {{ total_users }}</li>
                <li><strong>Total Librarians:</strong> {{ total_librarians }}</li>
                <li><strong>Total Students:</strong> {{ total_students }}</li>
            </ul>
        </div>
    </div>
    
    <form method="post" class="module aligned">
        {% csrf_token %}
        
        <div class="form-row">
            <div class="field-box">
                <label for="notification_type" class="required">Notification Type:</label>
                <select name="notification_type" id="notification_type" required>
                    <option value="announcement">Announcement</option>
                    <option value="reminder">Reminder</option>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="field-box">
                <label for="recipient_type" class="required">Recipients:</label>
                <select name="recipient_type" id="recipient_type" required>
                    <option value="">Select recipient group</option>
                    <option value="all_users">All Active Users ({{ total_users }})</option>
                    <option value="librarians">All Librarians ({{ total_librarians }})</option>
                    <option value="students">All Students ({{ total_students }})</option>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="field-box">
                <label for="title" class="required">Title:</label>
                <input type="text" name="title" id="title" maxlength="200" required>
                <p class="help">Maximum 200 characters</p>
            </div>
        </div>
        
        <div class="form-row">
            <div class="field-box">
                <label for="message" class="required">Message:</label>
                <textarea name="message" id="message" rows="5" cols="40" required></textarea>
                <p class="help">The main content of your notification</p>
            </div>
        </div>
        
        <div class="form-row">
            <div class="field-box">
                <label for="action_url">Action URL (optional):</label>
                <input type="url" name="action_url" id="action_url" maxlength="200">
                <p class="help">URL to open when notification is clicked (optional)</p>
            </div>
        </div>
        
        <div class="submit-row">
            <input type="submit" value="Send Notification" class="default" onclick="return confirm('Are you sure you want to send this notification to the selected recipients?');">
        </div>
    </form>
    
    {% if notification_templates %}
    <div class="module">
        <h2>Available Templates</h2>
        <p>You can use these as reference for your notifications:</p>
        <ul>
        {% for template in notification_templates %}
            <li>
                <strong>{{ template.get_event_type_display }}:</strong>
                {{ template.title_template }} - {{ template.body_template|truncatewords:20 }}
            </li>
        {% endfor %}
        </ul>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const recipientSelect = document.getElementById('recipient_type');
    const form = document.querySelector('form');
    
    recipientSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const count = selectedOption.text.match(/\((\d+)\)/);
            if (count && count[1] === '0') {
                alert('No recipients found for the selected group.');
            }
        }
    });
    
    form.addEventListener('submit', function(e) {
        const recipientType = recipientSelect.value;
        const title = document.getElementById('title').value;
        const message = document.getElementById('message').value;
        
        if (!recipientType || !title || !message) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }
        
        const selectedOption = recipientSelect.options[recipientSelect.selectedIndex];
        const count = selectedOption.text.match(/\((\d+)\)/);
        if (count && parseInt(count[1]) > 100) {
            if (!confirm(`You are about to send notifications to ${count[1]} recipients. This may take some time. Continue?`)) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>

<style>
.field-box {
    margin-bottom: 20px;
}

.field-box label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}

.field-box input, .field-box select, .field-box textarea {
    width: 100%;
    max-width: 500px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.field-box .help {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.submit-row {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.submit-row input {
    background-color: #417690;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.submit-row input:hover {
    background-color: #205067;
}
</style>
{% endblock %}
