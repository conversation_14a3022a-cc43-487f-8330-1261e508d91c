from django.db import models
from django.utils import timezone
from django.utils.text import slugify
from django.core.validators import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MaxValueValidator
from librarian.models import Librarian_param
from subLibrarian.models import Sublibrarian_param
from datetime import datetime
from django.core.exceptions import ValidationError
import random
import string


class Timing(models.Model):
    start_time = models.TimeField()
    end_time = models.TimeField()

    def __str__(self):
        return f"{self.start_time} - {self.end_time}"


class Shift(models.Model):
    librarian = models.ForeignKey(
        Librarian_param, on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=225)
    time_range = models.Char<PERSON>ield(max_length=225, blank=True, null=True, help_text="Legacy field - use start_time and end_time instead")
    start_time = models.TimeField(null=True, blank=True, help_text="Shift start time (HH:MM format)")
    end_time = models.TimeField(null=True, blank=True, help_text="Shift end time (HH:MM format)")
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, help_text="Base monthly rate - other rates calculated automatically")

    def __str__(self):
        return self.name

    @property
    def formatted_time_range(self):
        """Return formatted time range from start_time and end_time"""
        if self.start_time and self.end_time:
            start_str = self.start_time.strftime('%H:%M')
            end_str = self.end_time.strftime('%H:%M')

            if self.is_overnight_shift():
                return f"{start_str} - {end_str} (+1 day)"
            else:
                return f"{start_str} - {end_str}"
        return self.time_range or "Not specified"

    def save(self, *args, **kwargs):
        # Auto-generate time_range from start_time and end_time for backward compatibility
        if self.start_time and self.end_time:
            self.time_range = self.formatted_time_range
        super().save(*args, **kwargs)

    def get_time_duration_minutes(self):
        """Get shift duration in minutes"""
        if self.start_time and self.end_time:
            from datetime import datetime, timedelta
            start_dt = datetime.combine(datetime.today(), self.start_time)
            end_dt = datetime.combine(datetime.today(), self.end_time)

            # Handle overnight shifts (end time is next day)
            if end_dt <= start_dt:
                end_dt += timedelta(days=1)

            duration = end_dt - start_dt
            return int(duration.total_seconds() / 60)
        return 0

    def is_overnight_shift(self):
        """Check if this is an overnight shift (crosses midnight)"""
        if self.start_time and self.end_time:
            return self.end_time <= self.start_time
        return False

    def get_duration_hours(self):
        """Get shift duration in hours (decimal)"""
        return self.get_time_duration_minutes() / 60.0

    def overlaps_with(self, other_shift):
        """Check if this shift overlaps with another shift (handles overnight shifts)"""
        if not all([self.start_time, self.end_time, other_shift.start_time, other_shift.end_time]):
            return False

        from datetime import datetime, timedelta

        # Convert times to datetime objects for comparison
        today = datetime.today()
        tomorrow = today + timedelta(days=1)

        # Handle this shift
        self_start = datetime.combine(today, self.start_time)
        if self.is_overnight_shift():
            self_end = datetime.combine(tomorrow, self.end_time)
        else:
            self_end = datetime.combine(today, self.end_time)

        # Handle other shift
        other_start = datetime.combine(today, other_shift.start_time)
        if other_shift.is_overnight_shift():
            other_end = datetime.combine(tomorrow, other_shift.end_time)
        else:
            other_end = datetime.combine(today, other_shift.end_time)

        # For overnight shifts, we need to check both today and tomorrow scenarios
        if self.is_overnight_shift() or other_shift.is_overnight_shift():
            # Check overlap in 48-hour window to handle all overnight scenarios
            return not (self_end <= other_start or other_end <= self_start)

        # Regular same-day shifts
        return not (self_end <= other_start or other_end <= self_start)

    def is_adjacent_to(self, other_shift, tolerance_minutes=2):
        """Check if this shift is adjacent (touching) to another shift with optional tolerance"""
        if not all([self.start_time, self.end_time, other_shift.start_time, other_shift.end_time]):
            return False

        from datetime import datetime, timedelta

        # Convert times to datetime objects for comparison
        today = datetime.today()

        self_start = datetime.combine(today, self.start_time)
        self_end = datetime.combine(today, self.end_time)
        other_start = datetime.combine(today, other_shift.start_time)
        other_end = datetime.combine(today, other_shift.end_time)

        # Check if shifts are exactly adjacent
        if (self.end_time == other_shift.start_time or
            other_shift.end_time == self.start_time):
            return True

        # Check if shifts are within tolerance (small gap)
        tolerance = timedelta(minutes=tolerance_minutes)

        # Check if this shift ends close to when other starts
        if abs(self_end - other_start) <= tolerance:
            return True

        # Check if other shift ends close to when this starts
        if abs(other_end - self_start) <= tolerance:
            return True

        return False

    def can_merge_with(self, other_shift):
        """Check if this shift can be merged with another shift"""
        return self.overlaps_with(other_shift) or self.is_adjacent_to(other_shift)

    @classmethod
    def get_available_time_slots(cls, librarian, exclude_shift_id=None):
        """Get available time slots for a librarian"""
        from datetime import time, datetime, timedelta

        # Get all existing shifts for the librarian
        existing_shifts = cls.objects.filter(librarian=librarian)
        if exclude_shift_id:
            existing_shifts = existing_shifts.exclude(id=exclude_shift_id)

        # Create a list of occupied time ranges
        occupied_ranges = []
        for shift in existing_shifts:
            if shift.start_time and shift.end_time:
                occupied_ranges.append((shift.start_time, shift.end_time))

        # Sort by start time
        occupied_ranges.sort(key=lambda x: x[0])

        # Find available slots
        available_slots = []
        current_time = time(0, 0)  # Start at midnight

        for start_time, end_time in occupied_ranges:
            if current_time < start_time:
                available_slots.append((current_time, start_time))
            current_time = max(current_time, end_time)

        # Add remaining time until midnight
        if current_time < time(23, 59):
            available_slots.append((current_time, time(23, 59)))

        return available_slots

    @classmethod
    def find_conflicting_shifts(cls, librarian, start_time, end_time, exclude_shift_id=None):
        """Find shifts that conflict with the given time range"""
        if not start_time or not end_time:
            return []

        existing_shifts = cls.objects.filter(librarian=librarian)
        if exclude_shift_id:
            existing_shifts = existing_shifts.exclude(id=exclude_shift_id)

        conflicting_shifts = []
        temp_shift = cls(start_time=start_time, end_time=end_time)

        for shift in existing_shifts:
            if temp_shift.overlaps_with(shift) or temp_shift.is_adjacent_to(shift):
                conflicting_shifts.append(shift)

        return conflicting_shifts

    @classmethod
    def merge_shifts(cls, shift_ids, new_name=None, new_price=None, preserve_seats=True):
        """Merge multiple shifts into one with optional seat preservation"""
        if len(shift_ids) < 2:
            raise ValueError("At least 2 shifts are required for merging")

        shifts = cls.objects.filter(id__in=shift_ids).order_by('start_time')
        if not shifts.exists():
            raise ValueError("No shifts found with the given IDs")

        # Verify all shifts belong to the same librarian
        librarian = shifts.first().librarian
        if not all(shift.librarian == librarian for shift in shifts):
            raise ValueError("All shifts must belong to the same librarian")

        # Verify shifts can be merged (are adjacent or overlapping)
        shifts_list = list(shifts)
        for i in range(len(shifts_list) - 1):
            if not shifts_list[i].can_merge_with(shifts_list[i + 1]):
                raise ValueError(f"Shifts '{shifts_list[i].name}' and '{shifts_list[i + 1].name}' cannot be merged")

        # Calculate merged time range
        earliest_start = min(shift.start_time for shift in shifts)
        latest_end = max(shift.end_time for shift in shifts)

        # Calculate merged price (average or sum - you can customize this logic)
        if new_price is None:
            total_duration = 0
            total_weighted_price = 0
            for shift in shifts:
                duration = shift.get_time_duration_minutes()
                total_duration += duration
                total_weighted_price += float(shift.price) * duration

            new_price = total_weighted_price / total_duration if total_duration > 0 else 0

        # Generate merged name if not provided
        if new_name is None:
            shift_names = [shift.name for shift in shifts]
            new_name = f"Merged: {' + '.join(shift_names)}"

        # Create the merged shift
        merged_shift = cls.objects.create(
            librarian=librarian,
            name=new_name,
            start_time=earliest_start,
            end_time=latest_end,
            price=new_price
        )

        # Handle seats if preservation is enabled
        if preserve_seats:
            from .models import Seat, Booking

            # Collect all seats from shifts to be merged
            all_seats = []
            for shift in shifts:
                shift_seats = list(shift.seats.all())
                all_seats.extend(shift_seats)

            # Group seats by seat_number to handle duplicates
            seat_groups = {}
            for seat in all_seats:
                if seat.seat_number not in seat_groups:
                    seat_groups[seat.seat_number] = []
                seat_groups[seat.seat_number].append(seat)

            # Transfer seats to merged shift
            for seat_number, seats in seat_groups.items():
                # Use the first seat as template, but check availability across all
                template_seat = seats[0]
                is_available = all(seat.is_available for seat in seats)

                # Create new seat for merged shift
                new_seat = Seat.objects.create(
                    librarian=librarian,
                    shift=merged_shift,
                    seat_number=seat_number,
                    is_available=is_available
                )

                # Transfer bookings from occupied seats
                if not is_available:
                    for seat in seats:
                        if not seat.is_available:
                            # Transfer bookings to new seat
                            bookings = seat.booking_set.all()
                            for booking in bookings:
                                booking.seat = new_seat
                                booking.save()
                            break  # Only need one booking per seat number

        # Delete the original shifts (this will cascade delete old seats)
        shifts.delete()

        return merged_shift

    def get_merge_suggestions(self):
        """Get suggestions for shifts that can be merged with this one"""
        if not self.librarian:
            return []

        other_shifts = Shift.objects.filter(librarian=self.librarian).exclude(id=self.id)
        mergeable_shifts = []

        for shift in other_shifts:
            if self.can_merge_with(shift):
                mergeable_shifts.append(shift)

        return mergeable_shifts

    def get_rate_for_billing_cycle(self, billing_cycle):
        """
        Get the rate for a specific billing cycle.
        If ShiftRate exists, use it; otherwise calculate from base monthly price.
        """
        try:
            shift_rate = self.rates.get(billing_type=billing_cycle)
            return shift_rate.rate
        except ShiftRate.DoesNotExist:
            # Calculate rate based on base monthly price
            if billing_cycle == 'monthly':
                return self.price
            elif billing_cycle == 'weekly':
                return round(self.price / 4.33)  # Average weeks per month
            elif billing_cycle == 'daily':
                return round(self.price / 30)  # Average days per month
            else:
                return self.price

    def get_half_cycle_rate(self, billing_cycle):
        """
        Calculate half-cycle rate: (base_rate / 2) + 10% of (base_rate / 2)
        """
        base_rate = self.get_rate_for_billing_cycle(billing_cycle)
        half_rate = base_rate / 2
        return round(half_rate + (half_rate * 0.1))

    def get_display_rate_with_period(self, billing_cycle):
        """
        Get formatted rate with billing period for display
        """
        rate = self.get_rate_for_billing_cycle(billing_cycle)
        period_map = {
            'daily': '/day',
            'weekly': '/week',
            'monthly': '/month'
        }
        return f"₹{rate}{period_map.get(billing_cycle, '/month')}"


class ShiftRate(models.Model):
    BILLING_TYPE_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('half_weekly', 'Half Weekly'),
        ('half_monthly', 'Half Monthly'),
    ]

    shift = models.ForeignKey(Shift, on_delete=models.CASCADE, related_name='rates')
    billing_type = models.CharField(max_length=15, choices=BILLING_TYPE_CHOICES)
    rate = models.IntegerField(help_text='Rate for the selected billing period')

    class Meta:
        unique_together = ('shift', 'billing_type')

    def __str__(self):
        return f"{self.shift.name} - {self.get_billing_type_display()}: ₹{self.rate}"


class Months(models.Model):
    name = models.CharField(max_length=225, unique=True)

    def __str__(self):
        return self.name


class States(models.Model):
    name = models.CharField(max_length=225, unique=True)

    def __str__(self):
        return self.name


class Courses(models.Model):
    name = models.CharField(max_length=225, unique=True)

    def __str__(self):
        return self.name


class StudentData(models.Model):
    IDENTITY_CHOICES = [
        ('pan', 'PAN Card'),
        ('aadhaar', 'Aadhaar Card'),
        ('driving_license', 'Driving License'),
        ('voter_id', 'Voter ID Card'),
    ]

    librarian = models.ForeignKey(
        Librarian_param, on_delete=models.CASCADE, null=True, blank=True
    )
    sublibrarian = models.ForeignKey(
        Sublibrarian_param, on_delete=models.CASCADE, null=True, blank=True
    )
    unique_id = models.CharField(max_length=20, unique=True, blank=True)
    course = models.ForeignKey(Courses, on_delete=models.CASCADE)
    name = models.CharField(max_length=225)
    f_name = models.CharField(max_length=225, null=True, blank=True, default=None)
    age = models.IntegerField(null=True, blank=True, default=0)
    gender = models.CharField(max_length=225)
    email = models.EmailField(max_length=225, blank=True, null=True)
    mobile = models.BigIntegerField()
    locality = models.CharField(max_length=225)
    city = models.CharField(max_length=225)
    state = models.ForeignKey(States, on_delete=models.CASCADE)
    registration_date = models.DateField(default=timezone.now)
    registration_fee = models.IntegerField()

    # Proof of Identity fields
    identity_type = models.CharField(
        max_length=20,
        choices=IDENTITY_CHOICES,
        null=True,
        blank=True,
        help_text="Type of identity document"
    )
    identity_number = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="Identity document number"
    )
    # is_first_registration = models.BooleanField(default=True)
    image = models.ImageField(upload_to="student_image/", blank=True, null=True)
    slug = models.SlugField(max_length=250, blank=True)
    color = models.CharField(max_length=20, default="normal")

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['identity_number'],
                condition=models.Q(identity_number__isnull=False) & ~models.Q(identity_number=''),
                name='unique_identity_number_studentdata'
            ),
            models.UniqueConstraint(
                fields=['librarian', 'email', 'mobile'],
                condition=models.Q(email__isnull=False) & ~models.Q(email=''),
                name='unique_librarian_email_mobile_studentdata'
            )
        ]

    def __str__(self):
        return f"{self.name} | {self.unique_id}"

    def get_masked_identity_number(self):
        """Return masked identity number for privacy protection"""
        if not self.identity_number:
            return None

        identity_num = str(self.identity_number)
        if len(identity_num) <= 4:
            return "****"

        # Show first 2 and last 2 characters, mask the middle
        return identity_num[:2] + "*" * (len(identity_num) - 4) + identity_num[-2:]

    def generate_unique_id(self):
        if self.librarian:
            library_prefix = self.librarian.user.username[:4].lower()
        elif self.sublibrarian:
            library_prefix = self.sublibrarian.librarian.user.username[:4].lower()
        else:
            raise ValueError("Either librarian or sublibrarian must be set")

        today = timezone.now()
        date_str = today.strftime("%m%y")
        count = (
            StudentData.objects.filter(
                unique_id__startswith=f"{library_prefix}_{date_str}"
            ).count()
            + 1
        )

        if count > 999:
            raise ValidationError(
                "The maximum number of registrations for this month has been reached."
            )

        unique_id = f"{library_prefix}_{date_str}_{count:03d}"

        # Retry mechanism to ensure unique_id is unique
        while StudentData.objects.filter(unique_id=unique_id).exists():
            count += 1
            if count > 999:
                raise ValidationError(
                    "The maximum number of registrations for this month has been reached."
                )
            unique_id = f"{library_prefix}_{date_str}_{count:03d}"

        return unique_id

    # def set_due_color(self):
    #     today = timezone.now().date()
    #     latest_invoice = self.invoice_set.order_by("-due_date").first()  # type: ignore

    #     if latest_invoice and latest_invoice.due_date:
    #         if isinstance(latest_invoice.due_date, str):
    #             try:
    #                 due_date = datetime.strptime(
    #                     latest_invoice.due_date, "%Y-%m-%d"
    #                 ).date()
    #             except ValueError:
    #                 due_date = today  # Fallback if date parsing fails
    #         else:
    #             due_date = latest_invoice.due_date

    #         delta = (due_date - today).days

    #         if delta <= -10:
    #             self.color = "grey"
    #         elif delta >= 1 and delta <= 6:
    #             self.color = "blue"
    #         elif delta == 0:
    #             self.color = "green"
    #         elif delta <= -1 and delta >= -9:
    #             self.color = "red"
    #         else:
    #             self.color = "normal"
    #     else:
    #         self.color = "normal"

    def save(self, *args, **kwargs):
        if not self.unique_id:
            self.unique_id = self.generate_unique_id()

        if self.sublibrarian and not self.librarian:
            self.librarian = self.sublibrarian.librarian

        if not self.librarian:
            raise ValueError("Either librarian or sublibrarian must be set")

        if not self.slug:
            base_slug = slugify(self.name + " " + self.city)
            unique_slug = base_slug
            num = 1
            while StudentData.objects.filter(slug=unique_slug).exists():
                unique_slug = f"{base_slug}-{num}"
                num += 1
            self.slug = unique_slug

        super().save(*args, **kwargs)


class Invoice(models.Model):
    PAYMENT_STATUS_CHOICES = [
        ('Unpaid', 'Unpaid'),
        ('Partially Paid', 'Partially Paid'),
        ('Paid', 'Paid'),
    ]

    BILLING_TYPE_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('half_weekly', 'Half Weekly'),
        ('half_monthly', 'Half Monthly'),
    ]

    invoice_id = models.CharField(max_length=20, unique=True, blank=True)
    student = models.ForeignKey(StudentData, on_delete=models.CASCADE)
    shift = models.ManyToManyField(Shift)
    months = models.ManyToManyField(Months, blank=True)  # Make optional since we use billing_units now
    issue_date = models.DateField(default=timezone.now)
    due_date = models.DateField()
    discount_amount = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    total_amount = models.IntegerField(validators=[MinValueValidator(1)])
    total_paid = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    remaining_due = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    is_paid_in_full = models.BooleanField(default=False)
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='Unpaid')
    mode_pay = models.CharField(max_length=225, default="Cash")
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    slug = models.SlugField(max_length=250, blank=True, unique=True)
    # Billing fields that were added in migration but missing from model
    billing_type = models.CharField(max_length=15, choices=BILLING_TYPE_CHOICES, default='monthly')
    billing_units = models.IntegerField(default=1, validators=[MinValueValidator(1), MaxValueValidator(12)], help_text='Number of billing periods (days/weeks/months)')
    billing_start_date = models.DateField(blank=True, null=True)
    billing_end_date = models.DateField(blank=True, null=True)

    class Meta:
        ordering = ['-issue_date', '-id']
        indexes = [
            models.Index(fields=['student', 'payment_status']),
            models.Index(fields=['issue_date']),
            models.Index(fields=['due_date']),
            models.Index(fields=['slug']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(total_amount__gte=0),
                name='invoice_total_amount_positive'
            ),
            models.CheckConstraint(
                check=models.Q(total_paid__gte=0),
                name='invoice_total_paid_positive'
            ),
            models.CheckConstraint(
                check=models.Q(remaining_due__gte=0),
                name='invoice_remaining_due_positive'
            ),
            models.CheckConstraint(
                check=models.Q(discount_amount__gte=0),
                name='invoice_discount_positive'
            ),
        ]

    def __str__(self):
        return f"Invoice: {self.invoice_id} - {self.student.name}"

    def generate_invoice_id(self):
        today = timezone.now()
        date_str = today.strftime("%m%y")

        # Get the active user's first name
        active_user = self.student.librarian
        first_name = active_user.user.first_name.replace(" ", "_") if active_user.user.first_name else "user"

        # Ensure first_name is not empty and contains only valid characters
        if not first_name or first_name.strip() == "":
            first_name = active_user.user.username[:10] if active_user.user.username else "lib"

        # Remove any special characters and ensure it's not empty
        import re
        first_name = re.sub(r'[^a-zA-Z0-9_]', '', first_name)
        if not first_name:
            first_name = "lib"

        # Initialize count
        count = 1

        while True:
            invoice_id = f"inv_{first_name}_{date_str}_{count:03d}"
            if not Invoice.objects.filter(invoice_id=invoice_id).exists():
                return invoice_id
            count += 1

    def save(self, *args, **kwargs):
        if not self.invoice_id:
            self.invoice_id = self.generate_invoice_id()

        if not self.slug and self.invoice_id:
            self.slug = slugify(self.invoice_id)

        # Calculate remaining due and payment status
        self.remaining_due = max(0, self.total_amount - self.total_paid)

        if self.total_paid >= self.total_amount:
            self.is_paid_in_full = True
            self.payment_status = 'Paid'
        elif self.total_paid > 0:
            self.is_paid_in_full = False
            self.payment_status = 'Partially Paid'
        else:
            self.is_paid_in_full = False
            self.payment_status = 'Unpaid'

        super().save(*args, **kwargs)

    def update_payment_status(self):
        """Update payment status based on current payments"""
        from django.db.models import Sum
        total_payments = self.payment_set.aggregate(
            total=Sum('amount_paid')
        )['total'] or 0

        self.total_paid = total_payments
        self.remaining_due = max(0, self.total_amount - self.total_paid)

        if self.total_paid >= self.total_amount:
            self.is_paid_in_full = True
            self.payment_status = 'Paid'
        elif self.total_paid > 0:
            self.is_paid_in_full = False
            self.payment_status = 'Partially Paid'
        else:
            self.is_paid_in_full = False
            self.payment_status = 'Unpaid'

        self.save()

    def get_payment_percentage(self):
        """Get payment completion percentage"""
        if self.total_amount == 0:
            return 0
        return round((self.total_paid / self.total_amount) * 100, 1)


class Payment(models.Model):
    PAYMENT_MODE_CHOICES = [
        ('Cash', 'Cash'),
        ('Online', 'Online'),
        ('UPI', 'UPI'),
        ('Card', 'Card'),
        ('Bank Transfer', 'Bank Transfer'),
        ('Cheque', 'Cheque'),
    ]

    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE)
    amount_paid = models.IntegerField(validators=[MinValueValidator(1)])
    payment_date = models.DateField(default=timezone.now)
    payment_mode = models.CharField(max_length=50, choices=PAYMENT_MODE_CHOICES, default='Cash')
    next_commitment_date = models.DateField(null=True, blank=True)
    transaction_id = models.CharField(max_length=100, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-payment_date', '-created_at']
        indexes = [
            models.Index(fields=['invoice', 'payment_date']),
            models.Index(fields=['payment_date']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(amount_paid__gt=0),
                name='payment_amount_positive'
            ),
        ]

    def __str__(self):
        return f"Payment of ₹{self.amount_paid} for {self.invoice.invoice_id}"

    def save(self, *args, **kwargs):
        is_new = self.pk is None
        super().save(*args, **kwargs)

        # Update invoice payment status after saving payment
        old_status = self.invoice.payment_status
        self.invoice.update_payment_status()

        # Send notification if payment status changed to 'Paid'
        if is_new and self.invoice.payment_status == 'Paid' and old_status != 'Paid':
            self.send_completion_notification()

    def send_completion_notification(self):
        """Send payment completion notification"""
        try:
            from utils.notifications import send_dynamic_email

            subject = f"Payment Completed - Invoice #{self.invoice.invoice_id}"
            template_name = "payment_completion_email.html"
            context = {
                "student": self.invoice.student,
                "invoice": self.invoice,
                "payment": self,
                "library_name": self.invoice.student.librarian.library_name,
            }

            send_dynamic_email(
                subject,
                template_name,
                context,
                self.invoice.student.email
            )
        except Exception as e:
            # Log error but don't raise to avoid breaking payment processing
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to send payment completion notification: {str(e)}")


class RegistrationFee(models.Model):
    student = models.OneToOneField(StudentData, on_delete=models.CASCADE)
    reg_unique_id = models.CharField(max_length=225, unique=True, blank=True)
    is_paid = models.BooleanField(default=False)
    slug = models.SlugField(max_length=1000, blank=True)

    def __str__(self):
        return f"Registration Fee: {self.reg_unique_id} - {self.student.name}"

    def generate_reg_unique_id(self):
        today = timezone.now()
        date_str = today.strftime("%m%y")
        library_name = self.student.librarian.library_name.replace(" ", "_")  # type: ignore

        # Initialize count
        count = 1

        while True:
            reg_unique_id = f"reg_{library_name}_{date_str}_{count:03d}"
            if not RegistrationFee.objects.filter(reg_unique_id=reg_unique_id).exists():
                return reg_unique_id
            count += 1

    def save(self, *args, **kwargs):
        if not self.reg_unique_id:
            self.reg_unique_id = self.generate_reg_unique_id()

        if not self.slug:
            self.slug = slugify(self.reg_unique_id)

        super().save(*args, **kwargs)


class TempStudentData(models.Model):
    IDENTITY_CHOICES = [
        ('pan', 'PAN Card'),
        ('aadhaar', 'Aadhaar Card'),
        ('driving_license', 'Driving License'),
        ('voter_id', 'Voter ID Card'),
    ]

    librarian = models.ForeignKey(
        Librarian_param, on_delete=models.CASCADE, null=True, blank=True
    )
    course = models.ForeignKey(Courses, on_delete=models.CASCADE)
    name = models.CharField(max_length=225)
    f_name = models.CharField(max_length=225, null=True, blank=True, default=None)
    age = models.IntegerField(null=True, blank=True, default=0)
    gender = models.CharField(max_length=225)
    email = models.EmailField(max_length=225, blank=True, null=True)
    mobile = models.BigIntegerField()
    locality = models.CharField(max_length=225)
    city = models.CharField(max_length=225)
    state = models.ForeignKey(States, on_delete=models.CASCADE)
    registration_date = models.DateField(default=timezone.now)
    image = models.ImageField(upload_to="temp_student_image/", blank=True, null=True)
    status = models.CharField(max_length=20, default="pending")
    created_at = models.DateTimeField(auto_now_add=True)

    # Proof of Identity fields
    identity_type = models.CharField(
        max_length=20,
        choices=IDENTITY_CHOICES,
        null=True,
        blank=True,
        help_text="Type of identity document"
    )
    identity_number = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="Identity document number"
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['identity_number'],
                condition=models.Q(identity_number__isnull=False) & ~models.Q(identity_number=''),
                name='unique_identity_number_tempstudentdata'
            )
        ]

    def __str__(self):
        return f"{self.librarian} - {self.name}"


# Seat model definition
class Seat(models.Model):
    librarian = models.ForeignKey(Librarian_param, on_delete=models.CASCADE)
    shift = models.ForeignKey(Shift, related_name="seats", on_delete=models.CASCADE)
    seat_number = models.CharField(max_length=20)
    is_available = models.BooleanField(default=True)

    class Meta:
        unique_together = (
            "librarian",
            "seat_number",
            "shift",
        )

    def __str__(self):
        return f"{self.librarian}-{self.seat_number} - {self.shift}"


# Booking model that references Shift model from the librarian app
class Booking(models.Model):
    student = models.ForeignKey(StudentData, on_delete=models.CASCADE)
    seat = models.ForeignKey(Seat, on_delete=models.CASCADE, related_name="booking_set")
    booking_date = models.DateField(auto_now_add=True)
    expire_date = models.DateField(blank=True, null=True)

    class Meta:
        unique_together = (
            "student",
            "seat",
        )

    def __str__(self):
        return f"{self.seat} - {self.booking_date}"


class ShortenedURL(models.Model):
    original_url = models.URLField()
    short_code = models.CharField(max_length=10, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if not self.short_code:
            self.short_code = self.generate_short_code()
        super().save(*args, **kwargs)

    def generate_short_code(self):
        return "".join(random.choices(string.ascii_letters + string.digits, k=6))

    def get_short_url(self, request):
        return request.build_absolute_uri(f"/students/s/{self.short_code}/")


class RegistrationFeedback(models.Model):
    """Model to store feedback from registration form users"""

    # Basic info
    temp_student = models.ForeignKey(
        TempStudentData,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Associated temporary student record"
    )
    librarian = models.ForeignKey(
        Librarian_param,
        on_delete=models.CASCADE,
        help_text="Library where registration was done"
    )

    # Question 1: How easy was it to fill out the registration form? (0-10 scale)
    ease_rating = models.IntegerField(
        choices=[(i, str(i)) for i in range(11)],
        help_text="0 = Very Difficult, 10 = Extremely Easy"
    )

    # Question 2: How long did the registration take you?
    TIME_CHOICES = [
        ('less_than_1', 'Less than 1 minute'),
        ('1_to_2', '1–2 minutes'),
        ('3_to_5', '3–5 minutes'),
        ('more_than_5', 'More than 5 minutes'),
    ]
    time_taken = models.CharField(
        max_length=20,
        choices=TIME_CHOICES,
        help_text="Time taken to complete registration"
    )

    # Question 3: Did you face any issues while filling the form?
    faced_issues = models.BooleanField(
        default=False,
        help_text="Whether user faced any issues"
    )
    issue_description = models.TextField(
        blank=True,
        null=True,
        max_length=500,
        help_text="Description of issues faced (if any)"
    )

    # Question 4: How visually appealing was the form design? (1-5 stars)
    design_rating = models.IntegerField(
        choices=[(i, f"{i} star{'s' if i != 1 else ''}") for i in range(1, 6)],
        help_text="Visual appeal rating from 1 to 5 stars"
    )

    # Question 5: Which device did you use to fill out the form?
    DEVICE_CHOICES = [
        ('mobile', 'Mobile'),
        ('tablet', 'Tablet'),
        ('laptop_desktop', 'Laptop/Desktop'),
        ('other', 'Other'),
    ]
    device_used = models.CharField(
        max_length=20,
        choices=DEVICE_CHOICES,
        help_text="Device used for registration"
    )

    # Question 6: Was the QR code easy to scan and access?
    qr_easy_to_scan = models.BooleanField(
        default=True,
        help_text="Whether QR code was easy to scan"
    )
    qr_problem_description = models.TextField(
        blank=True,
        null=True,
        max_length=300,
        help_text="Description of QR code problems (if any)"
    )

    # Question 7: What improvements would you like to see?
    suggestions = models.TextField(
        blank=True,
        null=True,
        max_length=1000,
        help_text="Optional suggestions for improvement"
    )

    # Question 8: Would you recommend this system to others?
    would_recommend = models.BooleanField(
        default=True,
        help_text="Whether user would recommend the system"
    )

    # Metadata
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text="IP address of the user"
    )
    user_agent = models.TextField(
        blank=True,
        null=True,
        help_text="Browser user agent string"
    )
    submitted_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When feedback was submitted"
    )

    class Meta:
        ordering = ['-submitted_at']
        verbose_name = "Registration Feedback"
        verbose_name_plural = "Registration Feedbacks"

    def __str__(self):
        student_name = self.temp_student.name if self.temp_student else "Anonymous"
        return f"Feedback from {student_name} - {self.librarian.library_name} ({self.submitted_at.strftime('%Y-%m-%d')})"

    def get_ease_rating_display_text(self):
        """Get descriptive text for ease rating"""
        if self.ease_rating <= 2:
            return "Very Difficult"
        elif self.ease_rating <= 4:
            return "Difficult"
        elif self.ease_rating <= 6:
            return "Moderate"
        elif self.ease_rating <= 8:
            return "Easy"
        else:
            return "Very Easy"

    def get_design_rating_stars(self):
        """Get star representation for design rating"""
        return "⭐" * self.design_rating + "☆" * (5 - self.design_rating)
