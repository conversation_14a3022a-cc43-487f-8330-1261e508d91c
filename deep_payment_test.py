#!/usr/bin/env python3
"""
Deep Payment System Testing - Find Real Issues
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def test_payment_calculation_bugs():
    """Test for payment calculation bugs"""
    print("🔍 Testing payment calculation logic...")
    
    try:
        from studentsData.models import Invoice, Payment, StudentData
        from librarian.models import Librarian_param
        
        # Get existing data
        librarian = Librarian_param.objects.first()
        student = StudentData.objects.filter(librarian=librarian).first()
        
        if not librarian or not student:
            print("⚠️  Skipping test - no test data available")
            return True
        
        # Create test invoice
        invoice = Invoice.objects.create(
            student=student,
            due_date=datetime.now().date() + timedelta(days=30),
            total_amount=1000,
            discount_amount=0,
            mode_pay='Cash',
            description='Deep test invoice'
        )
        
        print(f"Created invoice: {invoice.invoice_id}")
        print(f"Initial state - Total: ₹{invoice.total_amount}, Paid: ₹{invoice.total_paid}, Remaining: ₹{invoice.remaining_due}")
        
        # Test 1: Add partial payment
        payment1 = Payment.objects.create(
            invoice=invoice,
            amount_paid=300,
            payment_mode='Cash',
            notes='First payment'
        )
        
        invoice.refresh_from_db()
        print(f"After payment 1 - Total: ₹{invoice.total_amount}, Paid: ₹{invoice.total_paid}, Remaining: ₹{invoice.remaining_due}")
        
        # Check if calculations are correct
        expected_remaining = 1000 - 300
        if invoice.remaining_due != expected_remaining:
            print(f"❌ Calculation error: Expected remaining {expected_remaining}, got {invoice.remaining_due}")
            return False
        
        # Test 2: Add another payment
        payment2 = Payment.objects.create(
            invoice=invoice,
            amount_paid=400,
            payment_mode='UPI',
            notes='Second payment'
        )
        
        invoice.refresh_from_db()
        print(f"After payment 2 - Total: ₹{invoice.total_amount}, Paid: ₹{invoice.total_paid}, Remaining: ₹{invoice.remaining_due}")
        
        # Check calculations again
        expected_remaining = 1000 - 300 - 400
        if invoice.remaining_due != expected_remaining:
            print(f"❌ Calculation error: Expected remaining {expected_remaining}, got {invoice.remaining_due}")
            return False
        
        # Test 3: Complete payment
        payment3 = Payment.objects.create(
            invoice=invoice,
            amount_paid=300,
            payment_mode='Card',
            notes='Final payment'
        )
        
        invoice.refresh_from_db()
        print(f"After final payment - Total: ₹{invoice.total_amount}, Paid: ₹{invoice.total_paid}, Remaining: ₹{invoice.remaining_due}")
        
        # Check if marked as paid
        if not invoice.is_paid_in_full:
            print(f"❌ Invoice not marked as paid in full")
            return False
        
        if invoice.payment_status != 'Paid':
            print(f"❌ Payment status not updated: {invoice.payment_status}")
            return False
        
        # Clean up
        invoice.delete()
        
        print("✅ Payment calculations working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Payment calculation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_billing_cycle_functionality():
    """Test billing cycle calculations"""
    print("\n🔍 Testing billing cycle functionality...")
    
    try:
        from studentsData.models import Shift, ShiftRate
        from librarian.models import Librarian_param
        
        librarian = Librarian_param.objects.first()
        if not librarian:
            print("⚠️  No librarian found for testing")
            return True
        
        # Get or create a shift
        shift = Shift.objects.filter(librarian=librarian).first()
        if not shift:
            print("⚠️  No shift found for testing")
            return True
        
        print(f"Testing shift: {shift.name} with base price: ₹{shift.price}")
        
        # Test different billing cycles
        billing_types = ['daily', 'weekly', 'monthly']
        
        for billing_type in billing_types:
            try:
                rate = shift.get_rate_for_billing_cycle(billing_type)
                print(f"  {billing_type}: ₹{rate}")
                
                if rate <= 0:
                    print(f"❌ Invalid rate for {billing_type}: {rate}")
                    return False
                    
            except Exception as e:
                print(f"❌ Error calculating {billing_type} rate: {e}")
                return False
        
        print("✅ Billing cycle calculations working")
        return True
        
    except Exception as e:
        print(f"❌ Billing cycle test failed: {e}")
        return False

def test_payment_validation():
    """Test payment validation"""
    print("\n🔍 Testing payment validation...")
    
    try:
        from studentsData.models import Invoice, Payment, StudentData
        from librarian.models import Librarian_param
        
        librarian = Librarian_param.objects.first()
        student = StudentData.objects.filter(librarian=librarian).first()
        
        if not librarian or not student:
            print("⚠️  Skipping validation test - no test data")
            return True
        
        # Create test invoice
        invoice = Invoice.objects.create(
            student=student,
            due_date=datetime.now().date() + timedelta(days=30),
            total_amount=500,
            discount_amount=0,
            mode_pay='Cash',
            description='Validation test invoice'
        )
        
        # Test 1: Try negative payment
        try:
            payment = Payment.objects.create(
                invoice=invoice,
                amount_paid=-100,
                payment_mode='Cash',
                notes='Negative payment test'
            )
            print("❌ Negative payment allowed - validation failed")
            payment.delete()
            return False
        except Exception:
            print("✅ Negative payment blocked correctly")
        
        # Test 2: Try zero payment
        try:
            payment = Payment.objects.create(
                invoice=invoice,
                amount_paid=0,
                payment_mode='Cash',
                notes='Zero payment test'
            )
            print("❌ Zero payment allowed - validation failed")
            payment.delete()
            return False
        except Exception:
            print("✅ Zero payment blocked correctly")
        
        # Test 3: Try overpayment
        try:
            payment = Payment.objects.create(
                invoice=invoice,
                amount_paid=600,  # More than total
                payment_mode='Cash',
                notes='Overpayment test'
            )
            
            invoice.refresh_from_db()
            if invoice.remaining_due < 0:
                print("⚠️  Overpayment creates negative remaining due")
            else:
                print("✅ Overpayment handled gracefully")
            
            payment.delete()
            
        except Exception as e:
            print(f"✅ Overpayment blocked: {e}")
        
        # Clean up
        invoice.delete()
        
        print("✅ Payment validation tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Payment validation test failed: {e}")
        return False

def test_ui_template_issues():
    """Test for UI template issues"""
    print("\n🔍 Testing UI template issues...")
    
    try:
        # Check if payment templates exist
        import os
        template_dir = "templates"
        
        required_templates = [
            "add_payment.html",
            "partial_payments_list.html",
            "invoice_create.html"
        ]
        
        missing_templates = []
        for template in required_templates:
            template_path = os.path.join(template_dir, template)
            if not os.path.exists(template_path):
                missing_templates.append(template)
        
        if missing_templates:
            print(f"❌ Missing templates: {missing_templates}")
            return False
        
        print("✅ All payment templates exist")
        
        # Check for dark mode CSS
        css_path = "static/css/dark-mode.css"
        if os.path.exists(css_path):
            with open(css_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'payment' in content.lower():
                print("✅ Payment dark mode styles found")
            else:
                print("⚠️  Payment dark mode styles may be incomplete")
        else:
            print("❌ Dark mode CSS file missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ UI template test failed: {e}")
        return False

def test_database_integrity():
    """Test database integrity"""
    print("\n🔍 Testing database integrity...")
    
    try:
        from django.db import connection
        
        # Check for orphaned payments
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT COUNT(*) FROM studentsData_payment p
                LEFT JOIN studentsData_invoice i ON p.invoice_id = i.id
                WHERE i.id IS NULL
            """)
            orphaned_payments = cursor.fetchone()[0]
            
            if orphaned_payments > 0:
                print(f"❌ Found {orphaned_payments} orphaned payments")
                return False
            
            print("✅ No orphaned payments found")
        
        # Check for invoices with incorrect totals
        from studentsData.models import Invoice
        from django.db.models import Sum
        
        problematic_invoices = []
        for invoice in Invoice.objects.all()[:10]:  # Check first 10
            actual_total = invoice.payment_set.aggregate(total=Sum('amount_paid'))['total'] or 0
            if actual_total != invoice.total_paid:
                problematic_invoices.append(invoice.invoice_id)
        
        if problematic_invoices:
            print(f"❌ Found invoices with incorrect totals: {problematic_invoices}")
            return False
        
        print("✅ Invoice totals are consistent")
        return True
        
    except Exception as e:
        print(f"❌ Database integrity test failed: {e}")
        return False

def run_deep_tests():
    """Run all deep tests"""
    print("🚀 Running Deep Payment System Analysis")
    print("=" * 60)
    
    tests = [
        test_payment_calculation_bugs,
        test_billing_cycle_functionality,
        test_payment_validation,
        test_ui_template_issues,
        test_database_integrity
    ]
    
    passed = 0
    failed = 0
    issues_found = []
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
                issues_found.append(test.__name__)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
            issues_found.append(f"{test.__name__} (crashed)")
    
    print("\n" + "=" * 60)
    print("📊 DEEP TEST RESULTS")
    print("=" * 60)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if issues_found:
        print(f"\n🔍 ISSUES FOUND:")
        for issue in issues_found:
            print(f"  • {issue}")
    
    if failed == 0:
        print("\n🎉 Deep analysis complete - no critical issues found!")
    else:
        print(f"\n⚠️  {failed} issues found that need immediate attention!")
    
    return failed == 0

if __name__ == '__main__':
    run_deep_tests()
