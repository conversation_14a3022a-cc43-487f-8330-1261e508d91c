from django.db import models
from librarian.models import *
from django.utils import timezone
from dateutil.relativedelta import relativedelta
from django.utils.timezone import now


def get_or_create_free_plan():
    """
    Get or create a free plan to ensure one always exists
    """
    free_plan = Plan.objects.filter(price=0).first()
    if not free_plan:
        free_plan = Plan.objects.create(
            name="Free Trial",
            description_line_01="Free trial plan",
            description_line_02="Limited features",
            description_line_03="10 days validity",
            description_line_04="Basic support",
            description_line_05="No SMS included",
            price=0.00,
            discount_price=0.00,
            duration_months=1,
            sms_quantity=0,
            recommended=False
        )
    return free_plan


class Plan(models.Model):
    name = models.CharField(max_length=100)
    description_line_01 = models.CharField(max_length=100)
    description_line_02 = models.CharField(max_length=100)
    description_line_03 = models.CharField(max_length=100)
    description_line_04 = models.CharField(max_length=100)
    description_line_05 = models.CharField(max_length=100)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_price = models.DecimalField(max_digits=10, decimal_places=2)
    duration_months = models.PositiveIntegerField()
    sms_quantity = models.IntegerField(default=0)
    recommended = models.BooleanField(default=False)

    def __str__(self):
        return self.name


class Membership(models.Model):
    librarian = models.OneToOneField(Librarian_param, on_delete=models.CASCADE)
    plan = models.ForeignKey(Plan, on_delete=models.CASCADE)
    start_date = models.DateField()
    expiry_date = models.DateField()
    razorpay_order_id = models.CharField(max_length=100, default="Null")
    razorpay_payment_id = models.CharField(max_length=100, default="Null")
    razorpay_signature = models.CharField(max_length=100, default="Null")

    def __str__(self):
        return f"{self.librarian} - {self.plan}"

    def save(self, *args, **kwargs):
        # Set start_date if not already set
        if not self.start_date:
            self.start_date = timezone.now().date()

        # Set expiry_date based on the plan duration in months if not set
        if not self.expiry_date and self.plan:
            self.expiry_date = self.start_date + relativedelta(
                months=self.plan.duration_months
            )

        today = timezone.now().date()

        if self.expiry_date and self.expiry_date < today:
            self.librarian.is_librarian = False
        else:
            self.librarian.is_librarian = True

        self.librarian.save()  # Be cautious with recursive saves
        super().save(*args, **kwargs)


class SmsPlan(models.Model):
    name = models.CharField(max_length=100)
    description_line_01 = models.CharField(max_length=100, null=True, blank=True)
    description_line_02 = models.CharField(max_length=100, null=True, blank=True)
    description_line_03 = models.CharField(max_length=100, null=True, blank=True)
    description_line_04 = models.CharField(max_length=100, null=True, blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_price = models.DecimalField(max_digits=10, decimal_places=2)
    sms_quantity = models.IntegerField()
    recommended = models.BooleanField(default=False)

    def __str__(self):
        return self.name


class Coupon(models.Model):
    DISCOUNT_TYPE_CHOICES = [
        ('amount', 'Discount Amount'),
        ('percentage', 'Percentage Discount'),
    ]
    discount_type = models.CharField(max_length=100, choices=DISCOUNT_TYPE_CHOICES)
    usage_limit = models.PositiveIntegerField(default=0)
    code = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    discount = models.DecimalField(
        max_digits=5, decimal_places=2, help_text="Discount percentage"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expiry_date = models.DateTimeField()
    overallDiscountAmount = models.DecimalField(max_digits=100, decimal_places=2, default=0) 
    def __str__(self):
        return self.code

    def is_valid(self):
        """Check if the coupon is still valid."""
        return self.is_active and self.expiry_date > now()
