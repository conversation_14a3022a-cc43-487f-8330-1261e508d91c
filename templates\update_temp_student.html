{% extends "base.html" %}

{% block title %}Update Temporary Student{% endblock %}

{% block page_title %}Student Management{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'temp_students_list' %}">Temporary Students</a></li>
<li class="breadcrumb-item active" aria-current="page">Update Student</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Update Temp Student Glass Theme */
    .form-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: calc(100vh - var(--topbar-height));
        padding: 2rem;
        position: relative;
    }

    .form-content::before {
        content: '';
        position: fixed;
        top: var(--topbar-height);
        left: 0;
        width: 100%;
        height: calc(100% - var(--topbar-height));
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    .form-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset,
            0 2px 4px rgba(255, 255, 255, 0.1) inset;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .form-card:hover {
        transform: translateY(-2px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .form-header {
        padding: 2rem 2rem 1rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        text-align: center;
    }

    .form-title {
        font-family: 'Plus Jakarta Sans', sans-serif;
        font-weight: 700;
        font-size: 2rem;
        color: white;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
    }

    .form-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        font-size: 1.5rem;
    }

    .form-subtitle {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }

    .form-body {
        padding: 2rem;
    }

    .form-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }

    .form-section:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
    }

    .section-title {
        font-family: 'Plus Jakarta Sans', sans-serif;
        font-weight: 600;
        font-size: 1.125rem;
        color: white;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        font-size: 0.875rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .label-icon {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        font-size: 0.75rem;
    }

    .form-control,
    .form-select {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        padding: 0.875rem 1rem;
        color: white;
        font-weight: 500;
        transition: all 0.3s ease;
        width: 100%;
    }

    .form-control:focus,
    .form-select:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(99, 102, 241, 0.5);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        color: white;
        outline: none;
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .form-select option {
        background: rgba(30, 30, 30, 0.95);
        color: white;
        padding: 0.5rem;
    }

    /* Form Validation Styles */
    .form-control.is-valid,
    .form-select.is-valid {
        border-color: rgba(34, 197, 94, 0.5);
        background: rgba(34, 197, 94, 0.1);
    }

    .form-control.is-invalid,
    .form-select.is-invalid {
        border-color: rgba(239, 68, 68, 0.5);
        background: rgba(239, 68, 68, 0.1);
    }

    .valid-feedback {
        color: #22c55e;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .invalid-feedback {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    /* Action Buttons */
    .form-actions {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
        flex-wrap: wrap;
    }

    .action-btn {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem 2rem;
        color: white;
        text-decoration: none;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: center;
        cursor: pointer;
        flex: 1;
        min-width: 150px;
    }

    .action-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        text-decoration: none;
    }

    .action-btn.primary {
        background: rgba(99, 102, 241, 0.3);
        border-color: rgba(99, 102, 241, 0.5);
    }

    .action-btn.primary:hover {
        background: rgba(99, 102, 241, 0.4);
    }

    .action-btn.secondary {
        background: rgba(107, 114, 128, 0.3);
        border-color: rgba(107, 114, 128, 0.5);
    }

    .action-btn.secondary:hover {
        background: rgba(107, 114, 128, 0.4);
    }

    /* Status Badge */
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: capitalize;
        margin-bottom: 1rem;
    }

    .status-badge.pending {
        background: rgba(245, 158, 11, 0.2);
        color: #f59e0b;
        border: 1px solid rgba(245, 158, 11, 0.3);
    }

    .status-badge.completed {
        background: rgba(16, 185, 129, 0.2);
        color: #10b981;
        border: 1px solid rgba(16, 185, 129, 0.3);
    }

    /* Mobile Responsive */
    @media (max-width: 991.98px) {
        .form-content {
            padding: 1rem;
            padding-bottom: calc(1rem + var(--bottom-menu-height));
        }
    }

    @media (max-width: 767.98px) {
        .form-content {
            padding: 0.75rem;
            padding-bottom: calc(0.75rem + var(--bottom-menu-height));
        }

        .form-header,
        .form-body {
            padding: 1.5rem;
        }

        .form-title {
            font-size: 1.5rem;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-actions {
            flex-direction: column;
        }

        .action-btn {
            width: 100%;
        }

        .form-section {
            padding: 1rem;
        }
    }

    /* Disable zoom and text selection for UI elements */
    body {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        touch-action: manipulation;
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent;
    }

    /* Allow text selection for form inputs */
    .form-control,
    .form-select {
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
    }

    /* Animation for page load */
    .form-card,
    .form-section {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Staggered animations */
    .form-group {
        animation: fadeInUp 0.6s ease-out forwards;
        opacity: 0;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Loading state for form submission */
    .form-loading .action-btn.primary {
        background: rgba(107, 114, 128, 0.3);
        cursor: not-allowed;
        pointer-events: none;
    }

    .form-loading .action-btn.primary::after {
        content: '';
        width: 16px;
        height: 16px;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 0.5rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="form-content">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-8 col-xl-6">
                <!-- Main Form Card -->
                <div class="form-card">
                    <!-- Form Header -->
                    <div class="form-header">
                        <h1 class="form-title">
                            <div class="form-icon">
                                <i class="fas fa-user-edit"></i>
                            </div>
                            Update Temporary Student
                        </h1>
                        <p class="form-subtitle">Modify student information and update registration status</p>

                        <!-- Current Status Badge -->
                        <div class="status-badge {{ temp_student.status }}">
                            <i class="fas fa-{% if temp_student.status == 'completed' %}check-circle{% else %}clock{% endif %}"></i>
                            {{ temp_student.status|title }}
                        </div>
                    </div>

                    <!-- Form Body -->
                    <div class="form-body">
                        <form method="POST" id="updateStudentForm" novalidate>
                            {% csrf_token %}

                            <!-- Personal Information Section -->
                            <div class="form-section">
                                <h3 class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    Personal Information
                                </h3>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name" class="form-label">
                                                <div class="label-icon">
                                                    <i class="fas fa-signature"></i>
                                                </div>
                                                Student Name
                                            </label>
                                            <input type="text"
                                                   id="name"
                                                   name="name"
                                                   class="form-control"
                                                   value="{{ temp_student.name }}"
                                                   placeholder="Enter student's full name"
                                                   required>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle"></i>
                                                Please provide a valid name.
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="f_name" class="form-label">
                                                <div class="label-icon">
                                                    <i class="fas fa-male"></i>
                                                </div>
                                                Father's Name
                                            </label>
                                            <input type="text"
                                                   id="f_name"
                                                   name="f_name"
                                                   class="form-control"
                                                   value="{{ temp_student.f_name }}"
                                                   placeholder="Enter father's name"
                                                   required>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle"></i>
                                                Please provide father's name.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="age" class="form-label">
                                                <div class="label-icon">
                                                    <i class="fas fa-birthday-cake"></i>
                                                </div>
                                                Age
                                            </label>
                                            <input type="number"
                                                   id="age"
                                                   name="age"
                                                   class="form-control"
                                                   value="{{ temp_student.age|default:18 }}"
                                                   min="16"
                                                   max="100"
                                                   placeholder="Enter age"
                                                   required>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle"></i>
                                                Please provide a valid age (16-100).
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="gender" class="form-label">
                                                <div class="label-icon">
                                                    <i class="fas fa-venus-mars"></i>
                                                </div>
                                                Gender
                                            </label>
                                            <select id="gender" name="gender" class="form-select" required>
                                                <option value="">Select Gender</option>
                                                <option value="male" {% if temp_student.gender == "Male" %}selected{% endif %}>Male</option>
                                                <option value="female" {% if temp_student.gender == "Female" %}selected{% endif %}>Female</option>
                                                <option value="other" {% if temp_student.gender == "Other" %}selected{% endif %}>Other</option>
                                            </select>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle"></i>
                                                Please select a gender.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Information Section -->
                            <div class="form-section">
                                <h3 class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-address-book"></i>
                                    </div>
                                    Contact Information
                                </h3>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="email" class="form-label">
                                                <div class="label-icon">
                                                    <i class="fas fa-envelope"></i>
                                                </div>
                                                Email Address
                                            </label>
                                            <input type="email"
                                                   id="email"
                                                   name="email"
                                                   class="form-control"
                                                   value="{{ temp_student.email }}"
                                                   placeholder="Enter email address"
                                                   required>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle"></i>
                                                Please provide a valid email address.
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mobile" class="form-label">
                                                <div class="label-icon">
                                                    <i class="fas fa-phone"></i>
                                                </div>
                                                Mobile Number
                                            </label>
                                            <input type="tel"
                                                   id="mobile"
                                                   name="mobile"
                                                   class="form-control"
                                                   value="{{ temp_student.mobile }}"
                                                   placeholder="Enter mobile number"
                                                   pattern="[0-9]{10}"
                                                   required>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle"></i>
                                                Please provide a valid 10-digit mobile number.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Address Information Section -->
                            <div class="form-section">
                                <h3 class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    Address Information
                                </h3>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="locality" class="form-label">
                                                <div class="label-icon">
                                                    <i class="fas fa-home"></i>
                                                </div>
                                                Locality
                                            </label>
                                            <input type="text"
                                                   id="locality"
                                                   name="locality"
                                                   class="form-control"
                                                   value="{{ temp_student.locality }}"
                                                   placeholder="Enter locality/area"
                                                   required>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle"></i>
                                                Please provide locality information.
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="city" class="form-label">
                                                <div class="label-icon">
                                                    <i class="fas fa-city"></i>
                                                </div>
                                                City
                                            </label>
                                            <input type="text"
                                                   id="city"
                                                   name="city"
                                                   class="form-control"
                                                   value="{{ temp_student.city }}"
                                                   placeholder="Enter city name"
                                                   required>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle"></i>
                                                Please provide city information.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Status Information Section -->
                            <div class="form-section">
                                <h3 class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-tasks"></i>
                                    </div>
                                    Registration Status
                                </h3>

                                <div class="form-group">
                                    <label for="status" class="form-label">
                                        <div class="label-icon">
                                            <i class="fas fa-flag"></i>
                                        </div>
                                        Current Status
                                    </label>
                                    <select id="status" name="status" class="form-select" required>
                                        <option value="">Select Status</option>
                                        <option value="pending" {% if temp_student.status == "pending" %}selected{% endif %}>Pending</option>
                                        <option value="completed" {% if temp_student.status == "completed" %}selected{% endif %}>Completed</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle"></i>
                                        Please select a status.
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="form-actions">
                                <button type="submit" class="action-btn primary">
                                    <i class="fas fa-save"></i>
                                    Update Student
                                </button>
                                <a href="{% url 'temp_students_list' %}" class="action-btn secondary">
                                    <i class="fas fa-arrow-left"></i>
                                    Back to List
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Update Temp Student Form Functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize form validation
        initializeFormValidation();

        // Initialize page animations
        initializeAnimations();

        // Removed page load success message
    });

    function initializeFormValidation() {
        const form = document.getElementById('updateStudentForm');
        const inputs = form.querySelectorAll('.form-control, .form-select');

        // Add real-time validation
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });

            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    validateField(this);
                }
            });
        });

        // Handle form submission
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            let isValid = true;
            inputs.forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });

            if (isValid) {
                submitForm();
            } else {
                if (window.modernDashboard) {
                    window.modernDashboard.showToast(
                        'Validation Error',
                        'Please correct the errors in the form.',
                        'error'
                    );
                }
            }
        });
    }

    function validateField(field) {
        const value = field.value.trim();
        let isValid = true;

        // Remove existing validation classes
        field.classList.remove('is-valid', 'is-invalid');

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
        }

        // Specific field validations
        switch (field.type) {
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (value && !emailRegex.test(value)) {
                    isValid = false;
                }
                break;

            case 'tel':
                const phoneRegex = /^[0-9]{10}$/;
                if (value && !phoneRegex.test(value)) {
                    isValid = false;
                }
                break;

            case 'number':
                const age = parseInt(value);
                if (value && (age < 16 || age > 100)) {
                    isValid = false;
                }
                break;
        }

        // Apply validation classes
        if (isValid && value) {
            field.classList.add('is-valid');
        } else if (!isValid) {
            field.classList.add('is-invalid');
        }

        return isValid;
    }

    function submitForm() {
        const form = document.getElementById('updateStudentForm');
        const submitBtn = form.querySelector('.action-btn.primary');

        // Add loading state
        form.classList.add('form-loading');
        submitBtn.disabled = true;

        // Show loading toast
        if (window.modernDashboard) {
            window.modernDashboard.showToast(
                'Updating...',
                'Please wait while we update the student information.',
                'info'
            );
        }

        // Submit the form
        form.submit();
    }

    function initializeAnimations() {
        // Add staggered animation to form groups
        const formGroups = document.querySelectorAll('.form-group');
        formGroups.forEach((group, index) => {
            group.style.animationDelay = `${index * 0.1}s`;
        });

        // Add staggered animation to form sections
        const formSections = document.querySelectorAll('.form-section');
        formSections.forEach((section, index) => {
            section.style.animationDelay = `${index * 0.2}s`;
        });
    }

    // Enhanced form interactions
    document.addEventListener('focus', function(e) {
        if (e.target.matches('.form-control, .form-select')) {
            e.target.closest('.form-group').style.transform = 'scale(1.02)';
        }
    }, true);

    document.addEventListener('blur', function(e) {
        if (e.target.matches('.form-control, .form-select')) {
            e.target.closest('.form-group').style.transform = 'scale(1)';
        }
    }, true);

    // Auto-capitalize name fields
    document.getElementById('name').addEventListener('input', function() {
        this.value = this.value.replace(/\b\w/g, l => l.toUpperCase());
    });

    document.getElementById('f_name').addEventListener('input', function() {
        this.value = this.value.replace(/\b\w/g, l => l.toUpperCase());
    });

    // Format mobile number input
    document.getElementById('mobile').addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '').slice(0, 10);
    });

    // Status change handler
    document.getElementById('status').addEventListener('change', function() {
        const statusBadge = document.querySelector('.status-badge');
        const newStatus = this.value;

        if (statusBadge && newStatus) {
            statusBadge.className = `status-badge ${newStatus}`;
            statusBadge.innerHTML = `
                <i class="fas fa-${newStatus === 'completed' ? 'check-circle' : 'clock'}"></i>
                ${newStatus.charAt(0).toUpperCase() + newStatus.slice(1)}
            `;
        }
    });
</script>
{% endblock %}
