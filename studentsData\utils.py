from django.utils import timezone

from librarian.models import Librarian_param
from .models import <PERSON><PERSON>, <PERSON>t, Shift


def update_seat_availability(librarian):
    """
    Update the availability of seats based on expired bookings.
    """
    now = timezone.now()

    expired_bookings = Booking.objects.filter(
        expire_date__lt=now, student__librarian=librarian
    )

    for booking in expired_bookings:

        seat = booking.seat

        seat.is_available = True
        seat.save()

    return len(expired_bookings)


def get_seat_management_options_for_merge(shift_ids):
    """
    Get seat management options when merging shifts.
    Returns information about seats that will be affected.
    """
    shifts = Shift.objects.filter(id__in=shift_ids)

    seat_info = {
        'total_seats': 0,
        'occupied_seats': 0,
        'available_seats': 0,
        'seat_conflicts': [],
        'booking_count': 0,
        'options': []
    }

    # Collect all seats from shifts to be merged
    all_seats = []
    for shift in shifts:
        shift_seats = list(shift.seats.all())
        all_seats.extend(shift_seats)

    seat_info['total_seats'] = len(all_seats)

    # Group seats by seat_number to identify conflicts
    seat_groups = {}
    for seat in all_seats:
        if seat.seat_number not in seat_groups:
            seat_groups[seat.seat_number] = []
        seat_groups[seat.seat_number].append(seat)

    # Analyze seat conflicts and bookings
    for seat_number, seats in seat_groups.items():
        occupied_seats_in_group = [s for s in seats if not s.is_available]
        available_seats_in_group = [s for s in seats if s.is_available]

        seat_info['occupied_seats'] += len(occupied_seats_in_group)
        seat_info['available_seats'] += len(available_seats_in_group)

        # Count bookings
        for seat in occupied_seats_in_group:
            seat_info['booking_count'] += seat.booking_set.count()

        # Check for conflicts (same seat number in multiple shifts with different availability)
        if len(seats) > 1:
            availability_states = [s.is_available for s in seats]
            if not all(state == availability_states[0] for state in availability_states):
                seat_info['seat_conflicts'].append({
                    'seat_number': seat_number,
                    'shifts': [s.shift.name for s in seats],
                    'availability': [s.is_available for s in seats]
                })

    # Define management options
    seat_info['options'] = [
        {
            'id': 'preserve_merge',
            'name': 'Preserve and Merge Seats',
            'description': 'Transfer all seats to the merged shift. Conflicts resolved by keeping occupied status.',
            'recommended': True,
            'preserves_bookings': True
        },
        {
            'id': 'delete_recreate',
            'name': 'Delete and Recreate Seats',
            'description': 'Delete all existing seats and create new ones. All bookings will be lost.',
            'recommended': False,
            'preserves_bookings': False
        },
        {
            'id': 'manual_transfer',
            'name': 'Manual Seat Transfer',
            'description': 'Manually select which seats to transfer to the merged shift.',
            'recommended': len(seat_info['seat_conflicts']) > 0,
            'preserves_bookings': True
        }
    ]

    return seat_info


def transfer_seats_to_shift(source_shift_ids, target_shift, seat_numbers=None):
    """
    Transfer specific seats from source shifts to target shift.
    If seat_numbers is None, transfer all seats.
    """
    source_shifts = Shift.objects.filter(id__in=source_shift_ids)
    transferred_count = 0

    for shift in source_shifts:
        seats_to_transfer = shift.seats.all()
        if seat_numbers:
            seats_to_transfer = seats_to_transfer.filter(seat_number__in=seat_numbers)

        for seat in seats_to_transfer:
            # Check if seat already exists in target shift
            if not Seat.objects.filter(
                seat_number=seat.seat_number,
                shift=target_shift,
                librarian=seat.librarian
            ).exists():
                # Create new seat in target shift
                new_seat = Seat.objects.create(
                    librarian=seat.librarian,
                    shift=target_shift,
                    seat_number=seat.seat_number,
                    is_available=seat.is_available
                )

                # Transfer bookings
                for booking in seat.booking_set.all():
                    booking.seat = new_seat
                    booking.save()

                transferred_count += 1

    return transferred_count
