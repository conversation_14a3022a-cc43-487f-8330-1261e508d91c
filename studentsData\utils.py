from django.utils import timezone
from django.db import transaction

from librarian.models import Librarian_param
from .models import Booking, Seat, Shift, MergedShift, MergedShiftBooking, ShiftAttendance


def update_seat_availability(librarian):
    """
    Update the availability of seats based on expired bookings.
    """
    now = timezone.now()

    expired_bookings = Booking.objects.filter(
        expire_date__lt=now, student__librarian=librarian
    )

    for booking in expired_bookings:

        seat = booking.seat

        seat.is_available = True
        seat.save()

    return len(expired_bookings)


def get_seat_management_options_for_merge(shift_ids):
    """
    Get seat management options when merging shifts.
    Returns information about seats that will be affected.
    """
    shifts = Shift.objects.filter(id__in=shift_ids)

    seat_info = {
        'total_seats': 0,
        'occupied_seats': 0,
        'available_seats': 0,
        'seat_conflicts': [],
        'booking_count': 0,
        'options': []
    }

    # Collect all seats from shifts to be merged
    all_seats = []
    for shift in shifts:
        shift_seats = list(shift.seats.all())
        all_seats.extend(shift_seats)

    seat_info['total_seats'] = len(all_seats)

    # Group seats by seat_number to identify conflicts
    seat_groups = {}
    for seat in all_seats:
        if seat.seat_number not in seat_groups:
            seat_groups[seat.seat_number] = []
        seat_groups[seat.seat_number].append(seat)

    # Analyze seat conflicts and bookings
    for seat_number, seats in seat_groups.items():
        occupied_seats_in_group = [s for s in seats if not s.is_available]
        available_seats_in_group = [s for s in seats if s.is_available]

        seat_info['occupied_seats'] += len(occupied_seats_in_group)
        seat_info['available_seats'] += len(available_seats_in_group)

        # Count bookings
        for seat in occupied_seats_in_group:
            seat_info['booking_count'] += seat.booking_set.count()

        # Check for conflicts (same seat number in multiple shifts with different availability)
        if len(seats) > 1:
            availability_states = [s.is_available for s in seats]
            if not all(state == availability_states[0] for state in availability_states):
                seat_info['seat_conflicts'].append({
                    'seat_number': seat_number,
                    'shifts': [s.shift.name for s in seats],
                    'availability': [s.is_available for s in seats]
                })

    # Define management options
    seat_info['options'] = [
        {
            'id': 'preserve_merge',
            'name': 'Preserve and Merge Seats',
            'description': 'Transfer all seats to the merged shift. Conflicts resolved by keeping occupied status.',
            'recommended': True,
            'preserves_bookings': True
        },
        {
            'id': 'delete_recreate',
            'name': 'Delete and Recreate Seats',
            'description': 'Delete all existing seats and create new ones. All bookings will be lost.',
            'recommended': False,
            'preserves_bookings': False
        },
        {
            'id': 'manual_transfer',
            'name': 'Manual Seat Transfer',
            'description': 'Manually select which seats to transfer to the merged shift.',
            'recommended': len(seat_info['seat_conflicts']) > 0,
            'preserves_bookings': True
        }
    ]

    return seat_info


def book_merged_shift_seat(student, merged_shift, seat_number, expire_date=None):
    """
    Book a seat across all shifts in a merged shift.
    This ensures the same seat is reserved in all linked shifts.
    """
    with transaction.atomic():
        # Check if seat is available in all shifts
        available_seats = merged_shift.get_available_seats()
        available_seat_numbers = [seat.seat_number for seat in available_seats]

        if seat_number not in available_seat_numbers:
            raise ValueError(f"Seat {seat_number} is not available in all shifts of the merged shift")

        # Create merged shift booking record
        merged_booking = MergedShiftBooking.objects.create(
            student=student,
            merged_shift=merged_shift,
            seat_number=seat_number,
            expire_date=expire_date
        )

        # Book the seat in each individual shift
        individual_bookings = []
        for shift in merged_shift.shifts.all():
            seat = Seat.objects.get(
                librarian=merged_shift.librarian,
                shift=shift,
                seat_number=seat_number
            )

            # Create individual booking
            booking = Booking.objects.create(
                student=student,
                seat=seat,
                expire_date=expire_date
            )

            # Mark seat as unavailable
            seat.is_available = False
            seat.save()

            individual_bookings.append(booking)

        return merged_booking, individual_bookings


def cancel_merged_shift_booking(merged_booking):
    """
    Cancel a merged shift booking and free up seats in all linked shifts.
    """
    with transaction.atomic():
        # Get all individual bookings for this merged booking
        individual_bookings = merged_booking.get_individual_bookings()

        # Free up seats and delete individual bookings
        for booking in individual_bookings:
            seat = booking.seat
            seat.is_available = True
            seat.save()
            booking.delete()

        # Delete the merged booking
        merged_booking.delete()


def mark_merged_shift_attendance(student, merged_shift, date, check_in_time=None, check_out_time=None, notes=None):
    """
    Mark attendance for a merged shift. This allows attendance within any of the linked shift time windows.
    Records attendance per individual shift for clarity.
    Properly handles overnight shifts.
    """
    attendances = []

    # Find which shift(s) the check-in/out time falls within
    valid_shifts = []
    for shift in merged_shift.shifts.all():
        attendance, created = ShiftAttendance.objects.get_or_create(
            student=student,
            shift=shift,
            date=date,
            defaults={
                'merged_shift_booking': MergedShiftBooking.objects.filter(
                    student=student,
                    merged_shift=merged_shift
                ).first()
            }
        )

        # Check if the time is within this shift's window (using improved method)
        is_valid_time = False
        if check_in_time and attendance.is_within_shift_time(check_in_time, date):
            is_valid_time = True
        if check_out_time and attendance.is_within_shift_time(check_out_time, date):
            is_valid_time = True

        # For overnight shifts, also check if times fall in the extended window
        if shift.is_overnight_shift() and not is_valid_time:
            if check_in_time and shift.is_time_within_shift(check_in_time, date):
                is_valid_time = True
            if check_out_time and shift.is_time_within_shift(check_out_time, date):
                is_valid_time = True

        if is_valid_time:
            valid_shifts.append(shift)
            attendance.mark_attendance(check_in_time, check_out_time)
            if notes:
                attendance.notes = notes
                attendance.save()
            attendances.append(attendance)

    if not valid_shifts:
        # If no shift time window matches, still record attendance for all shifts
        # but mark it in notes that time was outside normal windows
        for shift in merged_shift.shifts.all():
            attendance, created = ShiftAttendance.objects.get_or_create(
                student=student,
                shift=shift,
                date=date,
                defaults={
                    'merged_shift_booking': MergedShiftBooking.objects.filter(
                        student=student,
                        merged_shift=merged_shift
                    ).first()
                }
            )

            attendance.mark_attendance(check_in_time, check_out_time)
            time_info = []
            if check_in_time:
                time_info.append(f"Check-in: {check_in_time}")
            if check_out_time:
                time_info.append(f"Check-out: {check_out_time}")

            attendance.notes = f"Time outside normal shift window ({', '.join(time_info)}). {notes or ''}".strip()
            attendance.save()
            attendances.append(attendance)

    return attendances


def get_merged_shift_availability(merged_shift):
    """
    Get availability information for a merged shift.
    Returns seats that are free in ALL linked shifts.
    """
    available_seats = merged_shift.get_available_seats()

    availability_info = {
        'merged_shift': merged_shift,
        'total_shifts': merged_shift.shifts.count(),
        'shift_names': merged_shift.get_shift_names(),
        'time_ranges': merged_shift.get_time_ranges(),
        'available_seats': available_seats,
        'available_count': available_seats.count(),
        'total_duration_minutes': merged_shift.get_total_duration_minutes(),
        'final_price': merged_shift.calculate_final_price(),
    }

    return availability_info


def transfer_seats_to_shift(source_shift_ids, target_shift, seat_numbers=None):
    """
    Transfer specific seats from source shifts to target shift.
    If seat_numbers is None, transfer all seats.
    """
    source_shifts = Shift.objects.filter(id__in=source_shift_ids)
    transferred_count = 0

    for shift in source_shifts:
        seats_to_transfer = shift.seats.all()
        if seat_numbers:
            seats_to_transfer = seats_to_transfer.filter(seat_number__in=seat_numbers)

        for seat in seats_to_transfer:
            # Check if seat already exists in target shift
            if not Seat.objects.filter(
                seat_number=seat.seat_number,
                shift=target_shift,
                librarian=seat.librarian
            ).exists():
                # Create new seat in target shift
                new_seat = Seat.objects.create(
                    librarian=seat.librarian,
                    shift=target_shift,
                    seat_number=seat.seat_number,
                    is_available=seat.is_available
                )

                # Transfer bookings
                for booking in seat.booking_set.all():
                    booking.seat = new_seat
                    booking.save()

                transferred_count += 1

    return transferred_count
