/**
 * Custom Notification Handler
 * Provides a better UX for requesting notification permissions
 * and handling Firebase Cloud Messaging
 */

class NotificationHandler {
    constructor() {
        this.messaging = null;
        this.isFirebaseInitialized = false;
        this.deviceToken = null;
        this.init();
    }

    async init() {
        try {
            console.log('Initializing notification handler...');

            // Register service worker for background notifications
            await this.registerServiceWorker();

            // Skip Firebase for now to avoid errors
            console.log('Running in demo mode (Firebase disabled for testing)');
            this.isFirebaseInitialized = false;

            // Check current permission status
            this.checkNotificationStatus();

            // Set up basic message handlers
            this.setupMessageHandlers();

            console.log('Notification handler initialized successfully');

        } catch (error) {
            console.error('Error initializing notification handler:', error);
            // Don't throw - allow the page to continue working
        }
    }

    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/static/js/notification-sw.js', {
                    scope: '/'
                });

                console.log('Service Worker registered successfully:', registration);

                // Listen for service worker messages
                navigator.serviceWorker.addEventListener('message', (event) => {
                    console.log('Message from Service Worker:', event.data);
                });

                return registration;
            } catch (error) {
                console.error('Service Worker registration failed:', error);
                return null;
            }
        } else {
            console.warn('Service Workers not supported');
            return null;
        }
    }

    async loadFirebase() {
        return new Promise((resolve, reject) => {
            // Load Firebase scripts dynamically if not present
            const scripts = [
                'https://www.gstatic.com/firebasejs/8.6.3/firebase-app.js',
                'https://www.gstatic.com/firebasejs/8.6.3/firebase-messaging.js'
            ];

            let loadedCount = 0;
            scripts.forEach(src => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    loadedCount++;
                    if (loadedCount === scripts.length) {
                        resolve();
                    }
                };
                script.onerror = reject;
                document.head.appendChild(script);
            });
        });
    }

    async initializeFirebase() {
        // For demo purposes, we'll simulate Firebase
        console.log('Firebase initialized (demo mode)');
        this.isFirebaseInitialized = true;
    }

    checkNotificationStatus() {
        if (!('Notification' in window)) {
            this.showNotificationNotSupported();
            return;
        }

        const permission = Notification.permission;
        const isMobile = this.isMobileDevice();

        console.log('Checking notification status:', {
            permission,
            isMobile,
            userAgent: navigator.userAgent
        });

        switch (permission) {
            case 'granted':
                this.handlePermissionGranted();
                break;
            case 'denied':
                if (isMobile) {
                    this.showMobilePermissionDenied();
                } else {
                    this.showPermissionDenied();
                }
                break;
            case 'default':
                if (isMobile) {
                    this.showMobilePermissionRequest();
                } else {
                    this.showCustomPermissionRequest();
                }
                break;
        }
    }

    isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    isIOSDevice() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }

    isAndroidDevice() {
        return /Android/i.test(navigator.userAgent);
    }

    showCustomPermissionRequest() {
        // Create custom notification permission modal
        const modal = this.createPermissionModal();
        document.body.appendChild(modal);

        // Show modal with animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 100);
    }

    showMobilePermissionRequest() {
        // Create mobile-specific permission modal
        const modal = this.createMobilePermissionModal();
        document.body.appendChild(modal);

        // Show modal with animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 100);
    }

    showMobilePermissionDenied() {
        // Show mobile-specific instructions for enabling notifications
        const modal = this.createMobileSettingsModal();
        document.body.appendChild(modal);

        // Show modal with animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 100);
    }

    createPermissionModal() {
        const modal = document.createElement('div');
        modal.className = 'notification-permission-modal';
        modal.innerHTML = `
            <div class="notification-modal-overlay">
                <div class="notification-modal-content">
                    <div class="notification-modal-header">
                        <div class="notification-icon">🔔</div>
                        <h3>Stay Updated!</h3>
                    </div>
                    <div class="notification-modal-body">
                        <p>Get instant notifications for:</p>
                        <ul>
                            <li>📝 New student registrations</li>
                            <li>💰 Payment updates</li>
                            <li>📢 Important announcements</li>
                            <li>⚡ Real-time updates</li>
                        </ul>
                        <p class="notification-privacy">We respect your privacy and won't spam you.</p>
                    </div>
                    <div class="notification-modal-actions">
                        <button class="btn-allow" onclick="notificationHandler.requestPermission()">
                            Allow Notifications
                        </button>
                        <button class="btn-later" onclick="notificationHandler.dismissModal()">
                            Maybe Later
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add styles
        this.addModalStyles();
        
        return modal;
    }

    createMobilePermissionModal() {
        const modal = document.createElement('div');
        modal.className = 'notification-permission-modal mobile-modal';

        const isIOS = this.isIOSDevice();
        const isAndroid = this.isAndroidDevice();

        modal.innerHTML = `
            <div class="notification-modal-overlay">
                <div class="notification-modal-content">
                    <div class="notification-modal-header">
                        <div class="notification-icon">📱</div>
                        <h3>Enable Mobile Notifications</h3>
                    </div>
                    <div class="notification-modal-body">
                        <p>Get instant notifications for important updates:</p>
                        <ul>
                            <li>📝 New student registrations</li>
                            <li>💰 Payment updates</li>
                            <li>📢 Important announcements</li>
                        </ul>

                        ${isIOS ? `
                        <div class="mobile-instructions">
                            <h4>📱 For iPhone/iPad:</h4>
                            <p>After tapping "Allow", you may need to:</p>
                            <ol>
                                <li>Add this site to your Home Screen</li>
                                <li>Open from Home Screen (not Safari)</li>
                                <li>Allow notifications when prompted</li>
                            </ol>
                        </div>
                        ` : ''}

                        ${isAndroid ? `
                        <div class="mobile-instructions">
                            <h4>📱 For Android:</h4>
                            <p>If notifications don't work immediately:</p>
                            <ol>
                                <li>Check Chrome notification settings</li>
                                <li>Ensure site notifications are enabled</li>
                                <li>Try refreshing the page</li>
                            </ol>
                        </div>
                        ` : ''}

                        <p class="notification-privacy">We'll only send important notifications.</p>
                    </div>
                    <div class="notification-modal-actions">
                        <button class="btn-allow" onclick="notificationHandler.requestMobilePermission()">
                            📱 Enable Notifications
                        </button>
                        <button class="btn-later" onclick="notificationHandler.dismissModal()">
                            Maybe Later
                        </button>
                    </div>
                </div>
            </div>
        `;

        return modal;
    }

    createMobileSettingsModal() {
        const modal = document.createElement('div');
        modal.className = 'notification-permission-modal mobile-settings-modal';

        const isIOS = this.isIOSDevice();
        const isAndroid = this.isAndroidDevice();

        modal.innerHTML = `
            <div class="notification-modal-overlay">
                <div class="notification-modal-content">
                    <div class="notification-modal-header">
                        <div class="notification-icon">⚙️</div>
                        <h3>Enable Notifications in Settings</h3>
                    </div>
                    <div class="notification-modal-body">
                        <p>Notifications are currently disabled. To enable them:</p>

                        ${isIOS ? `
                        <div class="mobile-instructions">
                            <h4>📱 iPhone/iPad Instructions:</h4>
                            <ol>
                                <li>Go to <strong>Settings</strong> → <strong>Safari</strong></li>
                                <li>Scroll down to <strong>Notifications</strong></li>
                                <li>Enable <strong>Allow Notifications</strong></li>
                                <li>Return to this page and refresh</li>
                            </ol>
                            <p><strong>Alternative:</strong> Add this site to your Home Screen and open from there.</p>
                        </div>
                        ` : ''}

                        ${isAndroid ? `
                        <div class="mobile-instructions">
                            <h4>📱 Android Instructions:</h4>
                            <ol>
                                <li>Tap the <strong>🔒</strong> or <strong>ⓘ</strong> icon in the address bar</li>
                                <li>Tap <strong>Permissions</strong> or <strong>Site Settings</strong></li>
                                <li>Find <strong>Notifications</strong> and set to <strong>Allow</strong></li>
                                <li>Refresh this page</li>
                            </ol>
                            <p><strong>Or:</strong> Go to Chrome Settings → Site Settings → Notifications</p>
                        </div>
                        ` : ''}

                        <div class="test-section">
                            <button class="btn-test" onclick="notificationHandler.testPermissionAfterSettings()">
                                🔄 Test Notifications Now
                            </button>
                        </div>
                    </div>
                    <div class="notification-modal-actions">
                        <button class="btn-allow" onclick="notificationHandler.openNotificationSettings()">
                            ⚙️ Open Settings
                        </button>
                        <button class="btn-later" onclick="notificationHandler.dismissModal()">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        `;

        return modal;
    }

    addModalStyles() {
        if (document.getElementById('notification-modal-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'notification-modal-styles';
        styles.textContent = `
            .notification-permission-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10000;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .notification-permission-modal.show {
                opacity: 1;
                visibility: visible;
            }

            .notification-modal-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }

            .notification-modal-content {
                background: white;
                border-radius: 12px;
                max-width: 400px;
                width: 100%;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                transform: translateY(20px);
                transition: transform 0.3s ease;
            }

            .notification-permission-modal.show .notification-modal-content {
                transform: translateY(0);
            }

            .notification-modal-header {
                text-align: center;
                padding: 30px 30px 20px;
                border-bottom: 1px solid #f0f0f0;
            }

            .notification-icon {
                font-size: 48px;
                margin-bottom: 15px;
            }

            .notification-modal-header h3 {
                margin: 0;
                color: #333;
                font-size: 24px;
                font-weight: 600;
            }

            .notification-modal-body {
                padding: 25px 30px;
            }

            .notification-modal-body p {
                margin: 0 0 15px;
                color: #666;
                line-height: 1.5;
            }

            .notification-modal-body ul {
                list-style: none;
                padding: 0;
                margin: 15px 0;
            }

            .notification-modal-body li {
                padding: 8px 0;
                color: #555;
                font-size: 14px;
            }

            .notification-privacy {
                font-size: 12px;
                color: #999;
                font-style: italic;
            }

            .notification-modal-actions {
                padding: 20px 30px 30px;
                display: flex;
                gap: 12px;
                flex-direction: column;
            }

            .notification-modal-actions button {
                padding: 12px 24px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .btn-allow {
                background: #007bff;
                color: white;
            }

            .btn-allow:hover {
                background: #0056b3;
                transform: translateY(-1px);
            }

            .btn-later {
                background: #f8f9fa;
                color: #6c757d;
                border: 1px solid #dee2e6;
            }

            .btn-later:hover {
                background: #e9ecef;
            }

            .mobile-instructions {
                background: #f8f9fa;
                border-left: 4px solid #007bff;
                padding: 15px;
                margin: 15px 0;
                border-radius: 4px;
            }

            .mobile-instructions h4 {
                margin-top: 0;
                color: #007bff;
                font-size: 16px;
            }

            .mobile-instructions ol {
                margin: 10px 0;
                padding-left: 20px;
            }

            .mobile-instructions li {
                margin: 8px 0;
                line-height: 1.4;
            }

            .btn-test {
                background: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                margin: 10px 0;
            }

            .btn-test:hover {
                background: #218838;
            }

            .test-section {
                text-align: center;
                margin: 20px 0;
                padding: 15px;
                background: #e8f5e8;
                border-radius: 8px;
            }

            @media (max-width: 480px) {
                .notification-modal-content {
                    margin: 10px;
                    max-height: 90vh;
                    overflow-y: auto;
                }

                .notification-modal-header,
                .notification-modal-body,
                .notification-modal-actions {
                    padding-left: 15px;
                    padding-right: 15px;
                }

                .notification-modal-header {
                    padding-top: 20px;
                    padding-bottom: 15px;
                }

                .notification-modal-body {
                    padding-top: 15px;
                    padding-bottom: 15px;
                    font-size: 14px;
                }

                .notification-modal-actions {
                    padding-bottom: 20px;
                }

                .mobile-instructions {
                    padding: 12px;
                    margin: 12px 0;
                }

                .mobile-instructions h4 {
                    font-size: 15px;
                }

                .mobile-instructions ol {
                    padding-left: 18px;
                }

                .notification-icon {
                    font-size: 40px;
                }

                .notification-modal-header h3 {
                    font-size: 20px;
                }
            }
        `;
        
        document.head.appendChild(styles);
    }

    async requestPermission() {
        try {
            console.log('Requesting notification permission...');

            if (!('Notification' in window)) {
                throw new Error('Notifications not supported');
            }

            const permission = await Notification.requestPermission();
            console.log('Permission result:', permission);

            if (permission === 'granted') {
                await this.handlePermissionGranted();
                this.dismissModal();
                this.showSuccessMessage();
            } else {
                console.log('Permission not granted:', permission);
                this.showPermissionDenied();
                this.dismissModal();
            }
        } catch (error) {
            console.error('Error requesting notification permission:', error);
            this.dismissModal();
            this.showToast('❌ Failed to enable notifications. Please check browser settings.', 'error');
        }
    }

    async requestMobilePermission() {
        try {
            console.log('Requesting mobile notification permission...');

            // Check if notifications are supported
            if (!('Notification' in window)) {
                throw new Error('Notifications not supported in this browser');
            }

            // Check current permission before requesting
            const currentPermission = Notification.permission;
            console.log('Current permission before request:', currentPermission);

            if (currentPermission === 'granted') {
                console.log('Permission already granted');
                await this.handlePermissionGranted();
                this.dismissModal();
                this.showMobileSuccessMessage();
                return;
            }

            if (currentPermission === 'denied') {
                console.log('Permission previously denied, showing settings instructions');
                this.dismissModal();
                setTimeout(() => {
                    this.showMobilePermissionDenied();
                }, 500);
                return;
            }

            // Request permission
            console.log('Requesting permission...');
            const permission = await Notification.requestPermission();

            console.log('Mobile permission result:', permission);

            if (permission === 'granted') {
                console.log('Permission granted, setting up...');
                await this.handlePermissionGranted();
                this.dismissModal();
                this.showMobileSuccessMessage();
            } else if (permission === 'denied') {
                console.log('Permission denied, showing settings help');
                this.dismissModal();
                setTimeout(() => {
                    this.showMobilePermissionDenied();
                }, 500);
            } else {
                console.log('Permission default/unknown, showing retry message');
                this.dismissModal();
                this.showToast('Permission request unclear. Please try again or check browser settings.', 'warning');
            }
        } catch (error) {
            console.error('Error requesting mobile notification permission:', error);
            this.dismissModal();

            // More specific error messages
            if (error.message.includes('not supported')) {
                this.showToast('❌ Notifications not supported in this browser', 'error');
            } else if (error.message.includes('denied')) {
                this.showToast('❌ Notifications blocked. Please enable in browser settings.', 'warning');
            } else {
                this.showToast('❌ Failed to enable notifications. Try: Settings → Site Settings → Notifications → Allow', 'warning');
            }
        }
    }

    testPermissionAfterSettings() {
        console.log('Testing permission after settings change...');

        const permission = Notification.permission;
        console.log('Current permission:', permission);

        if (permission === 'granted') {
            this.dismissModal();
            this.handlePermissionGranted();
            this.showMobileSuccessMessage();

            // Send a test notification
            setTimeout(() => {
                this.sendTestNotification();
            }, 1000);
        } else {
            this.showToast('Notifications still not enabled. Please check your browser settings.', 'warning');
        }
    }

    openNotificationSettings() {
        const isAndroid = this.isAndroidDevice();
        const isIOS = this.isIOSDevice();

        if (isAndroid) {
            // For Android Chrome, we can try to open site settings
            this.showToast('Tap the 🔒 icon in the address bar, then go to Permissions → Notifications', 'info');
        } else if (isIOS) {
            // For iOS, guide to Safari settings
            this.showToast('Go to Settings → Safari → Notifications to enable', 'info');
        } else {
            // Generic instruction
            this.showToast('Please check your browser notification settings', 'info');
        }

        // Keep modal open so user can test after changing settings
    }

    async handlePermissionGranted() {
        try {
            console.log('Permission granted, setting up notifications...');

            // Generate a demo token for testing
            const token = 'demo_token_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
            this.deviceToken = token;

            console.log('Demo FCM token generated:', token);

            // Try to send token to server
            try {
                await this.sendTokenToServer(token);
                console.log('Token sent to server successfully');

                // Test notification immediately to verify it works
                this.testNotificationAfterSetup();

            } catch (error) {
                console.log('Could not send token to server:', error);
                // Still proceed with local testing
                this.testNotificationAfterSetup();
            }
        } catch (error) {
            console.error('Error handling permission granted:', error);
            throw error; // Re-throw to be caught by permission request
        }
    }

    testNotificationAfterSetup() {
        // Test that notifications actually work
        setTimeout(() => {
            try {
                const testNotification = new Notification('✅ Notifications Enabled!', {
                    body: 'Great! Notifications are now working on this device.',
                    icon: '/favicon.ico',
                    tag: 'setup-test',
                    requireInteraction: false
                });

                testNotification.onclick = () => {
                    console.log('Setup test notification clicked');
                    testNotification.close();
                };

                console.log('Setup test notification sent successfully');
            } catch (error) {
                console.error('Failed to send setup test notification:', error);
            }
        }, 1000);
    }

    async sendTokenToServer(token) {
        try {
            // Get device information
            const deviceInfo = this.getDeviceInfo();

            const response = await fetch('/librarian/save-device-token/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken(),
                },
                body: JSON.stringify({
                    token: token,
                    device_type: 'web',
                    device_name: deviceInfo.name
                })
            });

            if (!response.ok) {
                throw new Error('Failed to save device token');
            }

            const result = await response.json();
            console.log('Device token saved successfully:', result);

            if (result.device_info) {
                console.log(`Device registered: ${result.device_info.device_name}`);
                console.log(`Total devices for user: ${result.device_info.total_devices}`);

                // Show success message with device info
                this.showToast(
                    `📱 Device registered: ${result.device_info.device_name} (${result.device_info.total_devices} total devices)`,
                    'success'
                );
            }

        } catch (error) {
            console.error('Error saving device token:', error);
            this.showToast('⚠️ Could not register device for notifications', 'warning');
        }
    }

    getDeviceInfo() {
        const userAgent = navigator.userAgent;
        let deviceName = 'Unknown Browser';

        // Detect browser
        if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
            deviceName = 'Chrome';
        } else if (userAgent.includes('Firefox')) {
            deviceName = 'Firefox';
        } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
            deviceName = 'Safari';
        } else if (userAgent.includes('Edg')) {
            deviceName = 'Edge';
        } else if (userAgent.includes('Opera')) {
            deviceName = 'Opera';
        }

        // Add device type
        if (/Android/i.test(userAgent)) {
            deviceName += ' (Android)';
        } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
            deviceName += ' (iOS)';
        } else if (/Windows/i.test(userAgent)) {
            deviceName += ' (Windows)';
        } else if (/Mac/i.test(userAgent)) {
            deviceName += ' (Mac)';
        } else if (/Linux/i.test(userAgent)) {
            deviceName += ' (Linux)';
        }

        // Add mobile/desktop
        if (/Mobile|Android|iPhone|iPad/i.test(userAgent)) {
            if (!deviceName.includes('Android') && !deviceName.includes('iOS')) {
                deviceName += ' Mobile';
            }
        } else {
            deviceName += ' Desktop';
        }

        return {
            name: deviceName,
            userAgent: userAgent,
            platform: navigator.platform,
            language: navigator.language
        };
    }

    setupMessageHandlers() {
        // For demo mode, just set up basic handlers
        console.log('Setting up demo message handlers...');

        // Listen for messages from service worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                console.log('Message from service worker:', event.data);
                if (event.data && event.data.type === 'NOTIFICATION_CLICKED') {
                    console.log('Notification was clicked');
                }
            });
        }

        // Set up visibility change handler for background notifications
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                console.log('Page hidden - background notifications active');
            } else {
                console.log('Page visible - foreground notifications active');
            }
        });
    }

    showCustomNotification(payload) {
        const { title, body, icon } = payload.notification || {};
        
        // Create custom in-app notification
        const notification = document.createElement('div');
        notification.className = 'custom-notification';
        notification.innerHTML = `
            <div class="custom-notification-content">
                <div class="custom-notification-icon">${icon || '🔔'}</div>
                <div class="custom-notification-text">
                    <div class="custom-notification-title">${title || 'Notification'}</div>
                    <div class="custom-notification-body">${body || ''}</div>
                </div>
                <button class="custom-notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Add notification styles if not present
        this.addNotificationStyles();

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
    }

    addNotificationStyles() {
        if (document.getElementById('custom-notification-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'custom-notification-styles';
        styles.textContent = `
            .custom-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10001;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }

            .custom-notification.show {
                transform: translateX(0);
            }

            .custom-notification-content {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                padding: 16px;
                max-width: 300px;
                display: flex;
                align-items: flex-start;
                gap: 12px;
            }

            .custom-notification-icon {
                font-size: 24px;
                flex-shrink: 0;
            }

            .custom-notification-text {
                flex: 1;
            }

            .custom-notification-title {
                font-weight: 600;
                color: #333;
                margin-bottom: 4px;
            }

            .custom-notification-body {
                color: #666;
                font-size: 14px;
                line-height: 1.4;
            }

            .custom-notification-close {
                background: none;
                border: none;
                font-size: 18px;
                color: #999;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .custom-notification-close:hover {
                color: #666;
            }
        `;
        
        document.head.appendChild(styles);
    }

    dismissModal() {
        const modal = document.querySelector('.notification-permission-modal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    showSuccessMessage() {
        this.showToast('🎉 Notifications enabled! You\'ll now receive important updates.', 'success');
    }

    showMobileSuccessMessage() {
        const isIOS = this.isIOSDevice();
        const message = isIOS
            ? '🎉 Notifications enabled! For best results, add this site to your Home Screen.'
            : '🎉 Mobile notifications enabled! You\'ll receive updates even when the app is closed.';

        this.showToast(message, 'success');
    }

    showPermissionDenied() {
        this.showToast('ℹ️ Notifications are disabled. You can enable them in your browser settings.', 'info');
    }

    showNotificationNotSupported() {
        this.showToast('⚠️ Your browser doesn\'t support notifications.', 'warning');
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        // Add toast styles
        this.addToastStyles();
        
        document.body.appendChild(toast);
        
        setTimeout(() => toast.classList.add('show'), 100);
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 4000);
    }

    addToastStyles() {
        if (document.getElementById('toast-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'toast-styles';
        styles.textContent = `
            .toast {
                position: fixed;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%) translateY(100px);
                background: #333;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 10002;
                opacity: 0;
                transition: all 0.3s ease;
                max-width: 90%;
                text-align: center;
            }

            .toast.show {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }

            .toast-success { background: #28a745; }
            .toast-info { background: #17a2b8; }
            .toast-warning { background: #ffc107; color: #333; }
        `;
        
        document.head.appendChild(styles);
    }

    getCSRFToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }

    // Public method to trigger test notification
    async sendTestNotification() {
        try {
            // For demo purposes, show a simulated notification immediately
            this.showCustomNotification({
                notification: {
                    title: '📝 New QR Registration Received',
                    body: 'Hi Admin, a new student registration has been submitted via QR code.\n\nStudent Details:\n• Name: Test Student\n• Email: <EMAIL>\n• Mobile: 9876543210\n• Course: Computer Science\n• Date: ' + new Date().toLocaleDateString(),
                    icon: '📝'
                }
            });

            this.showToast('✅ Demo notification displayed!', 'success');

            // Also try to send real notification if backend is available
            try {
                const response = await fetch('/notifications/api/send/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCSRFToken(),
                    },
                    body: JSON.stringify({
                        event_type: 'qr_registration',
                        recipient_id: 1,
                        context_data: {
                            student_name: 'Test Student',
                            student_email: '<EMAIL>',
                            student_mobile: '9876543210',
                            course: 'Computer Science',
                            library_name: 'Test Library',
                            registration_date: new Date().toISOString().split('T')[0],
                            temp_student_id: 999
                        }
                    })
                });

                const result = await response.json();

                if (result.success) {
                    this.showToast('✅ Real notification also sent to backend!', 'success');
                }
            } catch (backendError) {
                console.log('Backend notification failed (expected in demo):', backendError);
            }

        } catch (error) {
            console.error('Error sending test notification:', error);
            this.showToast('❌ Error sending test notification', 'warning');
        }
    }
}

// Initialize the notification handler
const notificationHandler = new NotificationHandler();

// Add global function for easy testing
window.sendTestNotification = () => notificationHandler.sendTestNotification();
