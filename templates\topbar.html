<style>
    .modern-topbar {
        height: var(--topbar-height);
        padding: 0 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        -webkit-backdrop-filter: var(--glass-backdrop);
        border-bottom: 1px solid var(--glass-border);
        box-shadow: var(--glass-shadow);
        transition: all 0.3s ease;
    }

    /* Dark Mode Topbar */
    body.dark-mode .modern-topbar {
        background: rgba(17, 24, 39, 0.95);
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .topbar-left {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .mobile-menu-btn {
        display: none;
        background: none;
        border: none;
        font-size: 1.25rem;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 0.5rem;
        border-radius: var(--border-radius);
        transition: var(--transition);
    }

    /* Dark Mode Topbar Elements */
    body.dark-mode .mobile-menu-btn {
        color: rgba(255, 255, 255, 0.9) !important;
    }

    body.dark-mode .mobile-menu-btn:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
    }

    body.dark-mode .topbar-brand {
        color: white !important;
    }

    body.dark-mode .topbar-nav .nav-link {
        color: rgba(255, 255, 255, 0.8) !important;
    }

    body.dark-mode .topbar-nav .nav-link:hover {
        color: white !important;
    }

    body.dark-mode .user-dropdown .dropdown-toggle {
        color: white !important;
    }

    body.dark-mode .notification-btn {
        color: rgba(255, 255, 255, 0.8) !important;
    }

    body.dark-mode .notification-btn:hover {
        color: white !important;
    }

    /* Dark Mode Page Title and Breadcrumbs */
    body.dark-mode .page-title {
        color: white !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    body.dark-mode .breadcrumb-modern .breadcrumb-item {
        color: rgba(255, 255, 255, 0.7) !important;
    }

    body.dark-mode .breadcrumb-modern .breadcrumb-item.active {
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: 500;
    }

    body.dark-mode .breadcrumb-modern .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.7) !important;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    body.dark-mode .breadcrumb-modern .breadcrumb-item a:hover {
        color: white !important;
    }

    body.dark-mode .breadcrumb-modern .breadcrumb-item + .breadcrumb-item::before {
        color: rgba(255, 255, 255, 0.5) !important;
    }

    /* Dark Mode Action Buttons */
    body.dark-mode .action-btn {
        color: rgba(255, 255, 255, 0.8) !important;
    }

    body.dark-mode .action-btn:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
    }

    /* Dark Mode Dropdown Styles */
    body.dark-mode .dropdown-menu {
        background: rgba(17, 24, 39, 0.95) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        -webkit-backdrop-filter: blur(20px) !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    }

    body.dark-mode .dropdown-item {
        color: rgba(255, 255, 255, 0.9) !important;
        transition: all 0.3s ease;
    }

    body.dark-mode .dropdown-item:hover,
    body.dark-mode .dropdown-item:focus {
        background: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
    }

    body.dark-mode .dropdown-divider {
        border-color: rgba(255, 255, 255, 0.2) !important;
    }

    body.dark-mode .dropdown-header {
        color: rgba(255, 255, 255, 0.8) !important;
    }

    /* Dark Mode Notification Dropdown */
    body.dark-mode .notification-dropdown {
        background: #1f2937 !important;
        border-color: #374151 !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
    }

    body.dark-mode .notification-header {
        background: rgba(31, 41, 55, 0.8) !important;
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;
    }

    body.dark-mode .notification-header h6 {
        color: white !important;
    }

    body.dark-mode .notification-item {
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;
    }

    body.dark-mode .notification-item:hover {
        background: rgba(255, 255, 255, 0.05) !important;
    }

    body.dark-mode .notification-content h6 {
        color: rgba(255, 255, 255, 0.95) !important;
    }

    body.dark-mode .notification-content p {
        color: rgba(255, 255, 255, 0.8) !important;
    }

    body.dark-mode .notification-content small {
        color: rgba(255, 255, 255, 0.6) !important;
    }

    body.dark-mode .notification-footer {
        background: rgba(31, 41, 55, 0.8) !important;
        border-top-color: rgba(255, 255, 255, 0.1) !important;
    }

    body.dark-mode .notification-footer .btn-link {
        color: rgba(255, 255, 255, 0.9) !important;
    }

    /* Settings Dropdown - Light Mode */
    .settings-dropdown {
        background: #ffffff !important;
        border: 1px solid #e5e7eb !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
    }

    /* Dark Mode Settings Dropdown */
    body.dark-mode .settings-dropdown {
        background: #1f2937 !important;
        border-color: #374151 !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
    }

    body.dark-mode .settings-header {
        background: rgba(31, 41, 55, 0.8) !important;
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;
    }

    body.dark-mode .settings-header h6 {
        color: white !important;
    }

    body.dark-mode .setting-item {
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;
    }

    body.dark-mode .setting-item span {
        color: rgba(255, 255, 255, 0.9) !important;
    }

    body.dark-mode .setting-link {
        color: rgba(255, 255, 255, 0.8) !important;
    }

    body.dark-mode .setting-link:hover {
        color: white !important;
    }

    /* Dark Mode User Dropdown */
    body.dark-mode .user-dropdown {
        background: #1f2937 !important;
        border-color: #374151 !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
    }

    body.dark-mode .user-info {
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;
    }

    body.dark-mode .user-name {
        color: white !important;
    }

    body.dark-mode .user-role {
        color: rgba(255, 255, 255, 0.7) !important;
    }

    body.dark-mode .dropdown-item-modern {
        color: rgba(255, 255, 255, 0.9) !important;
    }

    body.dark-mode .dropdown-item-modern:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
    }

    body.dark-mode .dropdown-divider-modern {
        background: rgba(255, 255, 255, 0.2) !important;
    }

    /* Dark Mode Search */
    body.dark-mode .search-input {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
    }

    body.dark-mode .search-input::placeholder {
        color: rgba(255, 255, 255, 0.6) !important;
    }

    body.dark-mode .search-input:focus {
        background: rgba(255, 255, 255, 0.15) !important;
        border-color: var(--primary) !important;
        color: white !important;
    }

    body.dark-mode .search-icon {
        color: rgba(255, 255, 255, 0.6) !important;
    }

    body.dark-mode .search-results {
        background: rgba(17, 24, 39, 0.95) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        -webkit-backdrop-filter: blur(20px) !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    }

    body.dark-mode .search-result-item {
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.9) !important;
    }

    body.dark-mode .search-result-item:hover {
        background: rgba(255, 255, 255, 0.05) !important;
    }

    body.dark-mode .search-result-item .fw-semibold {
        color: white !important;
    }

    body.dark-mode .search-result-item .text-muted {
        color: rgba(255, 255, 255, 0.6) !important;
    }

    .mobile-menu-btn:hover {
        background: var(--gray-100);
        color: var(--gray-900);
    }

    .page-title {
        font-family: 'Comfortaa', sans-serif;
        font-weight: 600;
        font-size: 1.5rem;
        color: var(--gray-900);
        margin: 0;
    }

    .breadcrumb-modern {
        background: none;
        padding: 0;
        margin: 0;
        font-size: 0.875rem;
    }

    .breadcrumb-modern .breadcrumb-item {
        color: var(--gray-500);
    }

    .breadcrumb-modern .breadcrumb-item a {
        color: rgba(217, 217, 236, 0.9) !important;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .breadcrumb-modern .breadcrumb-item a:hover {
        color: rgba(217, 217, 236, 1) !important;
    }

    .breadcrumb-modern .breadcrumb-item.active {
        color: var(--gray-700);
        font-weight: 500;
    }

    .breadcrumb-modern .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: var(--gray-400);
    }

    .topbar-right {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .search-container {
        position: relative;
        display: none;
    }

    .search-input {
        width: 300px;
        padding: 0.5rem 1rem 0.5rem 2.5rem;
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius-lg);
        background: var(--gray-50);
        font-size: 0.875rem;
        transition: var(--transition);
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
    }

    .search-results {
        position: absolute;
        top: calc(100% + 0.5rem);
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        max-height: 400px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }

    .search-result-item {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--gray-200);
        cursor: pointer;
        transition: var(--transition);
    }

    .search-result-item:hover {
        background: var(--gray-50);
    }

    .search-result-item:last-child {
        border-bottom: none;
    }

    .search-icon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-400);
        font-size: 0.875rem;
    }

    .topbar-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .action-btn {
        position: relative;
        background: none;
        border: none;
        width: 40px;
        height: 40px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--gray-600);
        cursor: pointer;
        transition: var(--transition);
    }

    .action-btn:hover {
        background: var(--gray-100);
        color: var(--gray-900);
    }

    .action-btn .badge {
        position: absolute;
        top: -8px;
        right: -2px;
        min-width: 25px;
        min-height: 25px;
        width: 25px;
        height: 25px;
        background: var(--danger);
        border-radius: 50% !important;
        border: 2px solid white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.65rem;
        font-weight: 600;
        color: white;
        line-height: 1 !important;
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
        z-index: 10;
        padding: 0.5rem 0 0 0 !important;
        margin: 0 !important;
        white-space: nowrap;
        text-align: center;
        vertical-align: middle;
    }

    /* Ensure perfect circle for single digits */
    .action-btn .badge:not([data-multi-digit]) {
        width: 18px !important;
        height: 18px !important;
        min-width: 18px !important;
        min-height: 18px !important;
        padding: 3px 0 0 0  !important;
        border-radius: 50% !important;
    }

    /* Two digit numbers - slightly larger but still round */
    .action-btn .badge[data-multi-digit="2"] {
        width: 22px !important;
        height: 22px !important;
        min-width: 22px !important;
        min-height: 22px !important;
        font-size: 0.6rem;
        padding: 0 !important;
        border-radius: 50% !important;
    }

    /* Three digit numbers - larger but still round */
    .action-btn .badge[data-multi-digit="3"] {
        width: 26px !important;
        height: 26px !important;
        min-width: 26px !important;
        min-height: 26px !important;
        font-size: 0.55rem;
        padding: 0 !important;
        border-radius: 50% !important;
    }

    .user-menu {
        position: relative;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: var(--border-radius);
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        border: 2px solid transparent;
    }

    .user-avatar:hover {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
    }

    .user-dropdown {
        position: absolute;
        top: calc(100% + 0.5rem);
        right: 0;
        width: 280px;
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: var(--border-radius-lg);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: var(--transition);
        z-index: 1000;
    }

    .user-dropdown.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .user-info {
        padding: 1.5rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .user-name {
        font-weight: 600;
        color: var(--gray-900);
        margin: 0 0 0.25rem 0;
    }

    .user-role {
        font-size: 0.875rem;
        color: var(--gray-500);
        margin: 0;
    }

    .dropdown-menu-modern {
        padding: 0.5rem 0;
    }

    .dropdown-item-modern {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1.5rem;
        color: var(--gray-700);
        text-decoration: none;
        transition: var(--transition);
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        font-size: 0.875rem;
    }

    .dropdown-item-modern:hover {
        background: var(--gray-50);
        color: var(--gray-900);
    }

    .dropdown-item-modern i {
        width: 16px;
        text-align: center;
    }

    .dropdown-divider-modern {
        height: 1px;
        background: var(--gray-200);
        margin: 0.5rem 0;
    }

    /* Notification Dropdown */
    .notification-dropdown {
        position: absolute;
        top: calc(100% + 0.5rem);
        right: 0;
        width: 350px;
        background: #ffffff !important;
        border: 1px solid #e5e7eb !important;
        border-radius: var(--border-radius-lg);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: var(--transition);
        z-index: 1000;
        max-height: 500px;
        overflow: hidden;
    }

    .notification-dropdown.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .notification-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: var(--gray-50);
    }

    .notification-header h6 {
        color: #111827 !important;
        font-weight: 600;
    }

    /* Style the Mark all as read button */
    .notification-header .btn-link {
        background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
        color: white !important;
        text-decoration: none !important;
        padding: 0.375rem 0.75rem !important;
        border-radius: var(--border-radius) !important;
        font-size: 0.8125rem !important;
        font-weight: 500 !important;
        border: none !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3) !important;
    }

    .notification-header .btn-link:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.4) !important;
        color: white !important;
        text-decoration: none !important;
    }

    .notification-header .btn-link:active {
        transform: translateY(0) !important;
        box-shadow: 0 2px 6px rgba(var(--primary-rgb), 0.3) !important;
    }

    .notification-list {
        max-height: 350px;
        overflow-y: auto;
    }

    .notification-item {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--gray-200);
        transition: var(--transition);
        cursor: pointer;
    }

    .notification-item:hover {
        background: var(--gray-50);
    }

    .notification-item:last-child {
        border-bottom: none;
    }

    .notification-icon {
        width: 32px;
        height: 32px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.75rem;
        flex-shrink: 0;
    }

    .notification-content h6 {
        font-size: 0.875rem;
        font-weight: 600;
        margin: 0 0 0.25rem 0;
        color: #111827 !important;
    }

    .notification-content p {
        font-size: 0.8125rem;
        color: #6b7280 !important;
        margin: 0 0 0.25rem 0;
    }

    .notification-content small {
        font-size: 0.75rem;
        color: #9ca3af !important;
    }

    .notification-footer {
        padding: 0.75rem 1.5rem;
        border-top: 1px solid var(--gray-200);
        background: var(--gray-50);
        text-align: center;
    }





    /* Settings Dropdown */
    .settings-dropdown {
        position: absolute;
        top: calc(100% + 0.5rem);
        right: 0;
        width: 280px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius-lg);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: var(--transition);
        z-index: 1000;
    }

    .settings-dropdown.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .settings-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--gray-200);
        background: var(--gray-50);
    }

    .settings-header h6 {
        color: #111827 !important;
        font-weight: 600;
    }

    .setting-item {
        padding: 0.75rem 1.5rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .setting-item:last-child {
        border-bottom: none;
    }

    .setting-item span {
        color: #111827 !important;
        font-weight: 500;
    }

    .setting-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .setting-link {
        display: flex;
        align-items: center;
        color: #374151 !important;
        text-decoration: none;
        transition: var(--transition);
    }

    .setting-link:hover {
        color: var(--primary) !important;
    }

    .form-check-input {
        margin-left: 0.5rem !important;
    }

    .form-check-input:checked {
        background-color: var(--primary);
        border-color: var(--primary);
    }

    /* Mobile Responsive */
    @media (max-width: 991.98px) {
        .mobile-menu-btn {
            display: flex;
        }
        
        .modern-topbar {
            padding: 0 1rem;
        }
        
        .search-container {
            display: none !important;
        }
    }

    @media (max-width: 767.98px) {
        .page-title {
            font-size: 1.25rem;
        }

        .breadcrumb-modern {
            display: none;
        }

        .topbar-actions {
            gap: 0.25rem;
        }

        .action-btn {
            width: 36px;
            height: 36px;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
        }

        .user-dropdown {
            width: 260px;
        }

        /* Mobile dropdown positioning fixes */
        .notification-dropdown {
            width: 300px;
            right: -10px;
            left: auto;
            transform: translateX(0);
        }

        .settings-dropdown {
            width: 300px;
            right: -10px;
            left: auto;
            transform: translateX(0);
        }

        /* Ensure dropdowns don't go off screen on mobile */
        .notification-dropdown.show {
            transform: translateY(0) translateX(0);
        }

        .settings-dropdown.show {
            transform: translateY(0) translateX(0);
        }

        /* Adjust dropdown positioning for very small screens */
        @media (max-width: 480px) {
            .notification-dropdown {
                width: calc(100vw - 40px);
                right: -25px;
                max-width: 300px;
            }

            .settings-dropdown {
                width: calc(100vw - 40px);
                right: -20px;
                max-width: 220px;
            }
        }
    }
</style>

<div class="modern-topbar">
    <div class="topbar-left">
        <!-- Mobile Menu Button -->
        <button class="mobile-menu-btn d-lg-none" id="mobile-menu-btn" type="button">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Page Title and Breadcrumb -->
        <div>
            <h1 class="page-title" id="page-title">
                {% block page_title %}Dashboard{% endblock %}
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-modern" id="breadcrumb-nav">
                    {% block breadcrumb %}
                    <li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
                    {% endblock %}
                </ol>
            </nav>
        </div>
    </div>

    <div class="topbar-right">
        <!-- Search Bar (Desktop Only) -->
        <div class="search-container d-none d-lg-block">
            <div class="position-relative">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search..." id="global-search">
                <div class="search-results" id="search-results"></div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="topbar-actions">
            <!-- Notifications -->
            <div class="position-relative">
                <button class="action-btn" type="button" onclick="toggleNotifications()">
                    <i class="fas fa-bell"></i>
                    <span class="badge" id="notification-badge" style="display: none;"></span>
                </button>
                <div class="notification-dropdown" id="notification-dropdown">
                    <div class="notification-header">
                        <h6 class="mb-0">Notifications</h6>
                        <button class="btn-link" onclick="markAllAsRead()">Mark all as read</button>
                    </div>
                    <div class="notification-list" id="notification-list">
                        <div class="notification-item">
                            <div class="notification-icon bg-success">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="notification-content">
                                <h6>New Student Registration</h6>
                                <p>John Doe has registered for Morning Shift</p>
                                <small>2 minutes ago</small>
                            </div>
                        </div>
                        <div class="notification-item">
                            <div class="notification-icon bg-primary">
                                <i class="fas fa-rupee-sign"></i>
                            </div>
                            <div class="notification-content">
                                <h6>Payment Received</h6>
                                <p>₹2,500 payment from Jane Smith</p>
                                <small>15 minutes ago</small>
                            </div>
                        </div>
                        <div class="notification-item">
                            <div class="notification-icon bg-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="notification-content">
                                <h6>Pending Approval</h6>
                                <p>3 students waiting for approval</p>
                                <small>1 hour ago</small>
                            </div>
                        </div>
                    </div>
                    <div class="notification-footer">
                        <a href="/{{ role }}/notifications/" class="btn-link">View all notifications</a>
                    </div>
                </div>
            </div>

            <!-- Settings -->
            <div class="position-relative">
                <button class="action-btn" type="button" onclick="toggleSettings()">
                    <i class="fas fa-cog"></i>
                </button>
                <div class="settings-dropdown" id="settings-dropdown">
                    <div class="settings-header">
                        <h6 class="mb-0">Settings</h6>
                    </div>
                    <div class="settings-list">
                        <div class="setting-item">
                            <div class="setting-content">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Dark Mode</span>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="darkModeToggle" onchange="toggleDarkMode()">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-content">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Notifications</span>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input " type="checkbox" id="notificationToggle" checked onchange="toggleNotificationSettings()">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="setting-item">
                            <a href="/{{ role }}/settings/" class="setting-link">
                                <i class="fas fa-sliders-h me-2"></i>
                                Advanced Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Menu -->
        <div class="user-menu">
            <div class="user-avatar" onclick="toggleUserDropdown()">
                {% if user.is_authenticated %}{{ user.username.0|upper }}{% else %}U{% endif %}
            </div>

            <div class="user-dropdown" id="user-dropdown">
                <div class="user-info">
                    <h6 class="user-name">{% if user.is_authenticated %}{{ user.username }}{% else %}User{% endif %}</h6>
                    <p class="user-role">{{ role|title|default:"User" }}</p>
                </div>
                
                <div class="dropdown-menu-modern">
                    <a href="/{{ role }}/profile/" class="dropdown-item-modern">
                        <i class="fas fa-user"></i>
                        <span>View Profile</span>
                    </a>
                    <a href="/membership/plans/" class="dropdown-item-modern">
                        <i class="fas fa-crown"></i>
                        <span>Membership</span>
                    </a>

                    <div class="dropdown-divider-modern"></div>

                    <a href="/{{ role }}/help/" class="dropdown-item-modern">
                        <i class="fas fa-question-circle"></i>
                        <span>Help & Support</span>
                    </a>

                    <div class="dropdown-divider-modern"></div>

                    <button onclick="logout()" class="dropdown-item-modern">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // User dropdown functionality
    function toggleUserDropdown() {
        const dropdown = document.getElementById('user-dropdown');
        dropdown.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const userMenu = document.querySelector('.user-menu');
        const dropdown = document.getElementById('user-dropdown');
        
        if (!userMenu.contains(event.target)) {
            dropdown.classList.remove('show');
        }
    });

    // Logout function
    function logout() {
        if (confirm('Are you sure you want to logout?')) {
            window.location.href = '/{{ role }}/logout/';
        }
    }

    // Enhanced Search functionality
    let searchTimeout;
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('global-search');
        const searchResults = document.getElementById('search-results');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();

                clearTimeout(searchTimeout);

                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        performSearch(query);
                    }, 300);
                } else {
                    hideSearchResults();
                }
            });

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const query = this.value.trim();
                    if (query) {
                        performSearch(query);
                    }
                }
            });

            // Hide search results when clicking outside
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                    hideSearchResults();
                }
            });
        }
    });

    function performSearch(query) {
        const searchResults = document.getElementById('search-results');

        // Show loading state
        searchResults.innerHTML = '<div class="search-result-item"><i class="fas fa-spinner fa-spin me-2"></i>Searching...</div>';
        searchResults.style.display = 'block';

        // Try actual API call first
        fetch(`/api/search/?q=${encodeURIComponent(query)}`, {
            method: 'GET',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || '',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            }
            throw new Error('API not available');
        })
        .then(data => {
            displaySearchResults(data.results || []);
        })
        .catch(error => {
            // Generate dynamic mock results based on query
            const dynamicResults = generateDynamicResults(query);
            displaySearchResults(dynamicResults);
        });
    }

    function generateDynamicResults(query) {
        const results = [];
        const lowerQuery = query.toLowerCase();

        // Student results
        if (lowerQuery.includes('student') || lowerQuery.includes('john') || lowerQuery.includes('jane')) {
            results.push(
                { type: 'student', title: 'John Doe', subtitle: 'Student ID: ST001 - Morning Shift', url: '/students/1/' },
                { type: 'student', title: 'Jane Smith', subtitle: 'Student ID: ST002 - Evening Shift', url: '/students/2/' }
            );
        }

        // Transaction results
        if (lowerQuery.includes('payment') || lowerQuery.includes('transaction') || lowerQuery.includes('₹') || lowerQuery.includes('money')) {
            results.push(
                { type: 'transaction', title: '₹2,500 Payment', subtitle: 'From Jane Smith - Today', url: '/transactions/1/' },
                { type: 'transaction', title: '₹1,800 Payment', subtitle: 'From John Doe - Yesterday', url: '/transactions/2/' }
            );
        }

        // Visitor results
        if (lowerQuery.includes('visitor') || lowerQuery.includes('visit')) {
            results.push(
                { type: 'visitor', title: 'Mike Johnson', subtitle: 'Visitor on 2024-01-15', url: '/visitors/1/' },
                { type: 'visitor', title: 'Sarah Wilson', subtitle: 'Visitor on 2024-01-14', url: '/visitors/2/' }
            );
        }

        // Analytics results
        if (lowerQuery.includes('analytics') || lowerQuery.includes('report') || lowerQuery.includes('chart')) {
            results.push(
                { type: 'analytics', title: 'Monthly Revenue Report', subtitle: 'January 2024 Analytics', url: '/analytics/revenue/' },
                { type: 'analytics', title: 'Student Growth Chart', subtitle: 'Enrollment Statistics', url: '/analytics/students/' }
            );
        }

        // Settings results
        if (lowerQuery.includes('setting') || lowerQuery.includes('config') || lowerQuery.includes('profile')) {
            results.push(
                { type: 'settings', title: 'User Profile', subtitle: 'Manage your account', url: '/profile/' },
                { type: 'settings', title: 'System Settings', subtitle: 'Configure dashboard', url: '/settings/' }
            );
        }

        // If no specific matches, show general results
        if (results.length === 0) {
            results.push(
                { type: 'student', title: `Search: "${query}"`, subtitle: 'No exact matches found', url: '#' },
                { type: 'general', title: 'View All Students', subtitle: 'Browse student directory', url: '/students/' },
                { type: 'general', title: 'View Transactions', subtitle: 'Browse payment history', url: '/transactions/' }
            );
        }

        return results.slice(0, 5); // Limit to 5 results
    }

    function displaySearchResults(results) {
        const searchResults = document.getElementById('search-results');

        if (results.length === 0) {
            searchResults.innerHTML = '<div class="search-result-item">No results found</div>';
            return;
        }

        const resultsHTML = results.map(result => `
            <div class="search-result-item" onclick="window.location.href='${result.url}'">
                <div class="d-flex align-items-center">
                    <i class="fas fa-${getSearchIcon(result.type)} me-2 text-primary"></i>
                    <div>
                        <div class="fw-semibold">${result.title}</div>
                        <small class="text-muted">${result.subtitle}</small>
                    </div>
                </div>
            </div>
        `).join('');

        searchResults.innerHTML = resultsHTML;
    }

    function getSearchIcon(type) {
        const icons = {
            'student': 'user',
            'transaction': 'money-bill',
            'visitor': 'user-friends',
            'analytics': 'chart-line',
            'settings': 'cog',
            'general': 'search',
            'default': 'search'
        };
        return icons[type] || icons.default;
    }

    function hideSearchResults() {
        const searchResults = document.getElementById('search-results');
        searchResults.style.display = 'none';
    }

    // Notification functionality
    function toggleNotifications() {
        const dropdown = document.getElementById('notification-dropdown');

        dropdown.classList.toggle('show');

        // Close settings dropdown if open
        const settingsDropdown = document.getElementById('settings-dropdown');
        settingsDropdown.classList.remove('show');

        // Mark notifications as read when opened
        if (dropdown.classList.contains('show')) {
            markNotificationsAsRead();
        }
    }

    function markAllAsRead() {
        const notificationItems = document.querySelectorAll('.notification-item');
        notificationItems.forEach(item => {
            item.style.opacity = '0.7';
        });

        // Hide notification badge
        const badge = document.getElementById('notification-badge');
        badge.style.display = 'none';

        // API call to mark as read
        fetch('/api/notifications/mark-read/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || '',
                'Content-Type': 'application/json'
            }
        }).catch(error => console.error('Error marking notifications as read:', error));
    }

    function markNotificationsAsRead() {
        // Hide notification badge
        const badge = document.getElementById('notification-badge');
        badge.style.display = 'none';
    }

    // Settings functionality
    function toggleSettings() {
        const dropdown = document.getElementById('settings-dropdown');
        dropdown.classList.toggle('show');

        // Close notification dropdown if open
        const notificationDropdown = document.getElementById('notification-dropdown');
        notificationDropdown.classList.remove('show');
    }

    // Dark mode functionality
    function toggleDarkMode() {
        const isDarkMode = document.getElementById('darkModeToggle').checked;

        if (isDarkMode) {
            document.body.classList.add('dark-mode');
            localStorage.setItem('darkMode', 'enabled');
        } else {
            document.body.classList.remove('dark-mode');
            localStorage.setItem('darkMode', 'disabled');
        }

        // Apply dark mode styles
        applyDarkModeStyles(isDarkMode);
    }

    function applyDarkModeStyles(isDarkMode) {
        const root = document.documentElement;

        // Update browser theme colors
        updateBrowserTheme(isDarkMode);

        if (isDarkMode) {
            // Dark mode color overrides
            root.style.setProperty('--bg-primary', '#111827');
            root.style.setProperty('--bg-secondary', '#1f2937');
            root.style.setProperty('--bg-tertiary', '#374151');
            root.style.setProperty('--text-primary', '#ffffff');
            root.style.setProperty('--text-secondary', '#d1d5db');
            root.style.setProperty('--text-muted', '#9ca3af');
            root.style.setProperty('--border-color', '#4b5563');
            root.style.setProperty('--shadow-color', 'rgba(0, 0, 0, 0.3)');

            // Dark glassmorphism
            root.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.05)');
            root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.1)');
            root.style.setProperty('--glass-shadow', '0 8px 32px rgba(0, 0, 0, 0.3)');

            // Dark gradients
            root.style.setProperty('--gradient-hero', 'linear-gradient(135deg, #1f2937 0%, #111827 100%)');
            root.style.setProperty('--gradient-glass', 'linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)');
        } else {
            // Light mode (reset to defaults)
            root.style.setProperty('--bg-primary', '#ffffff');
            root.style.setProperty('--bg-secondary', '#f9fafb');
            root.style.setProperty('--bg-tertiary', '#f3f4f6');
            root.style.setProperty('--text-primary', '#111827');
            root.style.setProperty('--text-secondary', '#4b5563');
            root.style.setProperty('--text-muted', '#9ca3af');
            root.style.setProperty('--border-color', '#e5e7eb');
            root.style.setProperty('--shadow-color', 'rgba(0, 0, 0, 0.1)');

            // Light glassmorphism
            root.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.15)');
            root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.3)');
            root.style.setProperty('--glass-shadow', '0 8px 32px rgba(0, 0, 0, 0.1)');

            // Light gradients
            root.style.setProperty('--gradient-hero', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
            root.style.setProperty('--gradient-glass', 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)');
        }
    }

    function updateBrowserTheme(isDarkMode) {
        // Update theme-color meta tag
        const themeColorMeta = document.getElementById('theme-color-meta');
        const statusBarMeta = document.getElementById('status-bar-meta');

        if (themeColorMeta) {
            themeColorMeta.setAttribute('content', isDarkMode ? '#111827' : '#6366f1');
        }

        if (statusBarMeta) {
            statusBarMeta.setAttribute('content', isDarkMode ? 'black-translucent' : 'default');
        }
    }

    // Notification settings
    function toggleNotificationSettings() {
        const isEnabled = document.getElementById('notificationToggle').checked;
        const notificationBtn = document.querySelector('.action-btn');

        if (isEnabled) {
            notificationBtn.style.opacity = '1';
            notificationBtn.style.pointerEvents = 'auto';
            localStorage.setItem('notificationsEnabled', 'true');
        } else {
            notificationBtn.style.opacity = '0.5';
            notificationBtn.style.pointerEvents = 'none';
            localStorage.setItem('notificationsEnabled', 'false');
        }
    }

    // Initialize settings on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize dark mode
        const darkModeEnabled = localStorage.getItem('darkMode') === 'enabled';
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.checked = darkModeEnabled;
        }
        if (darkModeEnabled) {
            document.body.classList.add('dark-mode');
            applyDarkModeStyles(true);
        }

        // Initialize notification settings
        const notificationsEnabled = localStorage.getItem('notificationsEnabled') !== 'false';
        document.getElementById('notificationToggle').checked = notificationsEnabled;
        if (!notificationsEnabled) {
            toggleNotificationSettings();
        }

        // Show notification badge if there are unread notifications
        setTimeout(() => {
            const badge = document.getElementById('notification-badge');
            const count = 3; // Replace with actual count
            badge.style.display = 'block';
            badge.textContent = count.toString();

            // Set data attribute for styling based on digit count
            const digitCount = count.toString().length;
            if (digitCount === 1) {
                badge.removeAttribute('data-multi-digit');
            } else {
                badge.setAttribute('data-multi-digit', digitCount.toString());
            }
        }, 1000);
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        const notificationDropdown = document.getElementById('notification-dropdown');
        const settingsDropdown = document.getElementById('settings-dropdown');
        const userMenu = document.querySelector('.user-menu');

        if (!event.target.closest('.topbar-actions') && !userMenu?.contains(event.target)) {
            notificationDropdown?.classList.remove('show');
            settingsDropdown?.classList.remove('show');
        }
    });

    // Dynamic Page Title and Breadcrumb Management
    function updatePageTitle() {
        const currentPath = window.location.pathname;
        const pageTitle = document.getElementById('page-title');
        const breadcrumbNav = document.getElementById('breadcrumb-nav');

        // Define page configurations
        const pageConfigs = {
            // Dashboard
            '/{{ role }}/dashboard/': {
                title: 'Dashboard',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Dashboard', url: null, active: true }
                ]
            },
            // Analytics
            '/{{ role }}/analytics/': {
                title: 'Analytics',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Analytics', url: null, active: true }
                ]
            },
            // Marketing - Multiple role support
            '/librarian/marketing/': {
                title: 'Marketing',
                breadcrumb: [
                    { text: 'Home', url: '/librarian/dashboard/', active: false },
                    { text: 'Marketing', url: null, active: true }
                ]
            },
            '/sublibrarian/marketing/': {
                title: 'Marketing',
                breadcrumb: [
                    { text: 'Home', url: '/sublibrarian/dashboard/', active: false },
                    { text: 'Marketing', url: null, active: true }
                ]
            },
            '/manager/marketing/': {
                title: 'Marketing',
                breadcrumb: [
                    { text: 'Home', url: '/manager/dashboard/', active: false },
                    { text: 'Marketing', url: null, active: true }
                ]
            },
            '/{{ role }}/marketing/': {
                title: 'Marketing',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Marketing', url: null, active: true }
                ]
            },
            // Marketing Notifications
            '/librarian/marketing-send-notification/': {
                title: 'Marketing Notifications',
                breadcrumb: [
                    { text: 'Home', url: '/librarian/dashboard/', active: false },
                    { text: 'Marketing', url: '/librarian/marketing/', active: false },
                    { text: 'Send Notifications', url: null, active: true }
                ]
            },
            // Help & Support
            '/{{ role }}/help/': {
                title: 'Help & Support',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Help & Support', url: null, active: true }
                ]
            },
            // Students
            '/students/': {
                title: 'Fees Due',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Fees Due', url: null, active: true }
                ]
            },
            // Add Student
            '/students/create/': {
                title: 'Add Student',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Add Student', url: null, active: true }
                ]
            },
            // Student Register
            '/{{ role }}/student-register/': {
                title: 'Student Register',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Student Register', url: null, active: true }
                ]
            },
            '/librarian/student-register/': {
                title: 'Student Register',
                breadcrumb: [
                    { text: 'Home', url: '/librarian/dashboard/', active: false },
                    { text: 'Student Register', url: null, active: true }
                ]
            },
            '/sublibrarian/student-register/': {
                title: 'Student Register',
                breadcrumb: [
                    { text: 'Home', url: '/sublibrarian/dashboard/', active: false },
                    { text: 'Student Register', url: null, active: true }
                ]
            },
            // Invoice Register
            '/{{ role }}/invoice-register/': {
                title: 'Invoice Register',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Invoice Register', url: null, active: true }
                ]
            },
            '/librarian/invoice-register/': {
                title: 'Invoice Register',
                breadcrumb: [
                    { text: 'Home', url: '/librarian/dashboard/', active: false },
                    { text: 'Invoice Register', url: null, active: true }
                ]
            },
            '/sublibrarian/invoice-register/': {
                title: 'Invoice Register',
                breadcrumb: [
                    { text: 'Home', url: '/sublibrarian/dashboard/', active: false },
                    { text: 'Invoice Register', url: null, active: true }
                ]
            },
            // Transactions
            '/{{ role }}/daily-transaction/': {
                title: 'Daily Transactions',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Transactions', url: null, active: true }
                ]
            },
            // Visitors
            '/visitors/': {
                title: 'Visitors',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Visitors', url: null, active: true }
                ]
            },
            // Profile
            '/{{ role }}/profile/': {
                title: 'Profile',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Profile', url: null, active: true }
                ]
            },
            // Settings
            '/{{ role }}/settings/': {
                title: 'Settings',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Settings', url: null, active: true }
                ]
            },
            // Blogs
            '/blogs/': {
                title: 'Blogs',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Blogs', url: null, active: true }
                ]
            },
            // Complaints (for library commander)
            '/{{ role }}/complaint_dashboard/': {
                title: 'Complaints',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Complaints', url: null, active: true }
                ]
            },
            // Membership
            '/membership/': {
                title: 'Membership',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Membership', url: null, active: true }
                ]
            },
            // Wallet
            '/wallet/': {
                title: 'Wallet',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Wallet', url: null, active: true }
                ]
            },
            // Add Sublibrarian
            '/sublibrarian/signup/': {
                title: 'Add Sublibrarian',
                breadcrumb: [
                    { text: 'Home', url: '/librarian/dashboard/', active: false },
                    { text: 'Add Sublibrarian', url: null, active: true }
                ]
            },
            // Register
            '/{{ role }}/register/': {
                title: 'Register',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Register', url: null, active: true }
                ]
            },
            // All Invoices (moved from table)
            '/{{ role }}/all-invoices/': {
                title: 'All Invoices',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'All Invoices', url: null, active: true }
                ]
            },
            '/librarian/all-invoices/': {
                title: 'All Invoices',
                breadcrumb: [
                    { text: 'Home', url: '/librarian/dashboard/', active: false },
                    { text: 'All Invoices', url: null, active: true }
                ]
            },
            '/sublibrarian/all-invoices/': {
                title: 'All Invoices',
                breadcrumb: [
                    { text: 'Home', url: '/sublibrarian/dashboard/', active: false },
                    { text: 'All Invoices', url: null, active: true }
                ]
            },
            '/manager/all-invoices/': {
                title: 'All Invoices',
                breadcrumb: [
                    { text: 'Home', url: '/manager/dashboard/', active: false },
                    { text: 'All Invoices', url: null, active: true }
                ]
            }
        };

        // Check for exact match first
        let config = pageConfigs[currentPath];

        // If no exact match, check for partial matches
        if (!config) {
            for (const path in pageConfigs) {
                if (currentPath.startsWith(path) && path !== '/{{ role }}/dashboard/') {
                    config = pageConfigs[path];
                    break;
                }
            }
        }

        // If still no config found, create a dynamic one based on URL
        if (!config) {
            config = generateDynamicPageConfig(currentPath);
        }

        // Apply configuration
        if (config) {
            // Update page title
            pageTitle.textContent = config.title;

            // Update breadcrumb
            let breadcrumbHTML = '';
            config.breadcrumb.forEach(item => {
                if (item.active) {
                    breadcrumbHTML += `<li class="breadcrumb-item active" aria-current="page">${item.text}</li>`;
                } else {
                    breadcrumbHTML += `<li class="breadcrumb-item"><a href="${item.url}">${item.text}</a></li>`;
                }
            });
            breadcrumbNav.innerHTML = breadcrumbHTML;
        }
    }

    // Generate dynamic page configuration for unknown pages
    function generateDynamicPageConfig(path) {
        const segments = path.split('/').filter(segment => segment && segment !== '{{ role }}');

        if (segments.length === 0) {
            return {
                title: 'Dashboard',
                breadcrumb: [
                    { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                    { text: 'Dashboard', url: null, active: true }
                ]
            };
        }

        // Get the last meaningful segment as the page title
        const lastSegment = segments[segments.length - 1];
        const title = lastSegment.replace(/[-_]/g, ' ')
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');

        return {
            title: title,
            breadcrumb: [
                { text: 'Home', url: '/{{ role }}/dashboard/', active: false },
                { text: title, url: null, active: true }
            ]
        };
    }

    // Make updatePageTitle globally available
    window.updatePageTitle = updatePageTitle;

    // Update page title on page load and navigation
    document.addEventListener('DOMContentLoaded', updatePageTitle);

    // Update on browser navigation (back/forward buttons)
    window.addEventListener('popstate', updatePageTitle);

    // Manual page title update function for external use
    window.setPageTitle = function(title, breadcrumbItems = null) {
        const pageTitle = document.getElementById('page-title');
        const breadcrumbNav = document.getElementById('breadcrumb-nav');

        if (pageTitle) {
            pageTitle.textContent = title;
        }

        if (breadcrumbItems && breadcrumbNav) {
            let breadcrumbHTML = '';
            breadcrumbItems.forEach(item => {
                if (item.active) {
                    breadcrumbHTML += `<li class="breadcrumb-item active" aria-current="page">${item.text}</li>`;
                } else {
                    breadcrumbHTML += `<li class="breadcrumb-item"><a href="${item.url}">${item.text}</a></li>`;
                }
            });
            breadcrumbNav.innerHTML = breadcrumbHTML;
        }
    };
</script>
