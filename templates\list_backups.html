<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="robots" content="noindex, nofollow">
    <title>Database Backups | Librainian</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">


  
        
        <!-- disable Print Screen for Windows -->
    
          
        
        <!-- disable print screen for mac -->
    
          
    
        <!-- disabling print screen for Linux -->
    
  
    
        <!-- disabling inspection tool -->
    
          
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --success-color: #10b981;
            --text-color: #1e293b;
            --text-light: #6b7280;
            --text-white: #ffffff;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 10px 20px rgba(0, 0, 0, 0.2);
            --shadow-glass: 0 8px 32px rgba(31, 38, 135, 0.37);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-gradient);
            color: var(--text-white);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Main container with glass effect */
        .main-container {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: var(--border-radius);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-glass);
            margin: 2rem auto;
            max-width: 900px;
            position: relative;
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }

        .main-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        /* Header section */
        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            padding: 2rem;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .back-button {
            position: absolute;
            left: 2rem;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
            padding: 0.75rem 1.25rem;
            border-radius: 50px;
            transition: var(--transition);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .back-button:hover {
            color: var(--text-white);
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-50%) translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 255, 255, 0.2);
        }

        .page-title {
            font-weight: 600;
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        /* Content section */
        .content-section {
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        .backup-list {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(8px);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--shadow-md);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .list-group-item {
            border: none;
            margin-bottom: 1rem;
            border-radius: 12px;
            background: rgba(59, 130, 246, 0.05);
            border: 1px solid rgba(59, 130, 246, 0.1);
            transition: var(--transition);
            padding: 1.5rem;
        }

        .list-group-item:hover {
            background: rgba(59, 130, 246, 0.1);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .file-info-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .file-icon {
            width: 3rem;
            height: 3rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-white);
            font-size: 1.25rem;
            flex-shrink: 0;
        }

        .file-details {
            flex: 1;
        }

        .file-name {
            font-size: 1.1rem;
            color: var(--secondary-color);
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .file-info {
            font-size: 0.9rem;
            color: var(--text-light);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-download {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            border: none;
            border-radius: 50px;
            padding: 0.75rem 1.5rem;
            font-size: 0.95rem;
            font-weight: 500;
            transition: var(--transition);
            color: var(--text-white);
            box-shadow: var(--shadow-sm);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-download:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
            color: var(--text-white);
        }

        .alert-no-files {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--text-light);
            padding: 3rem 2rem;
            text-align: center;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
        }

        .alert-no-files i {
            font-size: 3rem;
            color: var(--text-light);
            margin-bottom: 1rem;
            opacity: 0.6;
        }

        .alert-no-files h3 {
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
        }

        /* Animations */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .main-container {
                margin: 1rem;
                border-radius: 12px;
            }

            .header-section {
                padding: 1.5rem 1rem;
            }

            .back-button {
                position: static;
                transform: none;
                margin-bottom: 1rem;
                align-self: flex-start;
            }

            .page-title {
                font-size: 1.8rem;
            }

            .content-section {
                padding: 1rem;
            }

            .file-info-container {
                flex-direction: column;
                text-align: center;
                gap: 1.5rem;
            }

            .btn-download {
                width: 100%;
                justify-content: center;
            }
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus indicators */
        .back-button:focus-visible,
        .btn-download:focus-visible {
            outline: 2px solid var(--text-white);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header-section">
            <a href="/librarycommander/dashboard/" class="back-button">
                <i class="fas fa-arrow-left"></i>
                Back to Dashboard
            </a>
            <h1 class="page-title">
                <i class="fas fa-database me-2"></i>
                Database Backups
            </h1>
            <p class="subtitle">Access and download your database backup files securely</p>
        </div>

        <div class="content-section">
            <div class="backup-list">
                {% if files %}
                    {% for file in files %}
                        <div class="list-group-item">
                            <div class="file-info-container">
                                <div class="file-icon">
                                    <i class="fas fa-file-archive"></i>
                                </div>
                                <div class="file-details">
                                    <div class="file-name">{{ file }}</div>
                                    <div class="file-info">
                                        <i class="fas fa-calendar-alt me-1"></i>
                                        Created on {% now "F j, Y" %}
                                        <span class="ms-3">
                                            <i class="fas fa-shield-alt me-1"></i>
                                            Secure backup file
                                        </span>
                                    </div>
                                </div>
                                <a href="{% url 'download_backup' file %}"
                                   class="btn btn-download"
                                   aria-label="Download {{ file }}">
                                    <i class="fas fa-download"></i>
                                    Download
                                </a>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="alert-no-files">
                        <i class="fas fa-folder-open"></i>
                        <h3>No Backup Files Available</h3>
                        <p class="mb-0">No backup files are currently available. Backups will appear here once they are created.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading states to download buttons
            const downloadButtons = document.querySelectorAll('.btn-download');

            downloadButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Downloading...';
                    this.disabled = true;

                    // Re-enable button after 3 seconds (assuming download starts)
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.disabled = false;
                    }, 3000);
                });
            });

            // Add keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const backButton = document.querySelector('.back-button');
                    if (backButton) {
                        backButton.click();
                    }
                }
            });

            // Track page view
            if (typeof gtag === 'function') {
                gtag('event', 'backup_list_view', {
                    'event_category': 'Admin',
                    'event_label': 'Database Backups'
                });
            }
        });
    </script>
</body>
</html>