{% extends "base.html" %}

{% block title %}Marketing Campaign - Librainian{% endblock %}

{% block extra_css %}

      
    <style>
        /* Template-specific styles - CSS variables inherited from base.html */
            background: var(--bg-gradient);
            color: var(--text-color);
            min-height: 100vh;
            overflow-x: hidden;
        }
        /* Main content area */
        .main-content {
            margin-left: 0;
            padding: 1rem;
            transition: var(--transition);
            min-height: 100vh;
        }

        /* Glass card design */
        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: var(--border-radius);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-glass);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            border: none;
            padding: 2rem;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .card-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .card-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .card-body {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(8px);
            padding: 2rem;
            position: relative;
            z-index: 1;
        }
        /* Alert styles */
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(8px);
            position: relative;
            z-index: 1;
        }

        .alert-primary {
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary-color);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        /* Form styles */
        .form-control {
            border: 2px solid rgba(59, 130, 246, 0.1);
            border-radius: 12px;
            padding: 0.875rem 1rem;
            background: rgba(255, 255, 255, 0.9);
            transition: var(--transition);
            font-size: 0.95rem;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: var(--text-white);
        }

        .form-label {
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        /* Student cards */
        .student-card {
            background: rgba(59, 130, 246, 0.05);
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 12px;
            padding: 1rem;
            margin: 0.5rem;
            transition: var(--transition);
            width: 18rem;
        }

        .student-card:hover {
            background: rgba(59, 130, 246, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        /* Button styles */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            border-radius: 50px;
            padding: 0.875rem 2rem;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
            color: var(--text-white);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
            color: var(--text-white);
        }

        /* Section headers */
        .section-header {
            background: rgba(59, 130, 246, 0.05);
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .section-header h3 {
            color: var(--secondary-color);
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* TinyMCE container */
        .editor-container {
            border: 2px solid rgba(59, 130, 246, 0.1);
            border-radius: 12px;
            overflow: hidden;
            background: var(--text-white);
        }


        /* Responsive design */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 0.5rem;
            }

            .card-header {
                padding: 1.5rem 1rem;
            }

            .card-header h1 {
                font-size: 1.6rem;
            }

            .card-body {
                padding: 1.5rem 1rem;
            }

            .student-card {
                width: 100%;
                margin: 0.5rem 0;
            }

            .btn-primary {
                width: 100%;
                margin: 0.5rem 0;
            }
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus indicators */
        .btn-primary:focus-visible,
        .form-control:focus-visible {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }









    </style>
{% endblock %}

{% block content %}
<div class="marketing-notifications-content fade-in">
    <div class="main-content">
        <div class="card">
            <div class="card-header">
                <h1>
                    <i class="fas fa-bullhorn me-2"></i>
                    Marketing Campaign
                </h1>
                <p>Create and send notifications to selected students</p>
            </div>
            <div class="card-body">
                {% if messages %}
                    {% for message in messages %}
                    <div class="alert alert-primary alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                {% endif %}
                <form id="marketingForm" method="POST" action="/librarian/send-notifications/">
                    {% csrf_token %}
                    <div class="section-header">
                        <h3>
                            <i class="fas fa-users"></i>
                            Selected Students
                        </h3>
                        <div class="d-flex flex-wrap">
                            {% for student in selected_students %}
                            <div class="student-card">
                                <input type="hidden" name="student_ids[]" value="{{ student.id }}">
                                <p class="fw-bold text-center mb-2">
                                    <i class="fas fa-user me-2"></i>
                                    {{ student.name }}
                                </p>
                                <p class="text-muted text-center mb-0">
                                    <i class="fas fa-envelope me-2"></i>
                                    {{ student.email }}
                                </p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="row g-4">
                        <!-- Email Section -->
                        <div class="col-lg-6">
                            <div class="section-header">
                                <h3>
                                    <i class="fas fa-envelope"></i>
                                    Email Template
                                </h3>

                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-template me-2"></i>
                                        Select Template:
                                    </label>
                                    <select name="email_template" class="form-control" id="emailTemplateSelect">
                                        <option value="">Choose a template...</option>
                                        <option value="welcome">Welcome Email</option>
                                        <option value="reminder">Payment Reminder</option>
                                        <option value="announcement">Announcement</option>
                                        <option value="custom">Custom Template</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-heading me-2"></i>
                                        Email Subject:
                                    </label>
                                    <input type="text" name="email_subject" class="form-control" placeholder="Enter email subject">
                                </div>

                                <div class="mb-4">
                                    <label class="form-label">
                                        <i class="fas fa-edit me-2"></i>
                                        Email Content:
                                    </label>
                                    <div class="editor-container">
                                        <textarea id="emailContent" name="email_content" class="form-control"></textarea>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Send Email Notifications
                                </button>
                            </div>
                        </div>
                    </form>

                    <form id="smsForm" method="POST" action="/librarian/send-notifications/">
                        <!-- SMS Section -->
                        <div class="col-lg-6">
                            <div class="section-header">
                                <h3>
                                    <i class="fas fa-sms"></i>
                                    SMS Template
                                </h3>

                                {% csrf_token %}
                                {% for student in selected_students %}
                                    <input type="hidden" name="student_ids[]" value="{{ student.id }}">
                                {% endfor %}

                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-list me-2"></i>
                                        Select Template:
                                    </label>
                                    <select name="sms_template" class="form-control" id="smsTemplateSelect">
                                        <option value="">Choose SMS template...</option>
                                        {% for template_content in template_contents %}
                                        <option value="{{template_content}}">{{template_content}}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label">
                                        <i class="fas fa-comment-alt me-2"></i>
                                        SMS Content:
                                    </label>
                                    <textarea name="sms_content" class="form-control" rows="4" maxlength="160" disabled="true" placeholder="SMS content will appear here when you select a template"></textarea>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Maximum 160 characters for SMS
                                    </small>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-mobile-alt me-2"></i>
                                    Send SMS Notifications
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Initialize TinyMCE
        tinymce.init({
            selector: '#emailContent',
            plugins: 'advlist autolink lists link image charmap preview anchor',
            toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist outdent indent | link image',
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Handle email template selection
            document.getElementById('emailTemplateSelect').addEventListener('change', function(e) {
                if (e.target.value !== 'custom') {
                    loadEmailTemplate(e.target.value);
                }
            });

            // Handle SMS template selection
            document.getElementById('smsTemplateSelect').addEventListener('change', function(e) {
                const selectedOption = e.target.options[e.target.selectedIndex]; 
                var templateContentDict = {{ template_contents|safe }};
                document.querySelector('[name="sms_content"]').value = templateContentDict[selectedOption.value].content;
            });
        });

        function loadEmailTemplate(template) {
            const templates = {
                'welcome': {
                    subject: 'Welcome to Our Library',
                    content: `<p>Dear {student_name},</p><p>Welcome to our library!</p><p>Best regards,<br>Library Team</p>`
                },
                'reminder': {
                    subject: 'Payment Reminder',
                    content: `<p>Dear {student_name},</p><p>Reminder about your pending library fees.</p><p>Best regards,<br>Library Team</p>`
                },
                'announcement': {
                    subject: 'Important Announcement',
                    content: `<p>Dear {student_name},</p><p>We have an important announcement.</p><p>Best regards,<br>Library Team</p>`
                }
            };

            const selectedTemplate = templates[template];
            if (selectedTemplate) {
                document.querySelector('[name="email_subject"]').value = selectedTemplate.subject;
                tinymce.get('emailContent').setContent(selectedTemplate.content);
            }
        }

        
    </script>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.3/tinymce.min.js"></script>
{% endblock %}
