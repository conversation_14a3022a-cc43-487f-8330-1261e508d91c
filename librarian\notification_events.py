"""
Comprehensive Notification Events System
Handles all notification triggers for the LMS
"""

from django.utils import timezone
from django.db.models import Sum
from django.contrib.auth.models import User
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class NotificationEventManager:
    """Manages all notification events and triggers"""
    
    def __init__(self):
        self.notification_service = None
        try:
            from .notification_service import notification_service
            self.notification_service = notification_service
        except ImportError:
            logger.warning("Notification service not available")
    
    def send_notification(self, event_type, recipients, **kwargs):
        """Send notification using the notification service"""
        if not self.notification_service:
            logger.warning(f"Cannot send notification: {event_type}")
            return False
            
        try:
            # Send to multiple recipients if needed
            if hasattr(recipients, '__iter__') and not isinstance(recipients, (str, User)):
                # Handle QuerySet or list of recipients
                recipients = list(recipients)
            else:
                recipients = [recipients]

            for recipient in recipients:
                self.notification_service.send_notification(
                    event_type, recipient, **kwargs
                )
            return True
        except Exception as e:
            logger.error(f"Error sending notification {event_type}: {e}")
            return False

    # 1. VISITOR CALLBACK NOTIFICATIONS
    def check_visitor_callbacks_today(self):
        """Check for visitor callbacks scheduled for today"""
        try:
            from visitorsData.models import Visitor

            today = timezone.now().date()
            callbacks_today = Visitor.objects.filter(
                callback=today,
                status__in=['pending', 'Checked In', 'Checked Out']  # Active visitors
            )

            if callbacks_today.exists():
                # Get librarians to notify
                librarians = User.objects.filter(
                    groups__name__in=['Librarian', 'SubLibrarian']
                )

                for callback in callbacks_today:
                    self.send_notification(
                        'visitor_callback_due',
                        librarians,
                        visitor_name=callback.name,
                        visitor_mobile=str(callback.contact),
                        callback_date=callback.callback.strftime('%Y-%m-%d'),
                        visitor_id=callback.id
                    )

                logger.info(f"Sent {callbacks_today.count()} visitor callback notifications")
                return callbacks_today.count()

        except Exception as e:
            logger.error(f"Error checking visitor callbacks: {e}")
        return 0

    # 2. ADMISSION PROCESSED NOTIFICATIONS
    def notify_admission_processed(self, student, processed_by):
        """Notify when admission is processed by sublibrarian"""
        try:
            # Notify main librarians
            librarians = User.objects.filter(
                groups__name='Librarian'
            ).exclude(id=processed_by.id)
            
            self.send_notification(
                'admission_processed',
                librarians,
                student_name=student.name,
                student_email=student.email,
                processed_by=processed_by.username,
                admission_date=timezone.now().strftime('%Y-%m-%d'),
                student_id=student.id
            )
            
            logger.info(f"Admission processed notification sent for {student.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending admission processed notification: {e}")
            return False

    # 3. MEMBER EXPIRY NOTIFICATIONS
    def check_member_expiry_notifications(self):
        """Check for member expiry notifications (10, 5, 1 days, and expired)"""
        try:
            from studentsData.models import StudentsData
            
            today = timezone.now().date()
            notifications_sent = 0
            
            # Get librarians to notify
            librarians = User.objects.filter(
                groups__name__in=['Librarian', 'SubLibrarian']
            )
            
            # Check for different expiry periods
            expiry_periods = [
                (10, 'member_expiry_10_days'),
                (5, 'member_expiry_5_days'), 
                (1, 'member_expiry_1_day'),
                (0, 'member_expired')
            ]
            
            for days, event_type in expiry_periods:
                if days == 0:
                    # Already expired
                    target_date = today
                    members = StudentsData.objects.filter(
                        membership_end_date=target_date,
                        is_active=True
                    )
                else:
                    # Expiring in X days
                    target_date = today + timedelta(days=days)
                    members = StudentsData.objects.filter(
                        membership_end_date=target_date,
                        is_active=True
                    )
                
                for member in members:
                    self.send_notification(
                        event_type,
                        librarians,
                        member_name=member.name,
                        member_email=member.email,
                        member_mobile=member.mobile,
                        expiry_date=member.membership_end_date.strftime('%Y-%m-%d'),
                        days_remaining=days,
                        member_id=member.id
                    )
                    notifications_sent += 1
            
            logger.info(f"Sent {notifications_sent} member expiry notifications")
            return notifications_sent
            
        except Exception as e:
            logger.error(f"Error checking member expiry: {e}")
            return 0

    # 4. INVOICE CREATED NOTIFICATIONS
    def notify_invoice_created(self, invoice, created_by):
        """Notify when invoice is created by sublibrarian"""
        try:
            # Notify main librarians
            librarians = User.objects.filter(
                groups__name='Librarian'
            ).exclude(id=created_by.id)
            
            self.send_notification(
                'invoice_created',
                librarians,
                invoice_number=invoice.invoice_number,
                student_name=invoice.student.name,
                amount=str(invoice.total_amount),
                created_by=created_by.username,
                creation_date=timezone.now().strftime('%Y-%m-%d'),
                invoice_id=invoice.id
            )
            
            logger.info(f"Invoice created notification sent for {invoice.invoice_number}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending invoice created notification: {e}")
            return False

    # 5. SALES MILESTONE NOTIFICATIONS
    def check_sales_milestones(self):
        """Check for sales milestones (50k, 100k, 150k, 200k)"""
        try:
            from studentsData.models import Invoice
            
            today = timezone.now().date()
            
            # Get total sales for current month
            month_start = today.replace(day=1)
            monthly_sales = Invoice.objects.filter(
                created_at__date__gte=month_start,
                created_at__date__lte=today,
                status='paid'
            ).aggregate(total=Sum('total_amount'))['total'] or 0
            
            # Check milestones
            milestones = [50000, 100000, 150000, 200000]
            librarians = User.objects.filter(
                groups__name__in=['Librarian', 'SubLibrarian']
            )
            
            for milestone in milestones:
                if monthly_sales >= milestone:
                    # Check if we've already notified for this milestone this month
                    milestone_key = f"sales_milestone_{milestone}_{month_start.strftime('%Y_%m')}"
                    
                    # You can store this in cache or a simple model to avoid duplicate notifications
                    # For now, we'll send the notification
                    self.send_notification(
                        'sales_milestone_reached',
                        librarians,
                        milestone_amount=f"₹{milestone:,}",
                        current_sales=f"₹{monthly_sales:,}",
                        month=month_start.strftime('%B %Y'),
                        milestone_value=milestone
                    )
                    
                    logger.info(f"Sales milestone notification sent: ₹{milestone:,}")
            
            return monthly_sales
            
        except Exception as e:
            logger.error(f"Error checking sales milestones: {e}")
            return 0

    # 6. MONTHLY SALES SUMMARY
    def send_monthly_sales_summary(self):
        """Send monthly sales summary on last day of month"""
        try:
            from studentsData.models import Invoice
            
            today = timezone.now().date()
            
            # Check if today is last day of month
            tomorrow = today + timedelta(days=1)
            if tomorrow.month != today.month:
                # It's the last day of the month
                month_start = today.replace(day=1)
                
                monthly_sales = Invoice.objects.filter(
                    created_at__date__gte=month_start,
                    created_at__date__lte=today,
                    status='paid'
                ).aggregate(total=Sum('total_amount'))['total'] or 0
                
                invoice_count = Invoice.objects.filter(
                    created_at__date__gte=month_start,
                    created_at__date__lte=today,
                    status='paid'
                ).count()
                
                librarians = User.objects.filter(
                    groups__name__in=['Librarian', 'SubLibrarian']
                )
                
                self.send_notification(
                    'monthly_sales_summary',
                    librarians,
                    month=month_start.strftime('%B %Y'),
                    total_sales=f"₹{monthly_sales:,}",
                    invoice_count=invoice_count,
                    average_invoice=f"₹{monthly_sales/invoice_count if invoice_count > 0 else 0:,.2f}"
                )
                
                logger.info(f"Monthly sales summary sent: ₹{monthly_sales:,}")
                return True
                
        except Exception as e:
            logger.error(f"Error sending monthly sales summary: {e}")
        return False

    # 7. VISITOR ADDED NOTIFICATIONS
    def notify_visitor_added(self, visitor, added_by):
        """Notify when visitor is added by sublibrarian"""
        try:
            # Notify main librarians
            librarians = User.objects.filter(
                groups__name='Librarian'
            ).exclude(id=added_by.id)
            
            self.send_notification(
                'visitor_added',
                librarians,
                visitor_name=visitor.name,
                visitor_mobile=visitor.mobile,
                visitor_purpose=getattr(visitor, 'purpose', 'Not specified'),
                added_by=added_by.username,
                visit_date=timezone.now().strftime('%Y-%m-%d'),
                visitor_id=visitor.id
            )
            
            logger.info(f"Visitor added notification sent for {visitor.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending visitor added notification: {e}")
            return False

    # 8. DAILY GALLA SUBMISSION REMINDER
    def send_daily_galla_reminder(self):
        """Send daily galla submission reminder"""
        try:
            # Send to sublibrarians who need to submit daily galla
            sublibrarians = User.objects.filter(
                groups__name='SubLibrarian',
                is_active=True
            )
            
            today = timezone.now().date()
            
            for sublibrarian in sublibrarians:
                self.send_notification(
                    'daily_galla_reminder',
                    [sublibrarian],
                    reminder_date=today.strftime('%Y-%m-%d'),
                    day_name=today.strftime('%A'),
                    user_name=sublibrarian.get_full_name() or sublibrarian.username
                )
            
            logger.info(f"Daily galla reminders sent to {sublibrarians.count()} sublibrarians")
            return sublibrarians.count()
            
        except Exception as e:
            logger.error(f"Error sending daily galla reminders: {e}")
            return 0

    # 9. CUSTOM ADMIN NOTIFICATIONS
    def send_custom_notification(self, title, message, recipients=None, priority='normal'):
        """Send custom notification from Django admin"""
        try:
            if recipients is None:
                # Send to all librarians and sublibrarians
                recipients = User.objects.filter(
                    groups__name__in=['Librarian', 'SubLibrarian'],
                    is_active=True
                )
            
            self.send_notification(
                'custom_admin_notification',
                recipients,
                custom_title=title,
                custom_message=message,
                priority=priority,
                sent_date=timezone.now().strftime('%Y-%m-%d %H:%M'),
                sender='Admin'
            )
            
            recipient_count = len(recipients) if isinstance(recipients, list) else recipients.count()
            logger.info(f"Custom admin notification sent to {recipient_count} users")
            return True
            
        except Exception as e:
            logger.error(f"Error sending custom admin notification: {e}")
            return False


# Global instance
notification_events = NotificationEventManager()
