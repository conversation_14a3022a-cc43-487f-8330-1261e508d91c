<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="robots" content="noindex, nofollow">
    <meta name="google" content="notranslate">
    <title>Secure Payment | Librainian</title>

    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

    <!-- Razorpay Checkout -->
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>

    <!-- Import master CSS variables from code_temp.html -->
    {% include "code_temp.html" %}

    <style>
        :root {
            /* Payment-specific variable aliases for compatibility */
            --primary-color: var(--primary);
            --success-color: var(--success);
            --warning-color: var(--warning);
            --danger-color: var(--danger);
            --text-color: var(--gray-900);
            --text-light: var(--gray-500);
            --text-white: var(--white);
            --bg-gradient: var(--gradient-hero);
            --shadow-glass: var(--glass-shadow);
        }

        * {
            box-sizing: border-box;
        }

        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            padding: 0;
            font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-gradient);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Main container */
        .main-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 2rem 1rem;
        }

        /* Payment container */
        .payment-container {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: var(--border-radius);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-glass);
            padding: 3rem 2rem;
            max-width: 600px;
            width: 100%;
            position: relative;
            overflow: hidden;
            animation: slideUp 0.8s ease-out;
        }

        .payment-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        /* Header section */
        .payment-header {
            text-align: center;
            margin-bottom: 2.5rem;
            position: relative;
            z-index: 1;
        }

        .payment-header .icon-container {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: var(--shadow-sm);
        }

        .payment-header i {
            font-size: 2.5rem;
            color: var(--text-white);
        }

        .payment-header h1 {
            color: var(--text-white);
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 2rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .payment-header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            line-height: 1.5;
            margin: 0;
        }

        /* Payment details card */
        .payment-details {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(8px);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-md);
            position: relative;
            z-index: 1;
        }

        .payment-details h3 {
            color: var(--secondary-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .detail-row:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--secondary-color);
        }

        .detail-label {
            color: var(--text-light);
            font-weight: 500;
        }

        .detail-value {
            color: var(--text-color);
            font-weight: 600;
        }

        /* Payment button */
        .pay-button {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            border: none;
            border-radius: 50px;
            padding: 1.25rem 3rem;
            color: var(--text-white);
            font-weight: 600;
            font-size: 1.2rem;
            cursor: pointer;
            transition: var(--transition);
            width: 100%;
            box-shadow: var(--shadow-sm);
            position: relative;
            z-index: 1;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }

        .pay-button:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(16, 185, 129, 0.4);
        }

        .pay-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Security badges */
        .security-info {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .security-badges {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .security-badge {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .security-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            margin: 0;
        }

        /* Loading state */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            background: var(--text-white);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow-md);
        }

        .spinner {
            width: 3rem;
            height: 3rem;
            border: 3px solid rgba(59, 130, 246, 0.2);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Dark Mode Loading Styles */
        body.dark-mode .loading-overlay {
            background: rgba(17, 24, 39, 0.8);
            backdrop-filter: blur(12px);
        }

        body.dark-mode .loading-content {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            color: white;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .spinner {
            border: 3px solid rgba(255, 255, 255, 0.2);
            border-top: 3px solid rgba(99, 102, 241, 0.8);
        }

        /* Animations */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem 0.5rem;
            }

            .payment-container {
                padding: 2rem 1.5rem;
                max-width: 100%;
            }

            .payment-header h1 {
                font-size: 1.6rem;
            }

            .payment-details {
                padding: 1.5rem;
            }

            .pay-button {
                padding: 1rem 2rem;
                font-size: 1.1rem;
            }

            .security-badges {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus indicators */
        .pay-button:focus-visible {
            outline: 2px solid var(--text-white);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="payment-container">
            <div class="payment-header">
                <div class="icon-container">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h1>Secure Payment</h1>
                <p>Complete your advertisement payment securely with Razorpay</p>
            </div>

            <div class="payment-details">
                <h3>
                    <i class="fas fa-receipt"></i>
                    Payment Summary
                </h3>

                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-bullhorn me-2"></i>
                        Advertisement
                    </span>
                    <span class="detail-value">{{ ad.subject }}</span>
                </div>

                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-calendar me-2"></i>
                        Package Type
                    </span>
                    <span class="detail-value">Premium Listing</span>
                </div>

                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-tags me-2"></i>
                        Base Amount
                    </span>
                    <span class="detail-value">₹{{ ad.amount }}</span>
                </div>

                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-shield-alt me-2"></i>
                        Processing Fee
                    </span>
                    <span class="detail-value">Included</span>
                </div>

                <div class="detail-row">
                    <span class="detail-label">
                        <strong><i class="fas fa-rupee-sign me-2"></i>Total Amount</strong>
                    </span>
                    <span class="detail-value">
                        <strong>₹{{ ad.amount }}</strong>
                    </span>
                </div>
            </div>

            <button id="rzp-button1" class="pay-button">
                <i class="fas fa-lock"></i>
                Pay Securely ₹{{ ad.amount }}
            </button>

            <div class="security-info">
                <div class="security-badges">
                    <div class="security-badge">
                        <i class="fas fa-shield-alt"></i>
                        SSL Secured
                    </div>
                    <div class="security-badge">
                        <i class="fas fa-credit-card"></i>
                        PCI Compliant
                    </div>
                    <div class="security-badge">
                        <i class="fas fa-lock"></i>
                        256-bit Encryption
                    </div>
                </div>
                <p class="security-text">
                    <i class="fas fa-info-circle me-2"></i>
                    Your payment information is secure and encrypted. Powered by Razorpay.
                </p>
            </div>
        </div>
    </div>

    <!-- Loading overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h4>Processing Payment...</h4>
            <p>Please wait while we securely process your payment.</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced Razorpay integration
        var options = {
            "key": "{{ settings.RAZORPAY_KEY_ID }}",
            "amount": "{{ request.session.razorpay_amount }}00",
            "currency": "INR",
            "name": "Librainian",
            "description": "Advertisement Payment - {{ ad.subject }}",
            "image": "/static/img/librainian-logo-black-transparent.png",
            "order_id": "{{ request.session.razorpay_order_id }}",
            "handler": function (response) {
                // Hide loading overlay
                hideLoading();

                // Show success message
                showSuccessMessage(response.razorpay_payment_id);

                // Track successful payment
                if (typeof gtag === 'function') {
                    gtag('event', 'purchase', {
                        'transaction_id': response.razorpay_payment_id,
                        'value': {{ request.session.razorpay_amount }},
                        'currency': 'INR',
                        'event_category': 'Payment',
                        'event_label': 'Advertisement Payment'
                    });
                }

                // Redirect to success page after delay
                setTimeout(() => {
                    window.location.href = "{% url 'advertisement_detail' ad.pk %}?payment_id=" + response.razorpay_payment_id;
                }, 2000);
            },
            "prefill": {
                "name": "{{ user.first_name|default:'User' }} {{ user.last_name|default:'' }}",
                "email": "{{ user.email|default:'' }}",
                "contact": "{{ user.phone|default:'' }}"
            },
            "notes": {
                "address": "Librainian Advertisement Payment"
            },
            "theme": {
                "color": "#10b981"
            },
            "modal": {
                "ondismiss": function() {
                    hideLoading();
                    console.log('Payment modal closed');
                }
            }
        };

        var rzp1 = new Razorpay(options);

        // Enhanced payment button click handler
        document.getElementById('rzp-button1').onclick = function(e) {
            e.preventDefault();

            // Show loading state
            showLoading();

            // Track payment initiation
            if (typeof gtag === 'function') {
                gtag('event', 'begin_checkout', {
                    'currency': 'INR',
                    'value': {{ request.session.razorpay_amount }},
                    'event_category': 'Payment',
                    'event_label': 'Advertisement Payment'
                });
            }

            // Open Razorpay checkout
            rzp1.open();
        };

        // Loading state functions
        function showLoading() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            const payButton = document.getElementById('rzp-button1');

            loadingOverlay.style.display = 'flex';
            payButton.disabled = true;
            payButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        }

        function hideLoading() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            const payButton = document.getElementById('rzp-button1');

            loadingOverlay.style.display = 'none';
            payButton.disabled = false;
            payButton.innerHTML = '<i class="fas fa-lock"></i> Pay Securely ₹{{ ad.amount }}';
        }

        // Success message function
        function showSuccessMessage(paymentId) {
            const container = document.querySelector('.payment-container');
            const successHtml = `
                <div class="text-center" style="position: relative; z-index: 1;">
                    <div class="icon-container" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); margin: 0 auto 1.5rem;">
                        <i class="fas fa-check" style="color: white; font-size: 2.5rem;"></i>
                    </div>
                    <h2 style="color: white; margin-bottom: 1rem;">Payment Successful!</h2>
                    <p style="color: rgba(255,255,255,0.9); margin-bottom: 1rem;">
                        Payment ID: <strong>${paymentId}</strong>
                    </p>
                    <p style="color: rgba(255,255,255,0.8);">
                        Redirecting to advertisement details...
                    </p>
                </div>
            `;
            container.innerHTML = successHtml;
        }

        // Error handling for Razorpay
        rzp1.on('payment.failed', function (response) {
            hideLoading();

            console.error('Payment failed:', response.error);

            // Track failed payment
            if (typeof gtag === 'function') {
                gtag('event', 'payment_failed', {
                    'event_category': 'Payment',
                    'event_label': 'Advertisement Payment Failed',
                    'error_code': response.error.code
                });
            }

            // Show error message
            showErrorMessage(response.error.description || 'Payment failed. Please try again.');
        });

        // Error message function
        function showErrorMessage(message) {
            const existingError = document.querySelector('.error-alert');
            if (existingError) {
                existingError.remove();
            }

            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger error-alert';
            errorDiv.style.cssText = `
                background: rgba(239, 68, 68, 0.1);
                border: 1px solid rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border-radius: 12px;
                padding: 1rem 1.5rem;
                margin-bottom: 1.5rem;
                position: relative;
                z-index: 1;
            `;
            errorDiv.innerHTML = `
                <i class="fas fa-exclamation-circle me-2"></i>
                ${message}
            `;

            const paymentDetails = document.querySelector('.payment-details');
            paymentDetails.parentNode.insertBefore(errorDiv, paymentDetails);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 5000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Track page view
            if (typeof gtag === 'function') {
                gtag('event', 'payment_page_view', {
                    'event_category': 'Payment',
                    'event_label': 'Advertisement Payment Page'
                });
            }

            // Add keyboard support
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && e.target.id === 'rzp-button1') {
                    e.preventDefault();
                    document.getElementById('rzp-button1').click();
                }
            });
        });
    </script>
</body>
</html>