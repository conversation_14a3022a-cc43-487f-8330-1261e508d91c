<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification System Test - LMS</title>
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        
        .test-section h3 {
            color: #007bff;
            margin-top: 0;
            font-size: 1.3em;
        }
        
        .btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #28a745, #1e7e34);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Notification System Test Center</h1>
        
        <div class="test-section">
            <h3>🎯 Step 1: Permission Setup</h3>
            <p>First, enable notifications to test the system:</p>
            <button class="btn" onclick="requestNotificationPermission()">🔔 Enable Notifications</button>
            <div id="permission-status">Checking permission status...</div>
        </div>
        
        <div class="test-section">
            <h3>📱 Step 2: Device Registration</h3>
            <p>Register this device for multi-device notification delivery:</p>
            <button class="btn btn-success" onclick="registerDevice()">📱 Register Device</button>
            <div id="device-info">Device information will appear here...</div>
        </div>
        
        <div class="test-section">
            <h3>🧪 Step 3: Basic Tests</h3>
            <p>Test basic notification functionality:</p>
            <button class="btn btn-success" onclick="sendTestNotification()">Send Test Notification (Custom Handler)</button>
            <button class="btn btn-success" onclick="triggerQRNotification()">Send QR Registration Notification</button>
            <button class="btn btn-warning" onclick="testBrowserNotification()">Test Browser Notification</button>
        </div>
        
        <div class="test-section">
            <h3>⚙️ Step 4: System Features</h3>
            <p>This notification system includes:</p>
            <ul class="feature-list">
                <li>Custom permission request dialog (no generic browser popup)</li>
                <li>Multi-device support (sends to ALL user devices)</li>
                <li>Background notifications (works when site is not active)</li>
                <li>15+ notification event types</li>
                <li>Push notifications for PWA, Chrome mobile, and other browsers</li>
                <li>Service Worker for offline notification handling</li>
                <li>Device tracking and management</li>
                <li>Notification delivery tracking and analytics</li>
                <li>User preference management</li>
                <li>Notification history tracking</li>
                <li>Firebase Cloud Messaging integration</li>
                <li>Site favicon as notification icon</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🚀 Step 5: Complete System Testing</h3>
            <p>Comprehensive testing of all notification events and triggers:</p>
            <button class="btn btn-success" onclick="window.open('/librarian/comprehensive-notification-test/', '_blank')">🎯 Comprehensive Event Test (All 15 Events)</button>
            <button class="btn btn-success" onclick="window.open('/librarian/full-notification-test/', '_blank')">🚀 Full System Integration Test</button>
            <p><small>The <strong>Comprehensive Event Test</strong> covers all notification types including visitor callbacks, member expiry, sales milestones, galla reminders, and custom admin notifications.</small></p>
        </div>
        
        <div class="test-section">
            <h3>🔧 Step 6: Individual Component Tests</h3>
            <p>Test individual components and mobile-specific features:</p>
            <button class="btn btn-warning" onclick="window.open('/librarian/demo-qr-registration/', '_blank')">📝 QR Registration Demo</button>
            <button class="btn btn-warning" onclick="window.open('/librarian/mobile-notification-test/', '_blank')">📱 Advanced Mobile Test</button>
            <button class="btn btn-warning" onclick="window.open('/librarian/simple-mobile-test/', '_blank')">📱 Simple Mobile Test</button>
            <p><small>Use <strong>Simple Mobile Test</strong> if you're having permission issues on mobile devices.</small></p>
        </div>
        
        <div class="test-section">
            <h3>🔧 Step 7: Integration</h3>
            <p>To integrate this into your registration form, add this code:</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 14px;">
# In your registration view (studentsData/views.py)<br>
from librarian.notification_events import notification_events<br><br>

def register_student_via_qr(request):<br>
&nbsp;&nbsp;&nbsp;&nbsp;# ... process form data ...<br>
&nbsp;&nbsp;&nbsp;&nbsp;temp_student = TempStudentData.objects.create(...)<br>
&nbsp;&nbsp;&nbsp;&nbsp;<br>
&nbsp;&nbsp;&nbsp;&nbsp;# Send notification<br>
&nbsp;&nbsp;&nbsp;&nbsp;notification_events.send_notification(<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'qr_registration',<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;librarians,<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;student_name=temp_student.name,<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;course=temp_student.course<br>
&nbsp;&nbsp;&nbsp;&nbsp;)
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 Step 8: Admin Management</h3>
            <p>Manage notifications through the admin interface:</p>
            <button class="btn btn-warning" onclick="window.open('/admin/', '_blank')">Open Admin Interface</button>
            <p><small>Look for "Custom Notifications", "Notification Logs", "Notification Preferences", and "Notification Schedules"</small></p>
        </div>
    </div>

    <!-- Load notification scripts -->
    <script src="/static/js/custom_notification_handler.js"></script>
    <script src="/static/js/qr_registration_notifier.js"></script>
    
    <script>
        function updatePermissionStatus() {
            const statusDiv = document.getElementById('permission-status');
            const permission = Notification.permission;
            
            switch (permission) {
                case 'granted':
                    statusDiv.innerHTML = '<span style="color: green;">✅ Notifications enabled</span>';
                    break;
                case 'denied':
                    statusDiv.innerHTML = '<span style="color: red;">❌ Notifications blocked</span>';
                    break;
                default:
                    statusDiv.innerHTML = '<span style="color: orange;">⚠️ Notifications not requested</span>';
            }
        }
        
        function requestNotificationPermission() {
            if (typeof notificationHandler !== 'undefined') {
                notificationHandler.requestPermission();
            } else {
                Notification.requestPermission().then(permission => {
                    updatePermissionStatus();
                });
            }
        }
        
        function registerDevice() {
            if (typeof notificationHandler !== 'undefined') {
                notificationHandler.handlePermissionGranted();
            } else {
                document.getElementById('device-info').innerHTML = '<span style="color: orange;">⚠️ Notification handler not loaded</span>';
            }
        }
        
        function sendTestNotification() {
            if (typeof notificationHandler !== 'undefined') {
                notificationHandler.sendTestNotification();
            } else {
                testBrowserNotification();
            }
        }
        
        function testBrowserNotification() {
            if (Notification.permission === 'granted') {
                new Notification('🔔 Test Browser Notification', {
                    body: 'This is a direct browser notification test with site favicon!',
                    icon: '/favicon.ico',
                    tag: 'test-notification'
                });
            } else {
                alert('Please allow notifications first!');
            }
        }
        
        // Update permission status on page load
        document.addEventListener('DOMContentLoaded', function() {
            updatePermissionStatus();
            
            // Update device info
            const deviceInfo = document.getElementById('device-info');
            const userAgent = navigator.userAgent;
            let deviceName = 'Unknown Browser';
            
            if (userAgent.includes('Chrome')) deviceName = 'Chrome';
            else if (userAgent.includes('Firefox')) deviceName = 'Firefox';
            else if (userAgent.includes('Safari')) deviceName = 'Safari';
            
            if (/Mobile|Android|iPhone|iPad/i.test(userAgent)) {
                deviceName += ' Mobile';
            } else {
                deviceName += ' Desktop';
            }
            
            deviceInfo.innerHTML = `<strong>Current Device:</strong> ${deviceName}<br><strong>Platform:</strong> ${navigator.platform}`;
        });
    </script>
</body>
</html>
