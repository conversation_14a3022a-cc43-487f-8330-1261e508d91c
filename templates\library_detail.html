<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="google" content="notranslate">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <link rel="icon" href="/static/img/librainian-logo-black-transparent.png" type="image/x-icon">

  <!-- Include Navbar Head Section -->
  {% with navbar_section="head" %}
      {% include "public_navbar.html" %}
  {% endwith %}

  <!-- Primary Meta Tags -->
  <title>{{library.library_name}} | Library Details, Hours & Contact | Librainian</title>
  <meta name="robots" content="index, follow, max-image-preview:large">
  <meta name="description" content="Discover {{library.library_name}}, a premier library in {{library.librarian_address}} offering excellent study spaces and reading facilities. View detailed information about opening hours, contact details, and available services. Register easily as a student with our convenient QR code system. {% if library.discount_available %}Special student discount of {{library.discount_amount}}% available for a limited time!{% endif %} Find the perfect environment for studying, research, and academic excellence.">
  <meta http-equiv="content-language" content="en">
  <meta name="keywords" content="{{library.library_name}}, best library in {{library.librarian_address}}, study space near {{library.librarian_address}}, {{library.library_name}} the quiet reading room, {{library.library_name}}'s opening hours, {{library.library_name}}'s contact information, {{library.library_name}}'s online registration form, {{library.library_name}} registration,{{library.library_name}} uses Librainian app, {{library.library_name}} location map, {{library.library_name}} contact number, {{library.library_name}} working hours, {{library.library_name}} timings,  library near me, top rated libraries in {{library.librarian_address}}, academic library, public library services, digital library access, library facilities, library amenities, library seating capacity, library membership fees,{{library.library_name}} for best at student services, {{library.library_name}} for exam preparation space, {{library.library_name}} reviews, {{library.library_name}} ratings{% if library.discount_available %}, find your library with librainian to get special library discount, librainian special library offers, Librainian discounted library membership, {{library.discount_amount}}% off library services, student discount in {{library.librarian_address}}{% endif %}">
  <meta name="geo.region" content="IN">
  <meta name="author" content="Librainian">
  <link rel="canonical" href="https://www.librainian.com/librarian/library-details/{{ library.slug }}/">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Librainian">
  <meta property="og:title" content="{{library.library_name}} | Library Details & Information">
  <meta property="og:description" content="Discover {{library.library_name}}, a premier library in {{library.librarian_address}} offering excellent study spaces and reading facilities. View detailed information about opening hours, contact details, and available services. {% if library.discount_available %}Special student discount of {{library.discount_amount}}% available for a limited time!{% endif %}">
  <meta property="og:url" content="https://www.librainian.com/librarian/library-details/{{ library.slug }}/">
  <meta property="og:image" content="{% if library.image %}{{ library.image.url }}{% else %}https://www.librainian.com/static/img/library-default.jpg{% endif %}">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:locale" content="en_US">

  <!-- Twitter -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:site" content="@librainian_app">
  <meta name="twitter:creator" content="@librainian_app">
  <meta name="twitter:title" content="{{library.library_name}} | Library Details & Information">
  <meta name="twitter:description" content="Discover {{library.library_name}}, a premier library in {{library.librarian_address}} offering excellent study spaces and reading facilities. View detailed information about opening hours, contact details, and available services. {% if library.discount_available %}Special student discount of {{library.discount_amount}}% available!{% endif %}">
  <meta name="twitter:image" content="{% if library.image %}{{ library.image.url }}{% else %}https://www.librainian.com/static/img/library-default.jpg{% endif %}">

<!-- Structured Data - Library Information -->
<script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "{{ library.library_name }}",
    "image": "{% if library.image %}{{ library.image.url }}{% else %}https://www.librainian.com/static/img/library-default.jpg{% endif %}",
    "description": "{{ library.description|escapejs }}",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "{{ library.librarian_address }}",
      "addressCountry": "IN"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": 20.5937,
      "longitude": 78.9629
    },
    "url": "https://www.librainian.com/librarian/library-details/{{ library.slug }}/",
    "telephone": "{{ library.librarian_phone_num }}",
    "email": "{{ library.user.email }}",
    "openingHoursSpecification": [
      {% for shift in shifts %}
      {
        "@type": "OpeningHoursSpecification",
        "name": "{{ shift.name }}",
        "dayOfWeek": [
          "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"
        ],
        "opens": "{{ shift.opens }}",
        "closes": "{{ shift.closes }}",
        "priceSpecification": {
          "@type": "UnitPriceSpecification",
          "price": "{{ shift.rate }}",
          "priceCurrency": "INR",
          "description": "Fee for {{ shift.name }} shift"
        }
      }{% if not forloop.last %},{% endif %}
      {% endfor %}
    ],
    "sameAs": [
      "https://www.librainian.com/",
      "https://www.facebook.com/librainian",
      "https://twitter.com/librainian_app"
    ]
  }
  </script>
  

  <!-- Breadcrumb Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://www.librainian.com/"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Libraries",
        "item": "https://www.librainian.com/librarian/library-list/"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": "{{library.library_name}}",
        "item": "https://www.librainian.com/librarian/library-details/{{ library.slug }}/"
      }
    ]
  }
  </script>

  <!-- Contact Form Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "Contact {{library.library_name}}",
    "description": "Contact form to reach out to {{library.library_name}} for inquiries and information",
    "mainEntity": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "telephone": "{{library.librarian_phone_num}}",
      "email": "{{library.user.email}}",
      "areaServed": "{{library.librarian_address}}",
      "availableLanguage": ["English", "Hindi"]
    }
  }
  </script>


  <link href="https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png" rel="icon">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

       <!-- Disable Right click -->

         

    <!-- disable Print Screen for Windows -->

      

    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
  <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-color: #1e293b;
            --text-primary: #1e293b;
            --text-light: #6b7280;
            --text-white: #ffffff;
            --glass-bg: rgba(255, 255, 255, 0.15);
            --glass-border: rgba(255, 255, 255, 0.25);
            --glass-backdrop: blur(16px);
            --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 10px 20px rgba(0, 0, 0, 0.2);
            --shadow-glass: 0 8px 32px rgba(31, 38, 135, 0.37);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Dark mode variables */
        body.dark-mode {
            --primary-color: #60a5fa;
            --primary-dark: #3b82f6;
            --secondary-color: #f3f4f6;
            --text-color: #f3f4f6;
            --text-primary: #f3f4f6;
            --text-light: #d1d5db;
            --glass-bg: rgba(0, 0, 0, 0.2);
            --glass-border: rgba(255, 255, 255, 0.1);
            --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        * {
            box-sizing: border-box;
        }

        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
            color: var(--text-primary);
            transition: var(--transition);
        }

        /* Dark mode body */
        body.dark-mode {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        }

        /* Top Controls */
        .top-controls {
            position: absolute;
            top: 1rem;
            left: 1rem;
            right: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
        }

        .back-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            text-decoration: none;
            color: var(--text-white);
            font-weight: 500;
            transition: var(--transition);
            border-radius: 50px;
        }

        .back-button:hover {
            color: var(--text-white);
            text-decoration: none;
        }

        .dark-mode-toggle {
            width: 45px;
            height: 45px;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-white);
            font-size: 1.1rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .dark-mode-toggle:hover {
            transform: scale(1.1);
        }

        /* Mobile adjustments for top controls */
        @media (max-width: 768px) {
            .top-controls {
                top: 0.5rem;
                left: 0.5rem;
                right: 0.5rem;
            }

            .back-button {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }

            .back-button span {
                display: none;
            }

            .dark-mode-toggle {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }

        /* Hero Section Styles */
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .hero-text {
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: rgba(255, 255, 255, 1);
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .hero-description {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .hero-badge {
            background: linear-gradient(135deg, var(--warning-color) 0%, #ff6b35 100%);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
            box-shadow: 0 8px 32px rgba(245, 158, 11, 0.3);
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .hero-image {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .library-hero-img {
            width: 100%;
            max-width: 500px;
            height: auto;
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .hero-placeholder {
            width: 400px;
            height: 400px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .hero-placeholder i {
            font-size: 6rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Section Styles */
        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: rgba(255, 255, 255, 1);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            max-width: 600px;
            margin: 0 auto;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        /* Offers Section */
        .offers-section {
            padding: 6rem 0;
            background: rgba(16, 185, 129, 0.05);
        }

        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .offer-card {
            text-align: center;
            padding: 3rem 2rem;
            transition: var(--transition-slow);
        }

        .offer-card:hover {
            transform: translateY(-10px);
        }

        .offer-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
        }

        .offer-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 1);
            margin-bottom: 1rem;
        }

        .offer-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--warning-color);
            margin-bottom: 1rem;
        }

        .offer-card p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .offer-validity {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: var(--success-color);
            font-weight: 500;
        }

        /* Info Section */
        .info-section {
            padding: 6rem 0;
        }

        .info-grid {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .info-item {
            display: flex;
            gap: 1.5rem;
            padding: 2rem;
            background: rgba(59, 130, 246, 0.05);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: var(--transition);
        }

        .info-item:hover {
            background: rgba(59, 130, 246, 0.1);
            transform: translateX(5px);
        }

        .info-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            flex-shrink: 0;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
        }

        .info-content h4 {
            font-size: 1.3rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 1);
            margin-bottom: 0.5rem;
        }

        .info-content p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .info-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .info-link:hover {
            color: var(--primary-dark);
            text-decoration: none;
        }

        .hours-list {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .hour-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .shift-name {
            font-weight: 600;
            color: rgba(255, 255, 255, 1);
        }

        .shift-time {
            color: rgba(255, 255, 255, 0.8);
        }

        .shift-price {
            font-weight: 600;
            color: var(--success-color);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--success-color);
            color: white;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-badge i {
            animation: pulse 2s infinite;
        }

        .library-description {
            margin-bottom: 2rem;
        }

        .library-description h4 {
            font-size: 1.3rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 1);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .library-description p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: var(--transition);
        }

        .feature-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .feature-item i {
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .feature-item span {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        /* Registration Section */
        .registration-section {
            padding: 6rem 0;
            background: rgba(147, 51, 234, 0.05);
        }

        .registration-info h3 {
            font-size: 2rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 1);
            margin-bottom: 1rem;
        }

        .registration-info p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .registration-benefits {
            margin-bottom: 2rem;
        }

        .benefit-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            color: rgba(255, 255, 255, 0.9);
        }

        .benefit-item i {
            color: var(--success-color);
            font-size: 1.1rem;
        }

        .registration-methods {
            text-align: center;
        }

        .method-divider {
            margin: 1.5rem 0;
            color: rgba(255, 255, 255, 0.6);
            font-weight: 500;
        }

        .qr-instruction {
            color: rgba(255, 255, 255, 0.8);
            font-style: italic;
        }

        .qr-registration {
            text-align: center;
            padding: 3rem 2rem;
        }

        .qr-header h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 1);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .qr-header p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
        }

        .qr-code-container {
            margin-bottom: 2rem;
        }

        .qr-image {
            width: 250px;
            height: 250px;
            border: 3px solid var(--primary-color);
            border-radius: 16px;
            background: white;
            padding: 1rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            transition: var(--transition);
        }

        .qr-image:hover {
            transform: scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .qr-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Contact Section */
        .contact-section {
            padding: 6rem 0;
        }

        .contact-details {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .contact-item {
            display: flex;
            gap: 1.5rem;
            align-items: flex-start;
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            flex-shrink: 0;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
        }

        .contact-content h4 {
            font-size: 1.2rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 1);
            margin-bottom: 0.5rem;
        }

        .contact-content p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .contact-actions {
            margin-top: 0.5rem;
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            width: 35px;
            height: 35px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
        }

        .action-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: scale(1.1);
        }

        .contact-form-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 1);
            margin-bottom: 1rem;
        }

        .contact-form-card p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0.5rem;
        }

        .map-section {
            margin-top: 4rem;
        }

        .map-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .map-header h3 {
            font-size: 1.8rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 1);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .map-header p {
            color: rgba(255, 255, 255, 0.8);
        }

        .map-container {
            padding: 1rem;
            overflow: hidden;
        }

        .map-placeholder {
            height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .map-placeholder i {
            font-size: 4rem;
        }

        /* Footer Section */
        .footer-section {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 4rem 0 2rem;
            margin-top: 4rem;
        }

        .footer-content {
            margin-bottom: 2rem;
        }

        .footer-about h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 1);
            margin-bottom: 1rem;
        }

        .footer-about p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .footer-social {
            display: flex;
            gap: 1rem;
        }

        .social-link {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition);
        }

        .social-link:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .footer-links h4,
        .footer-services h4,
        .footer-contact h4 {
            font-size: 1.2rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 1);
            margin-bottom: 1rem;
        }

        .footer-links ul,
        .footer-services ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li,
        .footer-services li {
            margin-bottom: 0.5rem;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-links a:hover {
            color: var(--primary-color);
        }

        .footer-services li {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .footer-services i {
            color: var(--success-color);
            font-size: 0.9rem;
        }

        .footer-contact-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            margin-bottom: 1rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .footer-contact-item i {
            color: var(--primary-color);
            margin-top: 0.2rem;
            flex-shrink: 0;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 2rem;
        }

        .footer-bottom-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .footer-bottom p {
            color: rgba(255, 255, 255, 0.6);
            margin: 0;
        }

        .footer-bottom-links {
            display: flex;
            gap: 2rem;
        }

        .footer-bottom-links a {
            color: rgba(255, 255, 255, 0.6);
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-bottom-links a:hover {
            color: var(--primary-color);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .hero-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }

            .hero-title {
                font-size: 3rem;
            }

            .offers-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }

            .features-grid {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .hero-section {
                min-height: 80vh;
                padding: 2rem 0;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .hero-actions {
                justify-content: center;
            }

            .section-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .offers-section,
            .info-section,
            .registration-section,
            .contact-section {
                padding: 4rem 0;
            }

            .offers-grid {
                grid-template-columns: 1fr;
            }

            .offer-card {
                padding: 2rem 1.5rem;
            }

            .info-item {
                flex-direction: column;
                text-align: center;
                align-items: center;
                gap: 1rem;
            }

            .opening-hour-container {
              padding: 0 !important;
              margin: 0 !important;
              border: none !important;
              background: none !important;
              border: none !important;
            }

            .hours-list {
                gap: 0.5rem;
            }

            .hour-item {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .qr-image {
                width: 200px;
                height: 200px;
            }

            .qr-actions {
                flex-direction: column;
                align-items: center;
            }

            .contact-item {
                gap: 1rem;
            }
     

            .footer-bottom-content {
                flex-direction: column;
                text-align: center;
            }

            .footer-bottom-links {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .section-title {
                font-size: 1.5rem;
            }

            .offer-card {
                padding: 1.5rem 1rem;
            }

            .qr-image {
                width: 180px;
                height: 180px;
            }

            .footer-social {
                justify-content: center;
            }
        }

        /* Glass Card Classes - Updated for new design */
        .glass-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.05) inset;
            transition: var(--transition-slow);
            position: relative;
            overflow: hidden;
            padding: 2rem;
        }

        .glass-card:hover {
            transform: translateY(-3px);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
        }

        /* Light mode glass card */
        body:not(.dark-mode) .glass-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.12),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        }

        body:not(.dark-mode) .glass-card:hover {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2) inset,
                0 2px 8px rgba(255, 255, 255, 0.2) inset;
        }

        /* Dark mode glass card */
        body.dark-mode .glass-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.05) inset;
        }

        body.dark-mode .glass-card:hover {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 2px 8px rgba(255, 255, 255, 0.05) inset;
        }

        /* Button styles */
        .btn_theme {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            border: none;
            border-radius: 50px;
            padding: 0.875rem 2rem;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn_theme:hover {
            color: var(--text-white);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .btn-outline-light {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(8px);
            border-radius: 50px;
            padding: 0.875rem 2rem;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 1);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        /* Form styles */
        .form-control {
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 0.875rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            transition: var(--transition);
            color: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 0.15);
            color: rgba(255, 255, 255, 1);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        /* Alert styles */
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(8px);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus indicators */
        .btn_theme:focus-visible,
        .btn-outline-light:focus-visible,
        .form-control:focus-visible {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }
  </style>
</head>

<body>
  <!-- Include Public Navigation Body -->
  {% with navbar_section="body" %}
      {% include "public_navbar.html" %}
  {% endwith %}

  <!-- Hero Section -->
  <section class="hero-section pt-5">
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">{{library.library_name}}</h1>
        <p class="hero-subtitle">Your Gateway to Knowledge and Excellence</p>
        <p class="hero-description">Premier Library in {{library.librarian_address}} - Where Learning Meets Innovation</p>
        {% if library.discount_available %}
        <div class="hero-badge">
          <i class="fas fa-tags"></i>
          Special Offer: {{library.discount_amount}}% Off for Students!
        </div>
        {% endif %}
        <div class="hero-actions">
          <a href="#registration" class="btn btn_theme btn-lg">
            <i class="fas fa-user-plus"></i>
            Register Now
          </a>
          <a href="#contact" class="btn btn-outline-light btn-lg">
            <i class="fas fa-phone"></i>
            Contact Us
          </a>
        </div>
      </div>
      <div class="hero-image">
        {% if library.image %}
        <img src="{{ library.image.url }}" alt="{{library.library_name}}" class="library-hero-img">
        {% else %}
        <div class="hero-placeholder">
          <i class="fas fa-book-open"></i>
        </div>
        {% endif %}
      </div>
    </div>
  </section>

  <!-- Offers Section -->
  {% if library.discount_available %}
  <section class="offers-section" id="offers">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">
          <i class="fas fa-gift"></i>
          Special Offers
        </h2>
        <p class="section-subtitle">Limited time deals for our valued students</p>
      </div>
      <div class="offers-grid">
        <div class="offer-card glass-card">
          <div class="offer-icon">
            <i class="fas fa-percentage"></i>
          </div>
          <h3>Student Discount</h3>
          <div class="offer-value">{{library.discount_amount}}% OFF</div>
          <p>Get {{library.discount_amount}}% discount on all library services. Perfect for students looking for quality study environment.</p>
          <div class="offer-validity">
            <i class="fas fa-clock"></i>
            Limited Time Offer
          </div>
        </div>
        <div class="offer-card glass-card">
          <div class="offer-icon">
            <i class="fas fa-users"></i>
          </div>
          <h3>Group Study</h3>
          <div class="offer-value">Free WiFi</div>
          <p>High-speed internet access for all registered students. Perfect for research and online learning.</p>
          <div class="offer-validity">
            <i class="fas fa-check-circle"></i>
            Always Available
          </div>
        </div>
        <div class="offer-card glass-card">
          <div class="offer-icon">
            <i class="fas fa-book"></i>
          </div>
          <h3>Study Materials</h3>
          <div class="offer-value">Free Access</div>
          <p>Access to extensive collection of books, journals, and digital resources for comprehensive learning.</p>
          <div class="offer-validity">
            <i class="fas fa-infinity"></i>
            Unlimited Access
          </div>
        </div>
      </div>
    </div>
  </section>
  {% endif %}

  <!-- Library Information Section -->
  <section class="info-section" id="about">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">
          <i class="fas fa-info-circle"></i>
          About Our Library
        </h2>
        <p class="section-subtitle">Everything you need to know about {{library.library_name}}</p>
      </div>
      <div class="row g-4">
        <div class="col-lg-6">
          <div class="info-card glass-card opening-hour-container">
            <div class="info-grid">
              <div class="info-item">
                <div class="info-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="info-content">
                  <h4>Location</h4>
                  <p>{{library.librarian_address}}</p>
                  <a href="{{library.google_map_url}}" target="_blank" class="info-link">
                    <i class="fas fa-external-link-alt"></i>
                    View on Maps
                  </a>
                </div>
              </div>

              <div class="info-item">
                <div class="info-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <div class="info-content">
                  <h4>Operating Hours</h4>
                  <div class="hours-list">
                    {% for shift in shifts %}
                    <div class="hour-item">
                      <span class="shift-name">{{shift.name}}</span>
                      <span class="shift-time">{{shift.opens}} - {{shift.closes}}</span>
                      <span class="shift-price">₹{{shift.rate}}</span>
                    </div>
                    {% endfor %}
                  </div>
                  <div class="status-badge">
                    <i class="fas fa-circle"></i>
                    Currently Open
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-6">
          <div class="info-card glass-card">
            <div class="library-description">
              <h4>
                <i class="fas fa-book-open"></i>
                About This Library
              </h4>
              <p>{{library.description}}</p>
            </div>
            <div class="features-grid">
              <div class="feature-item">
                <i class="fas fa-wifi"></i>
                <span>Free WiFi</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-coffee"></i>
                <span>Refreshments</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-parking"></i>
                <span>Parking Available</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-shield-alt"></i>
                <span>Secure Environment</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-air-conditioner"></i>
                <span>Air Conditioned</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-book-reader"></i>
                <span>Study Materials</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Student Registration Section -->
  <section class="registration-section" id="registration">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">
          <i class="fas fa-user-plus"></i>
          Student Registration
        </h2>
        <p class="section-subtitle">Join {{library.library_name}} today and start your learning journey</p>
      </div>
      <div class="row g-4 align-items-center">
        <div class="col-lg-6">
          <div class="registration-info glass-card">
            <h3>Quick & Easy Registration</h3>
            <p>Register as a student at {{library.library_name}} using our convenient QR code system or direct link. Get instant access to all our facilities and services.</p>

            <div class="registration-benefits">
              <div class="benefit-item">
                <i class="fas fa-check-circle"></i>
                <span>Instant Registration Process</span>
              </div>
              <div class="benefit-item">
                <i class="fas fa-check-circle"></i>
                <span>Access to All Study Areas</span>
              </div>
              <div class="benefit-item">
                <i class="fas fa-check-circle"></i>
                <span>Digital Library Resources</span>
              </div>
              <div class="benefit-item">
                <i class="fas fa-check-circle"></i>
                <span>Flexible Timing Options</span>
              </div>
            </div>

            <div class="registration-methods">
              <a href="/students/student-registration-temp/{{library.slug}}/" class="btn btn_theme btn-lg">
                <i class="fas fa-user-plus"></i>
                Register Online
              </a>
              <p class="method-divider">or</p>
              <p class="qr-instruction">Scan the QR code for quick registration</p>
            </div>
          </div>
        </div>
        <div class="col-lg-6">
          <div class="qr-registration glass-card">
            <div class="qr-header">
              <h3>
                <i class="fas fa-qrcode"></i>
                QR Code Registration
              </h3>
              <p>Scan to register instantly</p>
            </div>

            <div class="qr-code-container">
              <img id="qrImage"
                   src="data:image/png;base64,{{ qr_code }}"
                   alt="QR Code for {{ library.library_name }} student registration"
                   class="qr-image"
                   loading="lazy">
            </div>

            <div class="qr-actions">
              <button id="downloadQR" class="btn btn-outline-light" onclick="downloadQR()">
                <i class="fas fa-download"></i>
                Download QR
              </button>
              <button class="btn btn-outline-light" onclick="shareQR()">
                <i class="fas fa-share-alt"></i>
                Share QR
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Us Section -->
  <section class="contact-section" id="contact">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">
          <i class="fas fa-envelope"></i>
          Contact Us
        </h2>
        <p class="section-subtitle">Get in touch with {{library.library_name}} for any inquiries</p>
      </div>
      <div class="row g-4">
        <div class="col-lg-6">
          <div class="contact-info glass-card">
            <h3>Get In Touch</h3>
            <p>Have questions about our services, hours, or facilities? We're here to help you with all your inquiries.</p>

            <div class="contact-details">
              <div class="contact-item">
                <div class="contact-icon">
                  <i class="fas fa-phone"></i>
                </div>
                <div class="contact-content">
                  <h4>Phone</h4>
                  <p>
                    <span id="phoneText">{{library.librarian_phone_num}}</span>
                    <div class="contact-actions">
                      <button class="action-btn" onclick="copyToClipboard('phoneText')" title="Copy phone number">
                        <i class="fas fa-copy"></i>
                      </button>
                      <a href="tel:{{library.librarian_phone_num}}" class="action-btn" title="Call library">
                        <i class="fas fa-phone-alt"></i>
                      </a>
                    </div>
                  </p>
                </div>
              </div>

              <div class="contact-item">
                <div class="contact-icon">
                  <i class="fas fa-envelope"></i>
                </div>
                <div class="contact-content">
                  <h4>Email</h4>
                  <p>
                    <span id="emailText">{{library.user.email}}</span>
                    <div class="contact-actions">
                      <button class="action-btn" onclick="copyToClipboard('emailText')" title="Copy email">
                        <i class="fas fa-copy"></i>
                      </button>
                      <a href="mailto:{{library.user.email}}" class="action-btn" title="Send email">
                        <i class="fas fa-external-link-alt"></i>
                      </a>
                    </div>
                  </p>
                </div>
              </div>

              <div class="contact-item">
                <div class="contact-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="contact-content">
                  <h4>Address</h4>
                  <p>{{library.librarian_address}}</p>
                  <a href="{{library.google_map_url}}" target="_blank" class="btn btn-sm btn_theme mt-2">
                    <i class="fas fa-directions"></i>
                    Get Directions
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-6">
          <div class="contact-form-card glass-card">
            {% if success_message %}
            <div class="alert alert-success" role="alert">
              <i class="fas fa-check-circle me-2"></i>
              {{ success_message }}
            </div>
            {% endif %}
            {% if error_message %}
            <div class="alert alert-danger" role="alert">
              <i class="fas fa-exclamation-circle me-2"></i>
              {{ error_message }}
            </div>
            {% endif %}

            <h3>Send us a Message</h3>
            <p>Fill out the form below and we'll get back to you as soon as possible.</p>

            <form class="contact-form" method="POST">
              {% csrf_token %}
              <div class="form-group">
                <label for="name" class="form-label">
                  <i class="fas fa-user"></i>
                  Your Name
                </label>
                <input type="text" class="form-control" name="name" id="name" placeholder="Enter your full name" required>
              </div>

              <div class="form-group">
                <label for="email" class="form-label">
                  <i class="fas fa-envelope"></i>
                  Email Address
                </label>
                <input type="email" class="form-control" name="email" id="email" placeholder="Enter your email address" required>
              </div>

              <div class="form-group">
                <label for="message" class="form-label">
                  <i class="fas fa-message"></i>
                  Your Message
                </label>
                <textarea class="form-control" name="message" id="message" rows="5" placeholder="What would you like to know about our library?" required></textarea>
              </div>

              <button type="submit" class="btn btn_theme w-100">
                <i class="fas fa-paper-plane"></i>
                Send Message
              </button>
            </form>
          </div>
        </div>
      </div>

      <!-- Map Section -->
      <div class="map-section">
        <div class="map-header">
          <h3>
            <i class="fas fa-map"></i>
            Find Us on Map
          </h3>
          <p>Locate {{library.library_name}} easily with our interactive map</p>
        </div>
        <div class="map-container glass-card">
          {% if library.google_map_url %}
          <iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3022.9663095343008!2d-74.00425878459418!3d40.74844097932681!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c259bf5c1654f3%3A0xc80f9cfce5383d5d!2sGoogle!5e0!3m2!1sen!2sus!4v1647043432999!5m2!1sen!2sus"
            width="100%"
            height="400"
            style="border:0; border-radius: 16px;"
            allowfullscreen=""
            loading="lazy"
            referrerpolicy="no-referrer-when-downgrade">
          </iframe>
          {% else %}
          <div class="map-placeholder">
            <i class="fas fa-map-marker-alt"></i>
            <p>Map will be available soon</p>
            <a href="{{library.google_map_url}}" target="_blank" class="btn btn_theme">
              <i class="fas fa-external-link-alt"></i>
              View on Google Maps
            </a>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </section>

  <!-- Footer Section -->
  <footer class="footer-section">
    <div class="container">
      <div class="footer-content">
        <div class="row g-4">
          <div class="col-lg-4">
            <div class="footer-about">
              <h3>{{library.library_name}}</h3>
              <p>Your premier destination for learning, studying, and academic excellence. We provide a conducive environment for students to achieve their educational goals.</p>
              <div class="footer-social">
                <a href="#" class="social-link" title="Facebook">
                  <i class="fab fa-facebook-f"></i>
                </a>
                <a href="#" class="social-link" title="Twitter">
                  <i class="fab fa-twitter"></i>
                </a>
                <a href="#" class="social-link" title="Instagram">
                  <i class="fab fa-instagram"></i>
                </a>
                <a href="#" class="social-link" title="LinkedIn">
                  <i class="fab fa-linkedin-in"></i>
                </a>
              </div>
            </div>
          </div>
          <div class="col-lg-2">
            <div class="footer-links">
              <h4>Quick Links</h4>
              <ul>
                <li><a href="#about">About Us</a></li>
                <li><a href="#offers">Offers</a></li>
                <li><a href="#registration">Registration</a></li>
                <li><a href="#contact">Contact</a></li>
              </ul>
            </div>
          </div>
          <div class="col-lg-3">
            <div class="footer-services">
              <h4>Our Services</h4>
              <ul>
                <li><i class="fas fa-check"></i> Study Halls</li>
                <li><i class="fas fa-check"></i> Digital Library</li>
                <li><i class="fas fa-check"></i> Group Study Rooms</li>
                <li><i class="fas fa-check"></i> Free WiFi</li>
                <li><i class="fas fa-check"></i> Parking Facility</li>
              </ul>
            </div>
          </div>
          <div class="col-lg-3">
            <div class="footer-contact">
              <h4>Contact Info</h4>
              <div class="footer-contact-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>{{library.librarian_address}}</span>
              </div>
              <div class="footer-contact-item">
                <i class="fas fa-phone"></i>
                <span>{{library.librarian_phone_num}}</span>
              </div>
              <div class="footer-contact-item">
                <i class="fas fa-envelope"></i>
                <span>{{library.user.email}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <div class="footer-bottom-content">
          <p>&copy; 2025 Librainian.com. All rights reserved.</p>
          <div class="footer-bottom-links">
            <a href="https://librainian.com/blogs/p/privacy-policy/" target="_blank" rel="noopener">Privacy Policy</a>
            <a href="https://librainian.com/blogs/p/terms-of-use/" target="_blank" rel="noopener">Terms of Service</a>
            <a href="https://librainian.com">Powered by Librainian</a>
          </div>
        </div>
      </div>
    </div>
  </footer>




  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // Dark Mode Toggle Functionality
    const darkModeToggle = document.getElementById('darkModeToggle');
    const darkModeIcon = document.getElementById('darkModeIcon');
    const body = document.body;

    // Check for saved dark mode preference or default to light mode
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        body.classList.add('dark-mode');
        darkModeIcon.classList.remove('fa-moon');
        darkModeIcon.classList.add('fa-sun');
    }

    // Dark mode toggle event listener
    darkModeToggle.addEventListener('click', function() {
        body.classList.toggle('dark-mode');

        if (body.classList.contains('dark-mode')) {
            darkModeIcon.classList.remove('fa-moon');
            darkModeIcon.classList.add('fa-sun');
            localStorage.setItem('theme', 'dark');
        } else {
            darkModeIcon.classList.remove('fa-sun');
            darkModeIcon.classList.add('fa-moon');
            localStorage.setItem('theme', 'light');
        }
    });

    // Loader functionality
    $(document).ready(function() {
        $('#loader').show(); // Show the loader on page load

        $(window).on('load', function() {
            $('#loader').hide();
        });

        $('.contact-form').on('submit', function() {
            $('#loader').show();

            // Track form submission event
            gtag('event', 'form_submission', {
              'event_category': 'Contact',
              'event_label': '{{library.library_name}} Contact Form'
            });
        });

        // Track QR code download
        $('#downloadQR').on('click', function() {
            gtag('event', 'qr_download', {
              'event_category': 'Engagement',
              'event_label': '{{library.library_name}} QR Code'
            });
        });
    });
  </script>
  <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast-notification toast-${type}`;
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
            `;

            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : '#ef4444'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                z-index: 9999;
                animation: slideInRight 0.3s ease-out;
                font-size: 0.9rem;
                max-width: 320px;
                backdrop-filter: blur(16px);
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (toast.parentNode) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // Enhanced download function
        function downloadQR() {
            try {
                const qrImage = document.getElementById('qrImage');
                if (!qrImage || !qrImage.src) {
                    throw new Error('QR code image not found');
                }

                const link = document.createElement('a');
                link.href = qrImage.src;
                link.download = '{{ library.library_name|slugify }}_qr_code.png';
                link.style.display = 'none';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showToast('QR Code downloaded successfully!', 'success');

                // Track download event
                if (typeof gtag === 'function') {
                    gtag('event', 'qr_download', {
                        'event_category': 'Engagement',
                        'event_label': '{{ library.library_name }} QR Code'
                    });
                }
            } catch (error) {
                console.error('Download failed:', error);
                showToast('Download failed. Please try again.', 'error');
            }
        }

        // Share QR function
        function shareQR() {
            try {
                if (navigator.share) {
                    navigator.share({
                        title: '{{ library.library_name }} - Student Registration',
                        text: 'Register as a student at {{ library.library_name }} using this QR code',
                        url: window.location.href
                    }).then(() => {
                        showToast('QR Code shared successfully!', 'success');
                    }).catch((error) => {
                        console.error('Share failed:', error);
                        fallbackShare();
                    });
                } else {
                    fallbackShare();
                }
            } catch (error) {
                console.error('Share failed:', error);
                fallbackShare();
            }
        }

        // Fallback share function
        function fallbackShare() {
            try {
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(() => {
                    showToast('Registration link copied to clipboard!', 'success');
                }).catch(() => {
                    showToast('Unable to share. Please copy the URL manually.', 'error');
                });
            } catch (error) {
                showToast('Unable to share. Please copy the URL manually.', 'error');
            }
        }

        // Enhanced copy function with toast feedback
        function copyToClipboard(elementId) {
            try {
                const text = document.getElementById(elementId).textContent || document.getElementById(elementId).innerText;
                navigator.clipboard.writeText(text).then(() => {
                    showToast('Copied to clipboard!', 'success');
                }).catch(err => {
                    console.error('Failed to copy text: ', err);
                    showToast('Failed to copy. Please try again.', 'error');
                });
            } catch (error) {
                console.error('Copy failed:', error);
                showToast('Copy failed. Please try again.', 'error');
            }
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling to all links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });

            // Form validation enhancement
            const contactForm = document.querySelector('.contact-form');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    const name = this.querySelector('#name').value.trim();
                    const email = this.querySelector('#email').value.trim();
                    const message = this.querySelector('#message').value.trim();

                    if (!name || !email || !message) {
                        e.preventDefault();
                        showToast('Please fill in all required fields.', 'error');
                        return;
                    }

                    if (!email.includes('@')) {
                        e.preventDefault();
                        showToast('Please enter a valid email address.', 'error');
                        return;
                    }

                    showToast('Sending message...', 'info');
                });
            }

            // Track page view
            if (typeof gtag === 'function') {
                gtag('event', 'library_detail_view', {
                    'event_category': 'Engagement',
                    'event_label': '{{ library.library_name }}'
                });
            }
        });
    </script>
<script>
    window.addEventListener("load", function () {
        const path = window.location.pathname; // Get current page path
        let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object

        // Increment count for the current path
        pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

        // Store updated data back to localStorage
        localStorage.setItem("page_data", JSON.stringify(pageData));
      });

      // Function to send page view data
      function sendPageData() {
        const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

        if (Object.keys(pageData).length > 0) {
          fetch(location.origin + "/librarian/track-page-view/", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-CSRFToken": "{{ csrf_token }}",
            },
            body: JSON.stringify(pageData),
          })

            .then(() => {
              localStorage.removeItem("page_data");
            })
            .catch((error) => console.error("Error sending page data:", error));
            localStorage.removeItem("page_data");
        } else {

          console.log("No page data to send");
        }
      }

      // Send data every 10 seconds
      setInterval(sendPageData, 10000);
</script>
</body>

</html>
