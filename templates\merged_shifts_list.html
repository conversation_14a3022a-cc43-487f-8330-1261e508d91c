{% extends 'base.html' %}
{% load static %}

{% block title %}Merged Shifts{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Merged Shifts</h3>
                    <a href="{% url 'create_merged_shift' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Merged Shift
                    </a>
                </div>
                <div class="card-body">
                    {% if merged_shifts %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Included Shifts</th>
                                        <th>Time Ranges</th>
                                        <th>Price</th>
                                        <th>Discount</th>
                                        <th>Final Price</th>
                                        <th>Available Seats</th>
                                        <th>Total Duration</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for merged_shift in merged_shifts %}
                                    <tr>
                                        <td><strong>{{ merged_shift.name }}</strong></td>
                                        <td>{{ merged_shift.get_shift_names }}</td>
                                        <td>
                                            {% for time_range in merged_shift.get_time_ranges %}
                                                <span class="badge badge-info">{{ time_range }}</span><br>
                                            {% endfor %}
                                        </td>
                                        <td>₹{{ merged_shift.price }}</td>
                                        <td>{{ merged_shift.discount }}%</td>
                                        <td><strong>₹{{ merged_shift.calculate_final_price }}</strong></td>
                                        <td>
                                            <span class="badge badge-success">
                                                {{ merged_shift.availability_info.available_count }} seats
                                            </span>
                                        </td>
                                        <td>{{ merged_shift.get_total_duration_minutes }} min</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'book_merged_shift_seat' merged_shift.id %}" 
                                                   class="btn btn-sm btn-success" title="Book Seat">
                                                    <i class="fas fa-chair"></i>
                                                </a>
                                                <a href="{% url 'mark_merged_shift_attendance' merged_shift.id %}" 
                                                   class="btn btn-sm btn-info" title="Mark Attendance">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h4>No Merged Shifts Found</h4>
                            <p class="text-muted">Create your first merged shift to get started.</p>
                            <a href="{% url 'create_merged_shift' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Merged Shift
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge {
    margin: 2px;
}
.btn-group .btn {
    margin: 1px;
}
</style>
{% endblock %}
