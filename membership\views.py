import json
import random
import string
import razorpay
from django.conf import settings
from django.shortcuts import render, redirect
from django.utils import timezone
from .models import *
from librarian.models import *
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_protect
from wallet_and_transactions.models import *
from django.shortcuts import render
from django.http import JsonResponse
from .models import Coupon
from datetime import datetime
from django.views.decorators.http import require_http_methods
from django.contrib import messages


# Initialize Razorpay client
client = razorpay.Client(auth=(settings.RAZORPAY_KEY_ID, settings.RAZORPAY_KEY_SECRET))


@login_required(login_url="/librarian/login/")
def plan_list(request):
    plans = Plan.objects.all().order_by("price")[1:]
    return render(request, "package.html", {"plans": plans})


@login_required(login_url="/librarian/login/")
def sms_plan_list(request):
    sms_plans = SmsPlan.objects.all().order_by("price")
    return render(request, "package.html", {"smsplans": sms_plans})


@login_required(login_url="/librarian/login/")
def pre_package(request, id):
    try:
        plan = Plan.objects.get(id=id)
    except Plan.DoesNotExist:
        return redirect("plan_list")

    # Get num_months from URL parameters or default to 1
    num_months = int(request.GET.get('num_months', 1))

    # Calculate base amount
    base_amount = plan.discount_price * num_months

    # Apply duration-based discounts (only for specific months)
    discount_percentage = 0
    if num_months == 3:
        discount_percentage = 5
    elif num_months == 6:
        discount_percentage = 10
    elif num_months == 12:
        discount_percentage = 20
    # All other months (1, 2, 4, 5, 7, 8, 9, 10, 11) have no discount

    # Calculate final amount after discount
    discount_amount = base_amount * discount_percentage / 100
    final_amount = base_amount - discount_amount

    # Amount in paise for Razorpay
    order_amount = int(final_amount * 100)

    context = {
        "amount": order_amount,
        "plan": plan,
        "num_months": num_months,
        "base_amount": base_amount,
        "discount_percentage": discount_percentage,
        "discount_amount": discount_amount,
        "final_amount": final_amount,
    }
    return render(request, "pre_payment_modern.html", context)


@login_required(login_url="/librarian/login/")
def create_order(request, id):
    try:
        plan = Plan.objects.get(id=id)
    except Plan.DoesNotExist:
        return redirect("plan_list")

    # Get num_months from POST data or URL parameters
    num_months_str = request.POST.get("num_months") or request.GET.get("num_months", "1")
    try:
        num_months = float(num_months_str)
    except ValueError:
        num_months = 1.0

    # Get grand total from form (already calculated with discounts)
    grand_total = float(request.POST.get("grand_total", plan.discount_price))

    # Calculate order amount in paise (multiplying by 100)
    order_amount1 = grand_total * 100  # Amount in paise

    currency = "INR"
    receipt = f"order_rcptid_{int(timezone.now().timestamp())}"

    coupon_code = request.POST.get("coupon_code", "")
    discount = 0  # Default no discount

    if coupon_code:
        try:
            coupon = Coupon.objects.get(
                code=coupon_code, is_active=True, expiry_date__gte=timezone.now().date()
            )
            # Validate the coupon for the plan duration
            if (
                (plan.duration_months == 1 and coupon.discount == 20)
                or (plan.duration_months == 3 and coupon.discount == 30)
                or (plan.duration_months >= 6 and coupon.discount == 50)
            ):
                discount = coupon.discount
                # Deactivate the coupon after use
                coupon.is_active = False
                coupon.save()
            else:
                messages.error(request, "Coupon is not applicable for this plan.")
                return redirect("checkout")
        except Coupon.DoesNotExist:
            messages.error(request, "Invalid or expired coupon code.")
            return redirect("checkout")

    try:
        order = client.order.create(  # type: ignore
            dict(
                amount=order_amount1,
                currency=currency,
                receipt=receipt,
                payment_capture="1",
            )
        )
    except razorpay.errors.RazorpayError as e:  # type: ignore
        return JsonResponse({
            "success": False,
            "message": f"Error creating payment order: {str(e)}"
        })

    response_data = {
        "success": True,
        "order_id": order["id"],
        "razorpay_key": settings.RAZORPAY_KEY_ID,
        "amount": order_amount1,
        "currency": currency,
        "receipt": receipt,
        "plan_name": plan.name,
        "num_months": num_months,
        "coupon_code": coupon_code,
        "discount": discount,
    }

    return JsonResponse(response_data)


@csrf_protect  # Ensure CSRF protection
def verify_payment(request):
    # Create a debug log file
    import os
    import datetime

    log_file = "/tmp/payment_debug.log"

    def log_debug(message):
        with open(log_file, "a") as f:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"[{timestamp}] {message}\n")

    log_debug("=== PAYMENT VERIFICATION STARTED ===")
    log_debug(f"Request method: {request.method}")
    log_debug(f"User: {request.user}")
    log_debug(f"Is authenticated: {request.user.is_authenticated}")

    if request.method == "POST":
        razorpay_order_id = request.POST.get("razorpay_order_id")
        razorpay_payment_id = request.POST.get("razorpay_payment_id")
        razorpay_signature = request.POST.get("razorpay_signature")
        plan_id = request.POST.get("plan_id")
        # Use GET to retrieve num_months passed in the URL query string
        num_months_str = request.POST.get("num_months", "1")
        try:
            num_months = float(num_months_str)
        except ValueError:
            num_months = 1.0

        log_debug(f"Received payment data:")
        log_debug(f"  razorpay_order_id: {razorpay_order_id}")
        log_debug(f"  razorpay_payment_id: {razorpay_payment_id}")
        log_debug(f"  razorpay_signature: {razorpay_signature}")
        log_debug(f"  plan_id: {plan_id}")
        log_debug(f"  num_months: {num_months}")

        # Log all POST data for debugging
        log_debug(f"All POST data: {dict(request.POST)}")

        if not all(
            [razorpay_order_id, razorpay_payment_id, razorpay_signature, plan_id]
        ):
            missing_fields = [field for field, value in [('order_id', razorpay_order_id), ('payment_id', razorpay_payment_id), ('signature', razorpay_signature), ('plan_id', plan_id)] if not value]
            log_debug(f"Missing payment or plan information: {missing_fields}")
            log_debug("REDIRECTING TO FAILURE - Missing fields")
            return redirect("failure")

        try:
            # Check if user is authenticated
            if not request.user.is_authenticated:
                log_debug("User is not authenticated during payment verification")
                log_debug("REDIRECTING TO FAILURE - Not authenticated")
                return redirect("failure")

            log_debug(f"Payment verification started for user: {request.user.username}")
            log_debug(f"User ID: {request.user.id}")

            # Verify Razorpay signature
            params_dict = {
                'razorpay_order_id': razorpay_order_id,
                'razorpay_payment_id': razorpay_payment_id,
                'razorpay_signature': razorpay_signature
            }

            log_debug("Starting Razorpay signature verification...")
            try:
                client.utility.verify_payment_signature(params_dict)
                log_debug("Razorpay signature verification successful")
            except razorpay.errors.SignatureVerificationError as e:
                log_debug(f"Razorpay signature verification failed: {e}")
                log_debug("REDIRECTING TO FAILURE - Signature verification failed")
                return redirect("failure")
            except Exception as e:
                log_debug(f"Unexpected error during signature verification: {e}")
                log_debug("REDIRECTING TO FAILURE - Signature verification error")
                return redirect("failure")

            # Ensure the user is authenticated and get the librarian object
            log_debug("Looking for Librarian_param...")
            try:
                library = Librarian_param.objects.get(user=request.user)
                log_debug(f"Found librarian: {library}")
                log_debug(f"Librarian ID: {library.id}, Library name: {library.library_name}")
            except Librarian_param.DoesNotExist:
                log_debug(f"Librarian_param not found for user: {request.user.username}")
                log_debug("REDIRECTING TO FAILURE - Librarian not found")
                return redirect("failure")

            log_debug("Looking for Plan...")
            plan = Plan.objects.get(id=plan_id)
            log_debug(f"Found plan: {plan.name} (ID: {plan.id})")

            # Create or update the membership record
            log_debug("Looking for existing membership...")
            try:
                membership = Membership.objects.get(librarian=library)
                log_debug(f"Found existing membership: {membership}")
            except Membership.DoesNotExist:
                log_debug("Creating new membership with plan")
                # Create membership with the plan (plan field is required)
                membership = Membership.objects.create(librarian=library, plan=plan)

            current_date = timezone.now().date()
            membership.plan = plan
            membership.start_date = current_date
            # Calculate expiry date based on num_months (not plan.duration_months)
            membership.expiry_date = current_date + timezone.timedelta(
                days=30 * num_months
            )
            membership.razorpay_order_id = razorpay_order_id
            membership.razorpay_payment_id = razorpay_payment_id
            membership.razorpay_signature = razorpay_signature

            log_debug("Saving membership...")
            membership.save()
            log_debug(f"Membership updated: {membership}")

            # Create a transaction for the librarian's wallet
            log_debug("Looking for user wallet...")
            try:
                user_wallet = Wallet.objects.get(user=request.user)
                log_debug(f"Found wallet with balance: {user_wallet.balance}")
            except Wallet.DoesNotExist:
                log_debug("Creating new wallet")
                user_wallet = Wallet.objects.create(user=request.user, balance=0)

            amount = plan.sms_quantity * num_months
            note = f"Librarian selected plan {plan} and received {amount} points"

            log_debug(f"Creating transaction: amount={amount}, note={note}")
            transaction = Transaction.objects.create(
                wallet=user_wallet, amount=amount, is_credit=True, note=note
            )

            # Add to existing balance instead of replacing it
            old_balance = user_wallet.balance
            user_wallet.balance += transaction.amount
            user_wallet.save()

            log_debug(f"Transaction created: {transaction}")
            log_debug(f"Wallet updated: {old_balance} + {transaction.amount} = {user_wallet.balance}")

            log_debug("SUCCESS! Redirecting to success page")
            return redirect("success")

        except Plan.DoesNotExist:
            log_debug(f"Invalid plan ID: {plan_id}")
            log_debug("REDIRECTING TO FAILURE - Plan not found")
            return redirect("failure")
        except Exception as e:
            log_debug(f"Error during payment verification: {str(e)}")
            log_debug(f"Error type: {type(e).__name__}")
            import traceback
            log_debug(f"Traceback: {traceback.format_exc()}")
            log_debug("REDIRECTING TO FAILURE - Exception occurred")
            return redirect("failure")
    else:
        log_debug(f"Invalid request method: {request.method}")
        log_debug("REDIRECTING TO PLAN LIST - Invalid method")
        return redirect("plan_list")


@login_required(login_url="/librarian/login/")
def create_sms_order(request, id):
    try:
        plan = SmsPlan.objects.get(id=id)
    except SmsPlan.DoesNotExist:
        return redirect("sms_plan_list")

    # Create Razorpay Order
    order_amount = float(plan.price * 100)  # Amount in paise
    currency = "INR"
    receipt = f"order_rcptid_{int(timezone.now().timestamp())}"

    try:
        order = client.order.create(  # type: ignore
            dict(
                amount=order_amount,
                currency=currency,
                receipt=receipt,
                payment_capture="1",
            )
        )
    except razorpay.errors.RazorpayError as e:  # type: ignore
        return redirect("failure")

    context = {
        "order_id": order["id"],
        "razorpay_key": settings.RAZORPAY_KEY_ID,
        "amount": order_amount,
        "currency": currency,
        "receipt": receipt,
        "plan": plan,
    }
    return render(request, "sms_checkout.html", context)


@login_required(login_url="/librarian/login/")
@csrf_protect  # Ensure CSRF protection
def sms_verify_payment(request):
    if request.method == "POST":
        razorpay_order_id = request.POST.get("razorpay_order_id")
        razorpay_payment_id = request.POST.get("razorpay_payment_id")
        razorpay_signature = request.POST.get("razorpay_signature")
        smsplan_id = request.POST.get("plan_id")

        if not all(
            [razorpay_order_id, razorpay_payment_id, razorpay_signature, smsplan_id]
        ):
            return redirect("failure")

        try:
            # Ensure the user is authenticated and get the librarian object
            user = request.user
            plan = SmsPlan.objects.get(id=smsplan_id)

            user_wallet = Wallet.objects.get(user=user)

            user_wallet.razorpay_order_id = razorpay_order_id
            user_wallet.razorpay_payment_id = razorpay_payment_id
            user_wallet.razorpay_signature = razorpay_signature

            amount = plan.sms_quantity
            note = f"Librarian select sms plan {plan} and give {amount} points"

            transaction = Transaction.objects.create(
                wallet=user_wallet, amount=amount, is_credit=True, note=note
            )
            user_wallet.balance = transaction.amount
            user_wallet.save()

            return redirect("success")

        except SmsPlan.DoesNotExist:
            return redirect("failure")
        except razorpay.errors.SignatureVerificationError as e:  # type: ignore
            return redirect("failure")
        except Exception as e:
            return redirect("failure")
    else:
        return redirect("plan_list")


@login_required(login_url="/librarian/login/")
def success(request):
    return render(request, "success.html")


@csrf_protect
@require_http_methods(["GET", "POST"])
def admin_generate_coupons(request):
    if request.method == "POST":
        try:
            discount = int(request.POST.get("discount"))
            count = int(request.POST.get("count"))
            expiry_date = request.POST.get("expiry_date")

            if count < 1 or count > 1000:
                return JsonResponse(
                    {
                        "success": False,
                        "error": "Number of coupons must be between 1 and 1000",
                    },
                    status=400,
                )

            expiry_date_obj = datetime.strptime(expiry_date, "%Y-%m-%d")

            generated_coupons = []
            for _ in range(count):

                while True:
                    code = "".join(
                        random.choices(string.ascii_uppercase + string.digits, k=10)
                    )

                    if not Coupon.objects.filter(code=code).exists():
                        break

                coupon = Coupon.objects.create(
                    code=code, discount=discount, expiry_date=expiry_date_obj
                )
                generated_coupons.append(coupon.code)

            return JsonResponse(
                {"success": True, "generated_coupons": generated_coupons}
            )

        except ValueError:

            return JsonResponse(
                {"success": False, "error": "Invalid input values"}, status=400
            )

        except Exception as e:

            return JsonResponse({"success": False, "error": str(e)}, status=500)

    generated_coupons = Coupon.objects.all()
    return render(request, "create_coupons.html", {"all_coupons": generated_coupons})


def validate_coupon(request):
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            coupon_code = data.get("coupon_code", "")

            if not coupon_code:
                return JsonResponse(
                    {"valid": False, "message": "Coupon code is required."}
                )

            valid_coupons = Coupon.objects.filter(code=coupon_code, is_active=True)

            if valid_coupons.exists():
                return JsonResponse(
                    {"valid": True, "message": "Coupon applied successfully!", "discount": valid_coupons[0].discount, "discount_type": valid_coupons[0].discount_type} )
            else:
                return JsonResponse({"valid": False, "message": "Invalid coupon code."})

        except json.JSONDecodeError:
            return JsonResponse(
                {"valid": False, "message": "Invalid JSON format in request."}
            )
        except Exception as e:
            return JsonResponse(
                {"valid": False, "message": f"An error occurred: {str(e)}"}
            )

    return JsonResponse({"valid": False, "message": "Invalid request method."})


@login_required(login_url="/librarian/login/")
def failure(request):
    return render(request, "failure.html")


def test_payment_verification(request):
    """Test endpoint to debug payment verification issues"""
    if request.method == "GET":
        # Show test form
        return render(request, "test_payment.html")

    elif request.method == "POST":
        # Test the verification process
        try:
            # Get user info
            user_info = {
                "user": str(request.user),
                "is_authenticated": request.user.is_authenticated,
                "user_id": request.user.id if request.user.is_authenticated else None
            }

            # Check if librarian exists
            librarian_info = {}
            if request.user.is_authenticated:
                try:
                    librarian = Librarian_param.objects.get(user=request.user)
                    librarian_info = {
                        "exists": True,
                        "id": librarian.id,
                        "library_name": librarian.library_name,
                        "is_librarian": librarian.is_librarian
                    }
                except Librarian_param.DoesNotExist:
                    librarian_info = {"exists": False}

            # Check membership
            membership_info = {}
            if librarian_info.get("exists"):
                try:
                    membership = Membership.objects.get(librarian_id=librarian_info["id"])
                    membership_info = {
                        "exists": True,
                        "plan": str(membership.plan),
                        "start_date": str(membership.start_date),
                        "expiry_date": str(membership.expiry_date)
                    }
                except Membership.DoesNotExist:
                    membership_info = {"exists": False}

            # Check wallet
            wallet_info = {}
            if request.user.is_authenticated:
                try:
                    wallet = Wallet.objects.get(user=request.user)
                    wallet_info = {
                        "exists": True,
                        "balance": float(wallet.balance)
                    }
                except Wallet.DoesNotExist:
                    wallet_info = {"exists": False}

            # Check plans
            plans = Plan.objects.all()
            plan_info = [{"id": p.id, "name": p.name, "price": float(p.discount_price)} for p in plans]

            response_data = {
                "user_info": user_info,
                "librarian_info": librarian_info,
                "membership_info": membership_info,
                "wallet_info": wallet_info,
                "plans": plan_info,
                "razorpay_configured": bool(settings.RAZORPAY_KEY_ID and settings.RAZORPAY_KEY_SECRET)
            }

            return JsonResponse(response_data, indent=2)

        except Exception as e:
            return JsonResponse({
                "error": str(e),
                "error_type": type(e).__name__
            })
