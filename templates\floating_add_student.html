<!-- Floating Add Student Button -->
<div class="floating-add-student">
    <a href="/students/create/" class="fab-add-student" title="Add New Student">
        <i class="fas fa-user-plus"></i>
        <span class="fab-text">Add Student</span>
    </a>
</div>

<style>
    /* Floating Action Button Styles */
    .floating-add-student {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 1000;
    }

    .fab-add-student {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: #10b981;
        color: white;
        border: none;
        border-radius: 50px;
        padding: 1rem 1.5rem;
        font-weight: 700;
        font-size: 0.9rem;
        text-decoration: none;
        box-shadow: 0 8px 32px rgba(147, 51, 234, 0.6), 0 4px 16px rgba(168, 85, 247, 0.4);
        transition: all 0.3s ease;
        min-width: 60px;
        justify-content: center;
        border: 2px solid #059669;
    }

    .fab-add-student:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 12px 40px rgba(147, 51, 234, 0.8), 0 6px 20px rgba(168, 85, 247, 0.6);
        background: #059669;
        color: white;
        text-decoration: none;
        border-color: #047857;
    }

    .fab-add-student:active {
        transform: translateY(-1px) scale(1.02);
    }

    .fab-add-student i {
        font-size: 1.1rem;
        transition: transform 0.3s ease;
    }

    .fab-add-student:hover i {
        transform: rotate(90deg);
    }

    .fab-text {
        white-space: nowrap;
        transition: all 0.3s ease;
    }

    /* Hide completely on mobile - users will use sidebar */
    @media (max-width: 768px) {
        .floating-add-student {
            display: none !important;
        }
    }

    /* Hide on certain pages if needed */
    .hide-fab .floating-add-student {
        display: none;
    }

    /* Hide on Add Student page specifically */
    .floating-add-student.hide-on-add-page {
        display: none !important;
    }

    /* Animation on page load */
    @keyframes fabSlideIn {
        from {
            transform: translateX(100px);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .floating-add-student {
        animation: fabSlideIn 0.5s ease-out 0.5s both;
    }

    /* Pulse animation for attention */
    @keyframes fabPulse {
        0% {
            box-shadow: 0 8px 32px rgba(147, 51, 234, 0.6), 0 4px 16px rgba(168, 85, 247, 0.4);
        }
        50% {
            box-shadow: 0 12px 40px rgba(147, 51, 234, 0.8), 0 6px 20px rgba(168, 85, 247, 0.6);
        }
        100% {
            box-shadow: 0 8px 32px rgba(147, 51, 234, 0.6), 0 4px 16px rgba(168, 85, 247, 0.4);
        }
    }

    .fab-add-student.pulse {
        animation: fabPulse 2s infinite;
    }

    /* Dark mode adjustments */
    [data-theme="dark"] .fab-add-student {
        background: #10b981;
        border: 2px solid #059669;
    }

    [data-theme="dark"] .fab-add-student:hover {
        background: #059669;
        border-color: #047857;
    }
</style>

<script>
    // Add some interactivity to the floating button
    document.addEventListener('DOMContentLoaded', function() {
        const fab = document.querySelector('.fab-add-student');
        const fabContainer = document.querySelector('.floating-add-student');

        if (fab && fabContainer) {
            // Check if we're on the Add Student page
            const currentPath = window.location.pathname;
            const isAddStudentPage = currentPath.includes('/students/create/') ||
                                   currentPath.includes('/students/create') ||
                                   currentPath.endsWith('/students/create');

            // Hide the floating button on Add Student page
            if (isAddStudentPage) {
                fabContainer.classList.add('hide-on-add-page');
                return; // Exit early, don't add other interactions
            }

            // Add pulse effect on first visit (optional)
            setTimeout(() => {
                fab.classList.add('pulse');
                setTimeout(() => {
                    fab.classList.remove('pulse');
                }, 4000);
            }, 2000);

            // Remove pulse on hover
            fab.addEventListener('mouseenter', function() {
                this.classList.remove('pulse');
            });

            // Smooth scroll behavior when clicking
            fab.addEventListener('click', function(e) {
                // Add a small loading state
                const icon = this.querySelector('i');
                const originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin';

                setTimeout(() => {
                    icon.className = originalClass;
                }, 500);
            });
        }
    });
</script>
