{% extends "base.html" %}

{% block title %}Upgrade Your Subscription Plan{% endblock %}

{% block page_title %}Upgrade Plan{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Upgrade Plan</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Upgrade Plan Glass Theme */
    .upgrade-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: calc(100vh - var(--topbar-height));
        padding: 2rem;
        position: relative;
    }

    .upgrade-content::before {
        content: '';
        position: fixed;
        top: var(--topbar-height);
        left: 0;
        width: 100%;
        height: calc(100% - var(--topbar-height));
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    .upgrade-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset,
            0 2px 4px rgba(255, 255, 255, 0.1) inset;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .upgrade-card:hover {
        transform: translateY(-2px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .upgrade-header {
        padding: 2rem 2rem 1rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        text-align: center;
    }

    .upgrade-title {
        font-family: 'Plus Jakarta Sans', sans-serif;
        font-weight: 700;
        font-size: 2rem;
        color: white;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .upgrade-subtitle {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }

    .upgrade-body {
        padding: 2rem;
    }

    .current-plan-section {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .current-plan-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
    }

    .section-title {
        font-family: 'Plus Jakarta Sans', sans-serif;
        font-weight: 700;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-icon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        font-size: 1rem;
    }

    .plan-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
    }

    .plan-detail-item {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .plan-detail-item:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
    }

    .detail-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        font-size: 1.25rem;
        color: white;
        flex-shrink: 0;
    }

    .detail-content {
        flex: 1;
    }

    .detail-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.7);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.25rem;
    }

    .detail-value {
        font-size: 1rem;
        font-weight: 500;
        color: white;
        word-break: break-word;
    }

    /* Upgrade Benefits Section */
    .upgrade-benefits {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .upgrade-benefits:hover {
        transform: translateY(-2px);
        box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
    }

    .benefits-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .benefit-item {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .benefit-item:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
    }

    .benefit-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(16, 185, 129, 0.3);
        border: 1px solid rgba(16, 185, 129, 0.5);
        border-radius: 50%;
        font-size: 1.25rem;
        color: white;
        margin: 0 auto 1rem auto;
    }

    .benefit-title {
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .benefit-description {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.4;
    }

    /* Action Buttons */
    .upgrade-actions {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 2rem;
        flex-wrap: wrap;
    }

    .action-btn {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem 2rem;
        color: white;
        text-decoration: none;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        min-width: 180px;
        justify-content: center;
    }

    .action-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .action-btn.primary {
        background: rgba(99, 102, 241, 0.3);
        border-color: rgba(99, 102, 241, 0.5);
    }

    .action-btn.primary:hover {
        background: rgba(99, 102, 241, 0.4);
    }

    .action-btn.secondary {
        background: rgba(107, 114, 128, 0.3);
        border-color: rgba(107, 114, 128, 0.5);
    }

    .action-btn.secondary:hover {
        background: rgba(107, 114, 128, 0.4);
    }

    /* Status Badge */
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: capitalize;
        background: rgba(16, 185, 129, 0.2);
        color: #10b981;
        border: 1px solid rgba(16, 185, 129, 0.3);
        margin-bottom: 1rem;
    }

    .status-badge.warning {
        background: rgba(245, 158, 11, 0.2);
        color: #f59e0b;
        border-color: rgba(245, 158, 11, 0.3);
    }

    .status-badge.danger {
        background: rgba(239, 68, 68, 0.2);
        color: #ef4444;
        border-color: rgba(239, 68, 68, 0.3);
    }

    /* Mobile Responsive */
    @media (max-width: 991.98px) {
        .upgrade-content {
            padding: 1rem;
            padding-bottom: calc(1rem + var(--bottom-menu-height));
        }
    }

    @media (max-width: 767.98px) {
        .upgrade-content {
            padding: 0.75rem;
            padding-bottom: calc(0.75rem + var(--bottom-menu-height));
        }

        .upgrade-header,
        .upgrade-body {
            padding: 1.5rem;
        }

        .upgrade-title {
            font-size: 1.5rem;
        }

        .plan-details-grid {
            grid-template-columns: 1fr;
        }

        .benefits-grid {
            grid-template-columns: 1fr;
        }

        .upgrade-actions {
            flex-direction: column;
            align-items: center;
        }

        .action-btn {
            width: 100%;
            max-width: 300px;
        }

        .plan-detail-item {
            flex-direction: column;
            text-align: center;
            gap: 0.75rem;
        }
    }

    /* Disable zoom and text selection */
    body {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        touch-action: manipulation;
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent;
    }

    /* Animation for page load */
    .upgrade-card,
    .current-plan-section,
    .upgrade-benefits {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Staggered animations */
    .plan-detail-item {
        animation: fadeInUp 0.6s ease-out forwards;
        opacity: 0;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}
{% block content %}
<div class="upgrade-content">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-10 col-xl-8">
                <!-- Main Upgrade Card -->
                <div class="upgrade-card">
                    <!-- Upgrade Header -->
                    <div class="upgrade-header">
                        <h1 class="upgrade-title">Upgrade Your Subscription Plan</h1>
                        <p class="upgrade-subtitle">Review your current plan and explore premium options to enhance your experience.</p>
                    </div>

                    <!-- Upgrade Body -->
                    <div class="upgrade-body">
                        <!-- Current Plan Status -->
                        {% if membership.expiry_date %}
                            {% now "Y-m-d" as today %}
                            {% if membership.expiry_date|date:"Y-m-d" < today %}
                                <div class="status-badge danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Plan Expired
                                </div>
                            {% elif membership.expiry_date|timeuntil|slice:":2" == "1 " %}
                                <div class="status-badge warning">
                                    <i class="fas fa-clock"></i>
                                    Expires Soon
                                </div>
                            {% else %}
                                <div class="status-badge">
                                    <i class="fas fa-check-circle"></i>
                                    Active Plan
                                </div>
                            {% endif %}
                        {% endif %}

                        <!-- Current Plan Details -->
                        <div class="current-plan-section">
                            <h2 class="section-title">
                                <div class="section-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                Current Plan Details
                            </h2>

                            <div class="plan-details-grid">
                                <!-- Plan Name -->
                                <div class="plan-detail-item">
                                    <div class="detail-icon">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <div class="detail-content">
                                        <div class="detail-label">Plan Name</div>
                                        <div class="detail-value">{{membership.plan.name}}</div>
                                    </div>
                                </div>

                                <!-- Price -->
                                <div class="plan-detail-item">
                                    <div class="detail-icon">
                                        <i class="fas fa-tag"></i>
                                    </div>
                                    <div class="detail-content">
                                        <div class="detail-label">Price</div>
                                        <div class="detail-value">₹{{membership.plan.price}}</div>
                                    </div>
                                </div>

                                <!-- Start Date -->
                                <div class="plan-detail-item">
                                    <div class="detail-icon">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div class="detail-content">
                                        <div class="detail-label">Start Date</div>
                                        <div class="detail-value">{{membership.start_date}}</div>
                                    </div>
                                </div>

                                <!-- Expiry Date -->
                                <div class="plan-detail-item">
                                    <div class="detail-icon">
                                        <i class="fas fa-calendar-check"></i>
                                    </div>
                                    <div class="detail-content">
                                        <div class="detail-label">Expiry Date</div>
                                        <div class="detail-value">{{membership.expiry_date}}</div>
                                    </div>
                                </div>

                                <!-- Features -->
                                <div class="plan-detail-item" style="grid-column: 1 / -1;">
                                    <div class="detail-icon">
                                        <i class="fas fa-list"></i>
                                    </div>
                                    <div class="detail-content">
                                        <div class="detail-label">Features</div>
                                        <div class="detail-value">
                                            {% if membership.plan.description_line_01 %}{{membership.plan.description_line_01}}{% endif %}
                                            {% if membership.plan.description_line_02 %}, {{membership.plan.description_line_02}}{% endif %}
                                            {% if membership.plan.description_line_03 %}, {{membership.plan.description_line_03}}{% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Upgrade Benefits -->
                        <div class="upgrade-benefits">
                            <h2 class="section-title">
                                <div class="section-icon">
                                    <i class="fas fa-star"></i>
                                </div>
                                Why Upgrade?
                            </h2>

                            <div class="benefits-grid">
                                <div class="benefit-item">
                                    <div class="benefit-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="benefit-title">More Students</div>
                                    <div class="benefit-description">Increase your student capacity and grow your library</div>
                                </div>

                                <div class="benefit-item">
                                    <div class="benefit-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="benefit-title">Advanced Analytics</div>
                                    <div class="benefit-description">Get detailed insights and reports for better management</div>
                                </div>

                                <div class="benefit-item">
                                    <div class="benefit-icon">
                                        <i class="fas fa-headset"></i>
                                    </div>
                                    <div class="benefit-title">Priority Support</div>
                                    <div class="benefit-description">Get faster response times and dedicated assistance</div>
                                </div>

                                <div class="benefit-item">
                                    <div class="benefit-icon">
                                        <i class="fas fa-tools"></i>
                                    </div>
                                    <div class="benefit-title">Premium Features</div>
                                    <div class="benefit-description">Access exclusive tools and advanced functionality</div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="upgrade-actions">
                            <a href="/membership/plans/" class="action-btn primary">
                                <i class="fas fa-rocket"></i>
                                Upgrade Plan
                            </a>
                            <a href="/{{ role }}/dashboard/" class="action-btn secondary">
                                <i class="fas fa-arrow-left"></i>
                                Return to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Upgrade Plan Page Functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize page animations
        initializeAnimations();

        // Check plan status and show appropriate messages
        checkPlanStatus();

        // Removed page load success message
    });

    function initializeAnimations() {
        // Add staggered animation to plan detail items
        const planItems = document.querySelectorAll('.plan-detail-item');
        planItems.forEach((item, index) => {
            item.style.animationDelay = `${index * 0.1}s`;
        });

        // Add staggered animation to benefit items
        const benefitItems = document.querySelectorAll('.benefit-item');
        benefitItems.forEach((item, index) => {
            item.style.animationDelay = `${(index * 0.1) + 0.3}s`;
            item.style.animation = 'fadeInUp 0.6s ease-out forwards';
            item.style.opacity = '0';
        });
    }

    function checkPlanStatus() {
        const statusBadge = document.querySelector('.status-badge');
        if (statusBadge) {
            if (statusBadge.classList.contains('danger')) {
                // Plan expired
                if (window.modernDashboard) {
                    window.modernDashboard.showToast(
                        'Plan Expired',
                        'Your subscription plan has expired. Please upgrade to continue using premium features.',
                        'error'
                    );
                }
            } else if (statusBadge.classList.contains('warning')) {
                // Plan expires soon
                if (window.modernDashboard) {
                    window.modernDashboard.showToast(
                        'Plan Expires Soon',
                        'Your subscription plan will expire soon. Consider upgrading to avoid service interruption.',
                        'warning'
                    );
                }
            }
        }
    }

    // Enhanced upgrade button functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.action-btn.primary')) {
            // Track upgrade button click
            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Redirecting',
                    'Taking you to upgrade options...',
                    'info'
                );
            }
        }
    });

    // Add hover effects for benefit items
    document.querySelectorAll('.benefit-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-2px) scale(1)';
        });
    });

    // Add click tracking for analytics
    document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            const action = this.textContent.trim();
            console.log(`User clicked: ${action}`);

            // You can add analytics tracking here
            // Example: gtag('event', 'click', { 'event_category': 'upgrade', 'event_label': action });
        });
    });
</script>
{% endblock %}
