{% extends "base.html" %}
{% load static %}

{% block title %}Partial Payments - {{ librarian.library_name }}{% endblock %}

{% block extra_css %}
<!-- Bootstrap CSS (if not already included) -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: #667eea;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6b7280;
        font-weight: 500;
    }

    .payments-container {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .filters-section {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .filter-input {
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 10px;
        font-size: 1rem;
        min-width: 200px;
    }

    .filter-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* Desktop Table View */
    .payments-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;
    }

    .payments-table th,
    .payments-table td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
    }

    .payments-table th {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        font-weight: 600;
        color: #374151;
        position: sticky;
        top: 0;
    }

    .payments-table tr:hover {
        background-color: rgba(102, 126, 234, 0.05);
    }

    /* Mobile Card View */
    .payments-cards {
        display: none;
        gap: 1rem;
    }

    .payment-card {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid #e5e7eb;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

    .payment-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .student-name {
        font-weight: 600;
        color: #1f2937;
        font-size: 1.1rem;
    }

    .invoice-id {
        color: #6b7280;
        font-size: 0.875rem;
    }

    .card-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
    }

    .detail-label {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
    }

    .detail-value {
        font-weight: 500;
        color: #1f2937;
    }

    .progress-section {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
    }

    .progress-bar-container {
        background-color: #f3f4f6;
        border-radius: 10px;
        height: 8px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #667eea, #764ba2);
        transition: width 0.3s ease;
    }

    .progress-text {
        font-size: 0.875rem;
        color: #6b7280;
        text-align: center;
    }

    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-partial {
        background-color: #fef3c7;
        color: #d97706;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-outline {
        background: transparent;
        color: #667eea;
        border: 1px solid #667eea;
    }

    .btn-outline:hover {
        background: #667eea;
        color: white;
    }

    /* Action Buttons */
    .desktop-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .mobile-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .btn-mobile {
        padding: 0.75rem;
        border: none;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        text-decoration: none;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .btn-success {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }

    .btn-warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
    }

    .btn-info {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
    }

    /* Modal Styles */
    .glass-modal {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        color: white;
    }

    .modal-header {
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .modal-footer {
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }



    .payment-summary-card {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1.5rem;
    }

    .summary-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-top: 1rem;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
    }

    .summary-item.highlight {
        background: rgba(239, 68, 68, 0.2);
        border: 1px solid rgba(239, 68, 68, 0.3);
    }

    .calculation-display {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem;
    }

    .calc-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .calc-row:last-child {
        border-bottom: none;
    }

    .calc-row.highlight {
        font-weight: 600;
        color: #10b981;
        font-size: 1.1rem;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        color: white;
        padding: 0.75rem;
    }

    .form-control:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        color: white;
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .input-group-text {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #d1d5db;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .payments-table {
            display: none;
        }

        .payments-cards {
            display: flex;
            flex-direction: column;
        }

        .filters-section {
            flex-direction: column;
        }

        .filter-input {
            min-width: 100%;
        }

        .stats-cards {
            grid-template-columns: 1fr;
        }

        .card-details {
            grid-template-columns: 1fr;
        }
    }

    /* Search and Pagination Styles */
    .search-box-partial {
        position: relative;
        width: 250px;
    }

    .search-box-partial input {
        padding-left: 1rem;
        padding-right: 3rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.9);
        border-radius: 20px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .search-box-partial input::placeholder {
        color: rgba(255, 255, 255, 0.6);
        padding-left: 0.5rem;
    }

    .search-box-partial input:focus {
        border-color: var(--primary-color);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        transform: translateY(-1px);
    }

    .search-box-partial .search-icon {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: rgba(255, 255, 255, 0.6);
        pointer-events: none;
        font-size: 0.875rem;
    }

    .pagination-container {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 1.5rem;
        margin-top: 1rem;
    }

    /* Custom Pagination Styling */
    .custom-pagination {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        gap: 0.25rem;
    }

    .custom-page-item {
        display: flex;
    }

    .custom-page-link {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 8px;
        padding: 8px 16px;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
    }

    .custom-page-link:hover {
        background: rgba(59, 130, 246, 0.3);
        border-color: rgba(59, 130, 246, 0.5);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        text-decoration: none;
    }

    .custom-page-item.active .custom-page-link {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border-color: #3b82f6;
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        transform: translateY(-1px);
    }

    .custom-page-item.disabled .custom-page-link {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.4);
        cursor: not-allowed;
        pointer-events: none;
    }

    .custom-page-item.disabled .custom-page-link:hover {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.4);
        transform: none;
        box-shadow: none;
    }

    /* Hidden state for search */
    .payment-row.hidden,
    .payment-card.hidden {
        display: none !important;
    }

    /* Mobile responsive adjustments for search */
    @media (max-width: 768px) {
        .search-box-partial {
            width: 100%;
            margin-bottom: 1rem;
        }

        .header-controls {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-value">{{ invoice_data|length }}</div>
            <div class="stat-label">Students with Partial Payments</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-rupee-sign"></i>
            </div>
            <div class="stat-value" id="total-outstanding">
                ₹0
            </div>
            <div class="stat-label">Total Outstanding</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-value" id="average-progress">
                0%
            </div>
            <div class="stat-label">Average Payment Progress</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="payments-container">
        <!-- Filters -->
        <div class="filters-section">
            <input type="text" id="searchInput" class="filter-input" placeholder="🔍 Search by student name or invoice ID...">
            <select id="sortSelect" class="filter-input">
                <option value="name">Sort by Name</option>
                <option value="amount">Sort by Outstanding Amount</option>
                <option value="date">Sort by Last Payment Date</option>
                <option value="progress">Sort by Payment Progress</option>
            </select>
        </div>

        {% if invoice_data %}
        <!-- Desktop Table View -->
        <table class="payments-table" id="paymentsTable">
            <thead>
                <tr>
                    <th>Student</th>
                    <th>Invoice ID</th>
                    <th>Total Amount</th>
                    <th>Paid Amount</th>
                    <th>Outstanding</th>
                    <th>Progress</th>
                    <th>Last Payment</th>
                    <th>Next Due</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for item in invoice_data %}
                <tr class="payment-row" data-student-name="{{ item.student.name|lower }}" data-invoice-id="{{ item.invoice.invoice_id|lower }}" data-remaining="{{ item.invoice.remaining_due }}" data-percentage="{{ item.payment_percentage }}">
                    <td>
                        <div>
                            <strong>{{ item.student.name }}</strong><br>
                            <small class="text-muted">{{ item.student.course.name|default:"N/A" }}</small>
                        </div>
                    </td>
                    <td>
                        <span class="invoice-id">#{{ item.invoice.invoice_id }}</span>
                    </td>
                    <td>₹{{ item.invoice.total_amount }}</td>
                    <td>₹{{ item.invoice.total_paid }}</td>
                    <td><strong>₹{{ item.invoice.remaining_due }}</strong></td>
                    <td>
                        <div class="progress-bar-container">
                            <div class="progress-bar" style="width: {{ item.payment_percentage }}%"></div>
                        </div>
                        <div class="progress-text">{{ item.payment_percentage }}% paid</div>
                    </td>
                    <td>{{ item.last_payment_date|default:"No payments" }}</td>
                    <td>{{ item.next_commitment_date|default:"Not set" }}</td>
                    <td>
                        <div class="action-buttons desktop-actions">
                            <a href="{% url 'invoice_student' item.invoice.slug %}" class="btn-sm btn-primary" title="View Invoice">
                                <i class="fas fa-file-invoice"></i> Invoice
                            </a>
                            <a href="{% url 'mark_complete_page' item.invoice.slug %}" class="btn-sm btn-success" title="Mark as Completed">
                                <i class="fas fa-check"></i> Complete
                            </a>
                            <a href="{% url 'add_payment_page' item.invoice.slug %}" class="btn-sm btn-warning" title="Add Payment">
                                <i class="fas fa-plus"></i> Payment
                            </a>
                            <button class="btn-sm btn-info" onclick="sendReminder('{{ item.invoice.slug }}', '{{ item.student.email }}')" title="Send Reminder">
                                <i class="fas fa-bell"></i> Remind
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Desktop Pagination -->
        <div class="pagination-container mt-4 d-none d-md-block">
            <nav aria-label="Partial payments pagination">
                <ul class="custom-pagination justify-content-center" id="partialPaymentsPagination">
                    <!-- Pagination will be generated by JavaScript -->
                </ul>
            </nav>
            <div class="pagination-info text-center">
                <small class="text-muted" id="partialPaymentsPageInfo">Showing payments</small>
            </div>
        </div>

        <!-- Mobile Card View -->
        <div class="payments-cards" id="paymentsCards">
            {% for item in invoice_data %}
            <div class="payment-card" data-student-name="{{ item.student.name|lower }}" data-invoice-id="{{ item.invoice.invoice_id|lower }}" data-remaining="{{ item.invoice.remaining_due }}" data-percentage="{{ item.payment_percentage }}">
                <div class="card-header">
                    <div>
                        <div class="student-name">{{ item.student.name }}</div>
                        <div class="invoice-id">#{{ item.invoice.invoice_id }}</div>
                    </div>
                    <span class="status-badge status-partial">Partial</span>
                </div>
                
                <div class="card-details">
                    <div class="detail-item">
                        <span class="detail-label">Total Amount</span>
                        <span class="detail-value">₹{{ item.invoice.total_amount }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Paid Amount</span>
                        <span class="detail-value">₹{{ item.invoice.total_paid }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Outstanding</span>
                        <span class="detail-value"><strong>₹{{ item.invoice.remaining_due }}</strong></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Last Payment</span>
                        <span class="detail-value">{{ item.last_payment_date|default:"No payments" }}</span>
                    </div>
                </div>

                <div class="progress-section">
                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: {{ item.payment_percentage }}%"></div>
                    </div>
                    <div class="progress-text">{{ item.payment_percentage }}% paid</div>
                </div>

                <div class="action-buttons mobile-actions">
                    <a href="{% url 'invoice_student' item.invoice.slug %}" class="btn-mobile btn-primary">
                        <i class="fas fa-file-invoice"></i> View Invoice
                    </a>
                    <a href="{% url 'mark_complete_page' item.invoice.slug %}" class="btn-mobile btn-success">
                        <i class="fas fa-check"></i> Mark Complete
                    </a>
                    <a href="{% url 'add_payment_page' item.invoice.slug %}" class="btn-mobile btn-warning">
                        <i class="fas fa-plus"></i> Add Payment
                    </a>
                    <button class="btn-mobile btn-info" onclick="sendReminder('{{ item.invoice.slug }}', '{{ item.student.email }}')">
                        <i class="fas fa-bell"></i> Send Reminder
                    </button>
                </div>
            </div>
            {% endfor %}

            <!-- Mobile Pagination -->
            <div class="pagination-container mt-4 d-md-none">
                <nav aria-label="Partial payments mobile pagination">
                    <ul class="custom-pagination justify-content-center" id="partialPaymentsMobilePagination">
                        <!-- Pagination will be generated by JavaScript -->
                    </ul>
                </nav>
                <div class="pagination-info text-center">
                    <small class="text-muted" id="partialPaymentsMobilePageInfo">Showing payments</small>
                </div>
            </div>
        </div>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-check-circle"></i>
            <h3>No Partial Payments Found</h3>
            <p>All invoices are either fully paid or unpaid. Great job managing payments!</p>
        </div>
        {% endif %}
    </div>
</div>



{% endblock %}

{% block extra_js %}
<script>
    // Search and Filter Functionality
    class PartialPaymentsManager {
        constructor() {
            this.searchInput = document.getElementById('searchInput');
            this.sortSelect = document.getElementById('sortSelect');
            this.tableRows = document.querySelectorAll('.payment-row');
            this.cardElements = document.querySelectorAll('.payment-card');
            this.init();
        }

        init() {
            this.setupEventListeners();
            this.calculateTotals();
        }

        setupEventListeners() {
            if (this.searchInput) {
                this.searchInput.addEventListener('input', () => this.filterPayments());
            }

            if (this.sortSelect) {
                this.sortSelect.addEventListener('change', () => this.sortPayments());
            }

            // Payment amount input listener
            const paymentAmountInput = document.getElementById('paymentAmount');
            if (paymentAmountInput) {
                paymentAmountInput.addEventListener('input', () => this.updateCalculations());
            }
        }

        calculateTotals() {
            // Calculate total outstanding
            let totalOutstanding = 0;
            let totalPercentage = 0;
            let count = 0;

            // Get data from the invoice cards/rows using data attributes
            const invoiceElements = document.querySelectorAll('[data-remaining][data-percentage]');
            invoiceElements.forEach(element => {
                const remaining = parseInt(element.dataset.remaining) || 0;
                const percentage = parseFloat(element.dataset.percentage) || 0;

                totalOutstanding += remaining;
                totalPercentage += percentage;
                count++;
            });

            // Update display
            const totalOutstandingElement = document.getElementById('total-outstanding');
            const averageProgressElement = document.getElementById('average-progress');

            if (totalOutstandingElement) {
                totalOutstandingElement.textContent = `₹${totalOutstanding.toLocaleString()}`;
            }

            if (averageProgressElement) {
                const averageProgress = count > 0 ? (totalPercentage / count).toFixed(1) : 0;
                averageProgressElement.textContent = `${averageProgress}%`;
            }
        }

        filterPayments() {
            const searchTerm = this.searchInput.value.toLowerCase();

            // Filter table rows
            this.tableRows.forEach(row => {
                const studentName = row.dataset.studentName;
                const invoiceId = row.dataset.invoiceId;
                const isVisible = studentName.includes(searchTerm) || invoiceId.includes(searchTerm);
                row.style.display = isVisible ? '' : 'none';
            });

            // Filter cards
            this.cardElements.forEach(card => {
                const studentName = card.dataset.studentName;
                const invoiceId = card.dataset.invoiceId;
                const isVisible = studentName.includes(searchTerm) || invoiceId.includes(searchTerm);
                card.style.display = isVisible ? '' : 'none';
            });
        }

        sortPayments() {
            const sortBy = this.sortSelect.value;
            console.log(`Sorting by: ${sortBy}`);
        }

        updateCalculations() {
            const paymentAmount = parseFloat(document.getElementById('paymentAmount').value) || 0;
            const amountDue = parseFloat(document.getElementById('amountDue').textContent.replace('₹', '').replace(',', '')) || 0;

            document.getElementById('calcPaymentAmount').textContent = `₹${paymentAmount.toLocaleString()}`;
            document.getElementById('calcAmountDue').textContent = `₹${amountDue.toLocaleString()}`;

            const remaining = Math.max(0, amountDue - paymentAmount);
            document.getElementById('calcRemaining').textContent = `₹${remaining.toLocaleString()}`;
        }
    }

    // Global functions for button actions



    function sendReminder(invoiceSlug, email) {
        if (confirm(`Send payment reminder to ${email}?`)) {
            // Implementation for sending reminder
            fetch(`/{{ role }}/send-payment-reminder/${invoiceSlug}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Payment reminder sent successfully!');
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while sending the reminder.');
            });
        }
    }



    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        new PartialPaymentsManager();
        initializePartialPaymentsPagination();
    });

    // Initialize pagination for partial payments
    function initializePartialPaymentsPagination() {
        // Desktop pagination
        const desktopPagination = new PartialPaymentsPagination(
            '#paymentsTable tbody',
            '.payment-row',
            '#partialPaymentsPagination',
            '#partialPaymentsPageInfo',
            5 // items per page - reduced for testing
        );

        // Mobile pagination
        const mobilePagination = new PartialPaymentsPagination(
            '.payments-cards',
            '.payment-card',
            '#partialPaymentsMobilePagination',
            '#partialPaymentsMobilePageInfo',
            5 // items per page
        );
    }

    // Pagination class for partial payments
    class PartialPaymentsPagination {
        constructor(containerSelector, itemSelector, paginationSelector, pageInfoSelector, itemsPerPage) {
            this.container = document.querySelector(containerSelector);
            this.itemSelector = itemSelector;
            this.pagination = document.querySelector(paginationSelector);
            this.pageInfo = document.querySelector(pageInfoSelector);
            this.itemsPerPage = itemsPerPage;
            this.currentPage = 1;

            if (this.container && this.pagination) {
                this.init();
            }
        }

        init() {
            if (this.container && this.pagination) {
                this.updateDisplay();
            }
        }

        reset() {
            this.currentPage = 1;
            this.updateDisplay();
        }

        updateDisplay() {
            const items = Array.from(this.container.querySelectorAll(`${this.itemSelector}:not(.hidden)`));
            const totalItems = items.length;
            const totalPages = Math.ceil(totalItems / this.itemsPerPage);
            const startIndex = (this.currentPage - 1) * this.itemsPerPage;
            const endIndex = startIndex + this.itemsPerPage;

            // Hide all items first
            this.container.querySelectorAll(this.itemSelector).forEach(item => {
                item.style.display = 'none';
            });

            // Show only current page items
            items.slice(startIndex, endIndex).forEach(item => {
                item.style.display = '';
            });

            this.updatePagination(totalPages);
            this.updatePageInfo(startIndex + 1, Math.min(endIndex, totalItems), totalItems);
        }

        updatePagination(totalPages) {
            if (!this.pagination) return;

            this.pagination.innerHTML = '';

            if (totalPages <= 1) return;

            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `custom-page-item ${this.currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = '<a class="custom-page-link" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>';
            if (this.currentPage > 1) {
                prevLi.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.currentPage--;
                    this.updateDisplay();
                });
            }
            this.pagination.appendChild(prevLi);

            // Page numbers (show max 5 pages)
            const startPage = Math.max(1, this.currentPage - 2);
            const endPage = Math.min(totalPages, startPage + 4);

            for (let i = startPage; i <= endPage; i++) {
                const li = document.createElement('li');
                li.className = `custom-page-item ${i === this.currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="custom-page-link" href="#">${i}</a>`;
                li.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.currentPage = i;
                    this.updateDisplay();
                });
                this.pagination.appendChild(li);
            }

            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `custom-page-item ${this.currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = '<a class="custom-page-link" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>';
            if (this.currentPage < totalPages) {
                nextLi.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.currentPage++;
                    this.updateDisplay();
                });
            }
            this.pagination.appendChild(nextLi);
        }

        updatePageInfo(start, end, total) {
            if (this.pageInfo) {
                this.pageInfo.textContent = `Showing ${start}-${end} of ${total} payments`;
            }
        }
    }
</script>

<!-- Bootstrap JS (if not already included) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}
