{% extends "base.html" %}

{% block title %}Update Student - Librainian{% endblock %}

{% block extra_css %}

<style>
        /* Template-specific styles - CSS variables inherited from base.html */

        /* Glass Design Components */
        .glass-container {
            min-height: 100vh;
            padding: 2rem 1rem;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .glass-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            text-align: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .glass-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            transform: rotate(-45deg);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) rotate(-45deg); }
            50% { transform: translateX(100%) rotate(-45deg); }
        }

        .glass-header h3 {
            margin: 0;
            font-weight: 700;
            font-size: 1.75rem;
            position: relative;
            z-index: 1;
        }

        .glass-body {
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* Form Styling */
        .form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 0.875rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.95);
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
        }

        .form-control:disabled, .form-control[readonly] {
            background: rgba(255, 255, 255, 0.5);
            color: var(--text-muted);
        }

        /* File Upload Styling */
        .file-upload-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .file-upload-input {
            position: absolute;
            left: -9999px;
        }

        .file-upload-label {
            background: rgba(255, 255, 255, 0.9);
            border: 2px dashed rgba(99, 102, 241, 0.3);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
        }

        .file-upload-label:hover {
            border-color: var(--primary);
            background: rgba(255, 255, 255, 0.95);
        }

        .file-upload-icon {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        /* Action Buttons */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
            background: linear-gradient(135deg, var(--secondary-dark), var(--secondary));
            color: white;
            text-decoration: none;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .glass-container {
                padding: 1rem 0.5rem;
            }

            .glass-card {
                border-radius: 20px;
            }

            .glass-body {
                padding: 1.5rem;
            }

            .glass-header {
                padding: 1.5rem;
            }

            .glass-header h3 {
                font-size: 1.5rem;
            }

            .file-upload-label {
                padding: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .glass-body {
                padding: 1rem;
            }

            .btn-primary, .btn-secondary {
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
            }

            .file-upload-label {
                padding: 1rem;
            }
        }

        /* Alert Messages Styling */
        .alert-glass {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 1rem 1.25rem;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
            animation: slideDown 0.5s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .alert-glass.alert-success {
            background: rgba(16, 185, 129, 0.15);
            border-color: rgba(16, 185, 129, 0.3);
            color: #059669;
        }

        .alert-glass.alert-error,
        .alert-glass.alert-danger {
            background: rgba(239, 68, 68, 0.15);
            border-color: rgba(239, 68, 68, 0.3);
            color: #dc2626;
        }

        .alert-glass.alert-warning {
            background: rgba(245, 158, 11, 0.15);
            border-color: rgba(245, 158, 11, 0.3);
            color: #d97706;
        }

        .alert-glass.alert-info {
            background: rgba(59, 130, 246, 0.15);
            border-color: rgba(59, 130, 246, 0.3);
            color: #2563eb;
        }

        .btn-close-glass {
            background: none;
            border: none;
            color: inherit;
            opacity: 0.7;
            padding: 0.25rem;
            border-radius: 50%;
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: absolute;
            top: 0.75rem;
            right: 0.75rem;
        }

        .btn-close-glass:hover {
            opacity: 1;
            background: rgba(0, 0, 0, 0.1);
            transform: scale(1.1);
        }

        /* Disable zoom on mobile */
        @media (max-width: 768px) {
            body {
                touch-action: pan-x pan-y;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="student-update-content fade-in">
    <div class="glass-container">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="glass-card">
                        <div class="glass-header">
                            <h3><i class="fas fa-user-edit me-2"></i>Update Student Information</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            {% if messages %}
                <div class="row mb-4">
                    <div class="col-12">
                        {% for message in messages %}
                            <div class="alert-glass alert-{{ message.tags|default:'info' }} alert-dismissible fade show mb-3" role="alert">
                                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close-glass" data-bs-dismiss="alert" aria-label="Close">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Update Form -->
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="glass-card">
                        <div class="glass-body">
                            <form action="/students/doupdate/{{ std.slug }}/" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                                {% csrf_token %}

                                <div class="row">
                                    <!-- Left Column -->
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="unique_id" class="form-label">
                                                <i class="fas fa-id-card"></i>
                                                Student ID
                                            </label>
                                            <input type="text" class="form-control" id="unique_id" name="unique_id"
                                                   value="{{ std.unique_id }}" readonly>
                                        </div>

                                        <div class="mb-3">
                                            <label for="courseName" class="form-label">
                                                <i class="fas fa-book"></i>
                                                Course Name
                                            </label>
                                            <select class="form-select" id="courseName" name="course" required>
                                                {% if std.course %}
                                                <option value="{{ std.course.id }}" selected>{{ std.course.name }}</option>
                                                {% else %}
                                                <option value="" selected>Select a course</option>
                                                {% endif %}
                                                {% for course in courses %}
                                                <option value="{{ course.id }}" {% if std.course.id == course.id %}selected{% endif %}>
                                                    {{ course.name }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                            <div class="invalid-feedback">
                                                Please select a course.
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="name" class="form-label">
                                                <i class="fas fa-user"></i>
                                                Student Name
                                            </label>
                                            <input type="text" class="form-control" id="name" name="name"
                                                   value="{{ std.name }}" required>
                                            <div class="invalid-feedback">
                                                Please provide a valid name.
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="fathername" class="form-label">
                                                <i class="fas fa-male"></i>
                                                Father's Name
                                            </label>
                                            <input type="text" class="form-control" id="fathername" name="fathername"
                                                   value="{{ std.f_name }}">
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="age" class="form-label">
                                                    <i class="fas fa-birthday-cake"></i>
                                                    Age
                                                </label>
                                                <input type="number" class="form-control" id="age" name="age"
                                                       value="{{ std.age }}" min="1" max="100">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="gender" class="form-label">
                                                    <i class="fas fa-venus-mars"></i>
                                                    Gender
                                                </label>
                                                <select class="form-select" id="gender" name="gender" required>
                                                    <option value="male" {% if std.gender == "male" %}selected{% endif %}>Male</option>
                                                    <option value="female" {% if std.gender == "female" %}selected{% endif %}>Female</option>
                                                    <option value="other" {% if std.gender == "other" %}selected{% endif %}>Other</option>
                                                </select>
                                                <div class="invalid-feedback">
                                                    Please select a gender.
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="email" class="form-label">
                                                <i class="fas fa-envelope"></i>
                                                Email Address
                                            </label>
                                            <input type="email" class="form-control" id="email" name="email"
                                                   value="{{ std.email }}" required>
                                            <div class="invalid-feedback">
                                                Please provide a valid email address.
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="phone" class="form-label">
                                                <i class="fas fa-phone"></i>
                                                Mobile Number
                                            </label>
                                            <input type="tel" class="form-control" id="phone" name="phone"
                                                   value="{{ std.mobile }}" required>
                                            <div class="invalid-feedback">
                                                Please provide a valid mobile number.
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Right Column -->
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="locality" class="form-label">
                                                <i class="fas fa-map-marker-alt"></i>
                                                Address/Locality
                                            </label>
                                            <input type="text" class="form-control" id="locality" name="locality"
                                                   value="{{ std.locality }}" required>
                                            <div class="invalid-feedback">
                                                Please provide an address.
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="city" class="form-label">
                                                    <i class="fas fa-city"></i>
                                                    City
                                                </label>
                                                <input type="text" class="form-control" id="city" name="city"
                                                       value="{{ std.city }}" required>
                                                <div class="invalid-feedback">
                                                    Please provide a city.
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="stateName" class="form-label">
                                                    <i class="fas fa-flag"></i>
                                                    State
                                                </label>
                                                <select class="form-select" id="stateName" name="state" required>
                                                    {% if std.state %}
                                                    <option value="{{ std.state.id }}" selected>{{ std.state.name }}</option>
                                                    {% else %}
                                                    <option value="" selected>Select a state</option>
                                                    {% endif %}
                                                    {% for state in states %}
                                                    <option value="{{ state.id }}" {% if std.state and std.state.id == state.id %}selected{% endif %}>
                                                        {{ state.name }}
                                                    </option>
                                                    {% endfor %}
                                                </select>
                                                <div class="invalid-feedback">
                                                    Please select a state.
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="regfees" class="form-label">
                                                <i class="fas fa-rupee-sign"></i>
                                                Registration Fees
                                            </label>
                                            <input type="number" class="form-control" id="regfees" name="regfees"
                                                   value="{{ std.registration_fee }}" required min="0">
                                            <div class="invalid-feedback">
                                                Please provide a valid registration fee.
                                            </div>
                                        </div>

                                        <div class="mb-4">
                                            <label for="image" class="form-label">
                                                <i class="fas fa-camera"></i>
                                                Student Image
                                            </label>
                                            <div class="file-upload-wrapper">
                                                <input type="file" class="file-upload-input" id="image" name="image" accept="image/*">
                                                <label for="image" class="file-upload-label">
                                                    <div class="file-upload-icon">
                                                        <i class="fas fa-cloud-upload-alt"></i>
                                                    </div>
                                                    <div>
                                                        <strong>Choose a file</strong> or drag it here
                                                    </div>
                                                    <small class="text-muted">PNG, JPG, GIF up to 10MB</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                    <a href="/students/" class="btn-secondary me-md-2">
                                        <i class="fas fa-arrow-left"></i>
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn-primary">
                                        <i class="fas fa-save"></i>
                                        Update Student
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- JavaScript dependencies -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Modern functionality
        $(document).ready(function() {
            // Auto-hide glass alerts after 8 seconds
            $('.alert-glass').each(function() {
                const alert = this;
                setTimeout(function() {
                    $(alert).fadeOut(1000, function() {
                        $(this).remove();
                    });
                }, 8000);
            });

            // Handle glass alert close button
            $('.btn-close-glass').on('click', function() {
                const alert = $(this).closest('.alert-glass');
                alert.fadeOut(300, function() {
                    $(this).remove();
                });
            });

            // Form validation
            const form = document.querySelector('.needs-validation');
            if (form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });
            }

            // Loading state for submit button
            $('form').on('submit', function() {
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Updating...');
                submitBtn.prop('disabled', true);

                // Re-enable after a delay (in case of validation errors)
                setTimeout(() => {
                    submitBtn.html(originalText);
                    submitBtn.prop('disabled', false);
                }, 3000);
            });

            // File upload preview
            $('#image').on('change', function() {
                const file = this.files[0];
                const label = $(this).siblings('.file-upload-label');

                if (file) {
                    const fileName = file.name;
                    const fileSize = (file.size / 1024 / 1024).toFixed(2);
                    label.html(`
                        <div class="file-upload-icon">
                            <i class="fas fa-check-circle text-success"></i>
                        </div>
                        <div>
                            <strong>${fileName}</strong><br>
                            <small class="text-muted">${fileSize} MB</small>
                        </div>
                    `);
                } else {
                    label.html(`
                        <div class="file-upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div>
                            <strong>Choose a file</strong> or drag it here
                        </div>
                        <small class="text-muted">PNG, JPG, GIF up to 10MB</small>
                    `);
                }
            });

            // Phone number validation
            $('#phone').on('input', function() {
                const phone = $(this).val();
                const phoneRegex = /^[6-9]\d{9}$/;

                if (phone && !phoneRegex.test(phone)) {
                    $(this).addClass('is-invalid');
                    $(this).siblings('.invalid-feedback').text('Please enter a valid 10-digit mobile number');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // Email validation
            $('#email').on('input', function() {
                const email = $(this).val();
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                if (email && !emailRegex.test(email)) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // Smooth animations for cards
            $('.glass-card').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
            });
        });

        // Utility function to show dynamic notifications
        function showNotification(message, type = 'info', duration = 5000) {
            const alertTypes = {
                'success': { icon: 'check-circle', class: 'alert-success' },
                'error': { icon: 'exclamation-triangle', class: 'alert-danger' },
                'warning': { icon: 'exclamation-circle', class: 'alert-warning' },
                'info': { icon: 'info-circle', class: 'alert-info' }
            };

            const alertConfig = alertTypes[type] || alertTypes['info'];

            const alertHtml = `
                <div class="alert-glass ${alertConfig.class} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;">
                    <i class="fas fa-${alertConfig.icon} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close-glass" onclick="$(this).closest('.alert-glass').fadeOut(300, function() { $(this).remove(); })">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            $('body').append(alertHtml);

            // Auto-hide after specified duration
            setTimeout(function() {
                $('.alert-glass').last().fadeOut(1000, function() {
                    $(this).remove();
                });
            }, duration);
        }
    </script>
</div>
{% endblock %}