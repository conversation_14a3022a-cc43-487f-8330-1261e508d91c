<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="google" content="notranslate">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to Our Library</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">


      <!-- Disable Right click -->

        
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
 <style>
    /* Custom styles can be added here */
    body {
      -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;

      font-family: Arial, sans-serif;
      background-color: #f8f9fa;
      padding: 20px;
      background-image: url(/static/img/welcome.jpg);
      background-position: center;
      background-size: cover;
 
    }
    .box {
      /* max-width: 600px; */
      width: 60%;
      height: auto;
      margin: 0 auto;
      background-color: #ffffff;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0px 0px 10px 7px rgba(0,0,0,0.1);
    
    }
    .box-heading {
      background-color: #007bff;
      color: #ffffff;
      padding: 10px;
      text-align: center;
      border-radius: 8px;
    }
    .footer {
      background-color: #c3c3c9;
      color: #ffffff;
      padding: 10px;
      text-align: center;
      border-radius: 8px;
      margin-top: 40px;
    }
    .footer p{
        font-size: 12px;
    }
    h1 {
      text-align: center;
      margin-top: 20px;
    }
    p{
        font-size: 16px;
        margin-top: 10px;
    }
    @media screen and (max-width: 768px) {
        body{
            background-position: center;
            background-size: cover;
        }
      .box {
        width: 100% !important;
        height: auto;
      }
      .box-heading{
        width: 100%;
      }
      .box-heading h1{
        font-size: 22px;
      }
      p{
        font-size: 12px;
      }
      .footer {
        margin-top: 20px !important;
      }
    }
  

  </style>
</head>
<body>

  <div class="box">
    <div class="box-heading">
      <h1>Welcome to Our Library</h1>
    </div>
    <p>Dear [Customer Name],</p>
    <p>We are thrilled to have you join us. <br> Thank you for signing up!</p>
    <p>Feel free to explore our platform and let us know if you have any questions or feedback.</p>
    <p>If you have any questions, feel free to <u><i>email</i></u> our <u><i>customer success team</i></u>. (We're lightning quick at replying.) We also offer live chat during business hours.</p>
    <p>Best regards,<br> The [Librainian] Team</p>

    <div class="footer">
        <p>&copy; 2024 [Librainian]. All rights reserved.</p>
        <p>[Librainian]</p>
        <p>[F2/9 Jai Durga Society Netaji Nagar Hill no.3, 90ft road Sakinaka, Kurla West Mumbai 400072]</p>
      </div>
  </div>

  <!-- Bootstrap JS and dependencies (optional) -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
   
   
</body>
</html>
