/* Dark Mode Global Styles */

/* CSS Variables for Dark Mode */
:root {
    /* Light Mode Colors (Default) */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --text-primary: #111827;
    --text-secondary: #4b5563;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --shadow-color: rgba(0, 0, 0, 0.1);
    
    /* Glassmorphism Variables */
    --glass-bg: rgba(255, 255, 255, 0.15);
    --glass-border: rgba(255, 255, 255, 0.3);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --glass-backdrop: blur(20px);
    
    /* Gradients */
    --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* Dark Mode Variables */
body.dark-mode {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --text-primary: #ffffff;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #4b5563;
    --shadow-color: rgba(0, 0, 0, 0.3);
    
    /* Dark Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    
    /* Dark Gradients */
    --gradient-hero: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
}

/* Global Dark Mode Styles */
body.dark-mode {
    background: var(--gradient-hero);
    color: var(--text-primary);
    transition: background 0.3s ease, color 0.3s ease;
}

/* Card Components */
body.dark-mode .modern-card,
body.dark-mode .glass-card {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    color: var(--text-primary);
    box-shadow: var(--glass-shadow);
}

body.dark-mode .modern-card-header,
body.dark-mode .glass-header {
    background: rgba(255, 255, 255, 0.05);
    border-bottom-color: var(--glass-border);
    color: var(--text-primary);
}

body.dark-mode .modern-card-body,
body.dark-mode .glass-body {
    color: var(--text-primary);
}

/* Table Components */
body.dark-mode .table-glass {
    background: var(--glass-bg);
    border-color: var(--glass-border);
}

body.dark-mode .table-glass th {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border-color: var(--glass-border);
}

body.dark-mode .table-glass td {
    color: var(--text-primary);
    border-color: var(--glass-border);
}

body.dark-mode .table-glass tbody tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Form Components */
body.dark-mode .form-control,
body.dark-mode .form-control-glass,
body.dark-mode .form-select {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--glass-border);
    color: var(--text-primary);
}

body.dark-mode .form-control::placeholder,
body.dark-mode .form-control-glass::placeholder {
    color: var(--text-muted);
}

body.dark-mode .form-control:focus,
body.dark-mode .form-control-glass:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
}

/* Badge Components */
body.dark-mode .badge,
body.dark-mode .badge-glass {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--glass-border);
    color: var(--text-primary);
}

/* Alert Components */
body.dark-mode .alert,
body.dark-mode .alert-glass {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    color: var(--text-primary);
}

/* Button Components */
body.dark-mode .btn-primary-modern,
body.dark-mode .btn-secondary-modern {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    color: white;
}

body.dark-mode .btn-primary-modern:hover,
body.dark-mode .btn-secondary-modern:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.4);
}

/* Mobile Card Components */
body.dark-mode .mobile-student-card,
body.dark-mode .mobile-pending-card,
body.dark-mode .mobile-shift-card,
body.dark-mode .mobile-invoice-card {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    color: var(--text-primary);
    box-shadow: var(--glass-shadow);
}

body.dark-mode .mobile-field label {
    color: var(--text-secondary);
}

body.dark-mode .mobile-value {
    color: var(--text-primary);
}

body.dark-mode .contact-item-mobile {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
}

/* Text Elements */
body.dark-mode .student-name-mobile,
body.dark-mode .shift-name-mobile,
body.dark-mode h1, body.dark-mode h2, body.dark-mode h3,
body.dark-mode h4, body.dark-mode h5, body.dark-mode h6 {
    color: var(--text-primary);
}

body.dark-mode .student-id-mobile,
body.dark-mode .shift-time-mobile,
body.dark-mode .text-muted {
    color: var(--text-secondary);
}

/* Empty State */
body.dark-mode .empty-state-glass,
body.dark-mode .empty-state-mobile {
    color: var(--text-secondary);
}

body.dark-mode .empty-state-glass h5,
body.dark-mode .empty-state-mobile h5 {
    color: var(--text-primary);
}

/* Navigation */
body.dark-mode .navbar,
body.dark-mode .sidebar {
    background: var(--glass-bg);
    border-color: var(--glass-border);
}

body.dark-mode .nav-link {
    color: var(--text-secondary);
}

body.dark-mode .nav-link:hover,
body.dark-mode .nav-link.active {
    color: var(--text-primary);
}

/* Dropdown */
body.dark-mode .dropdown-menu {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
}

body.dark-mode .dropdown-item {
    color: var(--text-primary);
}

body.dark-mode .dropdown-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

/* Modal */
body.dark-mode .modal-content {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
}

/* Loading Components */
body.dark-mode .loading-screen {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

body.dark-mode .loading-spinner {
    color: rgba(255, 255, 255, 0.95);
}

body.dark-mode .spinner {
    border: 4px solid rgba(255, 255, 255, 0.2);
    border-top: 4px solid rgba(255, 255, 255, 0.9);
}

body.dark-mode .loading-overlay {
    background: rgba(17, 24, 39, 0.8);
    backdrop-filter: blur(12px);
}

body.dark-mode .loading-content {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    color: var(--text-primary);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
}

body.dark-mode .btn.loading::after {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: rgba(255, 255, 255, 0.9);
}

body.dark-mode .modal-header {
    border-bottom-color: var(--glass-border);
}

body.dark-mode .modal-footer {
    border-top-color: var(--glass-border);
}

/* Pagination */
body.dark-mode .page-link {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    color: var(--text-primary);
}

body.dark-mode .page-link:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--glass-border);
    color: var(--text-primary);
}

/* Dropdown Components */
body.dark-mode .dropdown-menu {
    background: rgba(17, 24, 39, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

body.dark-mode .dropdown-item {
    color: rgba(255, 255, 255, 0.9);
}

body.dark-mode .dropdown-item:hover,
body.dark-mode .dropdown-item:focus {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

body.dark-mode .dropdown-divider {
    border-color: rgba(255, 255, 255, 0.2);
}

body.dark-mode .dropdown-header {
    color: rgba(255, 255, 255, 0.8);
}

/* Index Page Specific Styles */
body.dark-mode .feature-card,
body.dark-mode .pricing-card,
body.dark-mode .testimonial-card,
body.dark-mode .security-card,
body.dark-mode .safety-card {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: white;
}

body.dark-mode .safety-section,
body.dark-mode section.safety-section {
    background: rgba(0, 0, 0, 0.3) !important;
}

/* Comprehensive Text Visibility */
body.dark-mode h1, body.dark-mode h2, body.dark-mode h3,
body.dark-mode h4, body.dark-mode h5, body.dark-mode h6,
body.dark-mode .section-title, body.dark-mode .card-title {
    color: white !important;
}

body.dark-mode p, body.dark-mode .lead, body.dark-mode .section-subtitle,
body.dark-mode .card-text, body.dark-mode li {
    color: rgba(255, 255, 255, 0.8) !important;
}

body.dark-mode .text-dark, body.dark-mode .text-black {
    color: white !important;
}

body.dark-mode .text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

body.dark-mode .text-secondary {
    color: rgba(255, 255, 255, 0.8) !important;
}

body.dark-mode small, body.dark-mode .small {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Payment System Dark Mode Styles */
body.dark-mode .payment-summary-card {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

body.dark-mode .payment-summary-card h6 {
    color: white !important;
}

body.dark-mode .summary-row {
    color: white !important;
}

body.dark-mode .payment-container {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

body.dark-mode .payment-form {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

body.dark-mode .calculation-display {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

body.dark-mode .calc-row {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

body.dark-mode .progress-bar-container {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

body.dark-mode .progress-text {
    color: rgba(255, 255, 255, 0.8) !important;
}

body.dark-mode .payment-card {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

body.dark-mode .history-item {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

body.dark-mode .info-item {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

body.dark-mode .info-label {
    color: rgba(255, 255, 255, 0.8) !important;
}

body.dark-mode .info-value {
    color: white !important;
}

body.dark-mode strong, body.dark-mode b {
    color: white !important;
}

/* Footer Dark Mode */
body.dark-mode .modern-footer,
body.dark-mode footer.modern-footer {
    background: linear-gradient(135deg, #111827 0%, #1f2937 100%) !important;
}

body.dark-mode .modern-footer h5,
body.dark-mode .modern-footer h6 {
    color: white !important;
}

body.dark-mode .modern-footer p,
body.dark-mode .modern-footer .text-white-50 {
    color: rgba(255, 255, 255, 0.8) !important;
}

body.dark-mode .modern-footer a {
    color: rgba(255, 255, 255, 0.8) !important;
}

body.dark-mode .modern-footer a:hover {
    color: white !important;
}

body.dark-mode .modern-footer .social-links a {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
}

body.dark-mode .modern-footer .social-links a:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

/* Transitions */
body.dark-mode * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
