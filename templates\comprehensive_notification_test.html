<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Notification System Test - LMS</title>
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .event-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .event-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border-left: 5px solid #007bff;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .event-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 123, 255, 0.2);
        }
        
        .event-card h3 {
            color: #007bff;
            margin-top: 0;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .event-card p {
            color: #666;
            margin: 10px 0;
            line-height: 1.5;
        }
        
        .btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            width: 100%;
            margin: 10px 0;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }
        
        .btn-success { background: linear-gradient(45deg, #28a745, #1e7e34); }
        .btn-warning { background: linear-gradient(45deg, #ffc107, #e0a800); }
        .btn-danger { background: linear-gradient(45deg, #dc3545, #c82333); }
        .btn-info { background: linear-gradient(45deg, #17a2b8, #138496); }
        
        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #bbdefb;
        }
        
        .stat-card h4 {
            margin: 0 0 10px 0;
            color: #1565c0;
        }
        
        .stat-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #0d47a1;
        }
        
        @media (max-width: 768px) {
            .event-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Comprehensive Notification System Test</h1>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h4>📊 Total Events</h4>
                <div class="value" id="total-events">15</div>
            </div>
            <div class="stat-card">
                <h4>✅ Notifications Sent</h4>
                <div class="value" id="notifications-sent">0</div>
            </div>
            <div class="stat-card">
                <h4>📱 Active Devices</h4>
                <div class="value" id="active-devices">-</div>
            </div>
            <div class="stat-card">
                <h4>🔔 Permission Status</h4>
                <div class="value" id="permission-status">-</div>
            </div>
        </div>
        
        <div class="event-grid">
            <!-- Visitor Events -->
            <div class="event-card">
                <h3>📞 Visitor Callback Due</h3>
                <p>Test notification for visitor callbacks scheduled for today</p>
                <button class="btn btn-info" onclick="testVisitorCallback()">Test Visitor Callback</button>
            </div>
            
            <div class="event-card">
                <h3>👥 Visitor Added</h3>
                <p>Notification when a new visitor is added by sublibrarian</p>
                <button class="btn btn-info" onclick="testVisitorAdded()">Test Visitor Added</button>
            </div>
            
            <!-- Admission Events -->
            <div class="event-card">
                <h3>✅ Admission Processed</h3>
                <p>Notification when admission is processed by sublibrarian</p>
                <button class="btn btn-success" onclick="testAdmissionProcessed()">Test Admission Processed</button>
            </div>
            
            <!-- Member Expiry Events -->
            <div class="event-card">
                <h3>⏰ Member Expiry Warnings</h3>
                <p>Test all member expiry notifications (10, 5, 1 days, expired)</p>
                <button class="btn btn-warning" onclick="testMemberExpiry10()">10 Days Warning</button>
                <button class="btn btn-warning" onclick="testMemberExpiry5()">5 Days Warning</button>
                <button class="btn btn-danger" onclick="testMemberExpiry1()">1 Day Warning</button>
                <button class="btn btn-danger" onclick="testMemberExpired()">Member Expired</button>
            </div>
            
            <!-- Financial Events -->
            <div class="event-card">
                <h3>💰 Invoice Created</h3>
                <p>Notification when invoice is created by sublibrarian</p>
                <button class="btn btn-success" onclick="testInvoiceCreated()">Test Invoice Created</button>
            </div>
            
            <div class="event-card">
                <h3>🎉 Sales Milestones</h3>
                <p>Test sales milestone notifications (50k, 100k, 150k, 200k)</p>
                <button class="btn btn-success" onclick="testSalesMilestone(50000)">₹50k Milestone</button>
                <button class="btn btn-success" onclick="testSalesMilestone(100000)">₹100k Milestone</button>
                <button class="btn btn-success" onclick="testSalesMilestone(150000)">₹150k Milestone</button>
                <button class="btn btn-success" onclick="testSalesMilestone(200000)">₹200k Milestone</button>
            </div>
            
            <div class="event-card">
                <h3>📊 Monthly Sales Summary</h3>
                <p>Monthly sales summary notification (last day of month)</p>
                <button class="btn btn-info" onclick="testMonthlySummary()">Test Monthly Summary</button>
            </div>
            
            <!-- Reminder Events -->
            <div class="event-card">
                <h3>📝 Daily Galla Reminder</h3>
                <p>Daily reminder for galla submission to sublibrarians</p>
                <button class="btn btn-warning" onclick="testGallaReminder()">Test Galla Reminder</button>
            </div>
            
            <!-- Admin Events -->
            <div class="event-card">
                <h3>📢 Custom Admin Notification</h3>
                <p>Custom notification from Django admin panel</p>
                <button class="btn btn-info" onclick="testCustomNotification()">Test Custom Notification</button>
            </div>
            
            <!-- QR Registration -->
            <div class="event-card">
                <h3>📝 QR Registration</h3>
                <p>Original QR registration notification with favicon icon</p>
                <button class="btn btn-success" onclick="testQRRegistration()">Test QR Registration</button>
            </div>
            
            <!-- Payment Events -->
            <div class="event-card">
                <h3>💳 Payment Received</h3>
                <p>Notification when payment is received</p>
                <button class="btn btn-success" onclick="testPaymentReceived()">Test Payment Received</button>
            </div>
            
            <!-- Facility Events -->
            <div class="event-card">
                <h3>📚 Facility Booking</h3>
                <p>Notification for facility bookings</p>
                <button class="btn btn-info" onclick="testFacilityBooking()">Test Facility Booking</button>
            </div>
            
            <!-- Emergency Events -->
            <div class="event-card">
                <h3>🚨 Emergency Alert</h3>
                <p>High priority emergency notification</p>
                <button class="btn btn-danger" onclick="testEmergencyAlert()">Test Emergency Alert</button>
            </div>
            
            <!-- System Events -->
            <div class="event-card">
                <h3>🔧 System Maintenance</h3>
                <p>System maintenance notification</p>
                <button class="btn btn-warning" onclick="testSystemMaintenance()">Test System Maintenance</button>
            </div>
            
            <!-- Bulk Test -->
            <div class="event-card">
                <h3>🚀 Bulk Test</h3>
                <p>Test multiple notifications at once</p>
                <button class="btn btn-danger" onclick="testAllNotifications()">Test All Notifications</button>
                <button class="btn" onclick="clearLog()">Clear Log</button>
            </div>
        </div>
        
        <div class="log-area" id="log-area">
            <div class="info">Comprehensive notification system test loaded...</div>
        </div>
    </div>

    <script>
        let logArea = document.getElementById('log-area');
        let notificationsSent = 0;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            logArea.innerHTML = '<div class="info">Log cleared...</div>';
            notificationsSent = 0;
            updateStats();
        }
        
        function updateStats() {
            document.getElementById('notifications-sent').textContent = notificationsSent;
            document.getElementById('permission-status').textContent = Notification.permission;
            
            // Check device count (simulated)
            document.getElementById('active-devices').textContent = '4';
        }
        
        function sendNotification(title, body, tag = 'test') {
            if (Notification.permission !== 'granted') {
                log('❌ Permission not granted. Please enable notifications first.', 'error');
                return false;
            }
            
            try {
                const notification = new Notification(title, {
                    body: body,
                    icon: '/favicon.ico',
                    tag: tag,
                    requireInteraction: false,
                    vibrate: [200, 100, 200]
                });
                
                notification.onclick = () => {
                    log(`👆 Notification clicked: ${title}`, 'success');
                    notification.close();
                };
                
                notification.onshow = () => {
                    log(`✅ Notification displayed: ${title}`, 'success');
                };
                
                notification.onerror = (error) => {
                    log(`❌ Notification error: ${error}`, 'error');
                };
                
                // Auto-close after 8 seconds
                setTimeout(() => {
                    if (notification) {
                        notification.close();
                    }
                }, 8000);
                
                notificationsSent++;
                updateStats();
                return true;
                
            } catch (error) {
                log(`❌ Failed to create notification: ${error.message}`, 'error');
                return false;
            }
        }
        
        // Test functions for each notification type
        function testVisitorCallback() {
            log('📞 Testing visitor callback notification...', 'info');
            sendNotification(
                '📞 Visitor Callback Due Today',
                'Callback scheduled for: John Doe\nMobile: 9876543210\nDate: 2024-01-10',
                'visitor-callback'
            );
        }
        
        function testVisitorAdded() {
            log('👥 Testing visitor added notification...', 'info');
            sendNotification(
                '👥 New Visitor Added',
                'Visitor: Jane Smith\nMobile: 9876543211\nPurpose: Library Tour\nAdded by: sublibrarian1',
                'visitor-added'
            );
        }
        
        function testAdmissionProcessed() {
            log('✅ Testing admission processed notification...', 'info');
            sendNotification(
                '✅ Admission Processed',
                'Student: Alex Johnson\nProcessed by: sublibrarian1\nDate: 2024-01-10',
                'admission-processed'
            );
        }
        
        function testMemberExpiry10() {
            log('⏰ Testing 10-day expiry warning...', 'info');
            sendNotification(
                '⏰ Member Expiry Warning - 10 Days',
                'Member: Sarah Wilson\nExpires: 2024-01-20\nMobile: 9876543212',
                'member-expiry-10'
            );
        }
        
        function testMemberExpiry5() {
            log('⚠️ Testing 5-day expiry warning...', 'info');
            sendNotification(
                '⚠️ Member Expiry Warning - 5 Days',
                'Member: Mike Brown\nExpires: 2024-01-15\nMobile: 9876543213',
                'member-expiry-5'
            );
        }
        
        function testMemberExpiry1() {
            log('🚨 Testing 1-day expiry warning...', 'info');
            sendNotification(
                '🚨 Member Expiry Warning - 1 Day',
                'Member: Lisa Davis\nExpires: 2024-01-11\nMobile: 9876543214',
                'member-expiry-1'
            );
        }
        
        function testMemberExpired() {
            log('❌ Testing member expired notification...', 'info');
            sendNotification(
                '❌ Member Expired Today',
                'Member: Tom Anderson\nExpired: 2024-01-10\nMobile: 9876543215',
                'member-expired'
            );
        }
        
        function testInvoiceCreated() {
            log('💰 Testing invoice created notification...', 'info');
            sendNotification(
                '💰 New Invoice Created',
                'Invoice: INV-2024-001\nStudent: Emma Taylor\nAmount: ₹5,000\nCreated by: sublibrarian1',
                'invoice-created'
            );
        }
        
        function testSalesMilestone(amount) {
            log(`🎉 Testing sales milestone: ₹${amount.toLocaleString()}...`, 'info');
            sendNotification(
                '🎉 Sales Milestone Reached!',
                `Milestone: ₹${amount.toLocaleString()}\nCurrent Sales: ₹${(amount + 5000).toLocaleString()}\nMonth: January 2024`,
                'sales-milestone'
            );
        }
        
        function testMonthlySummary() {
            log('📊 Testing monthly sales summary...', 'info');
            sendNotification(
                '📊 Monthly Sales Summary',
                'Month: January 2024\nTotal Sales: ₹2,50,000\nInvoices: 50\nAverage: ₹5,000',
                'monthly-summary'
            );
        }
        
        function testGallaReminder() {
            log('📝 Testing galla reminder...', 'info');
            sendNotification(
                '📝 Daily Galla Submission Reminder',
                'Hello SubLibrarian,\nPlease submit your daily galla for Wednesday, 2024-01-10',
                'galla-reminder'
            );
        }
        
        function testCustomNotification() {
            log('📢 Testing custom admin notification...', 'info');
            sendNotification(
                '📢 Important Announcement',
                'Library will be closed tomorrow for maintenance.\nPlease inform all students.\n\nSent by: Admin\nDate: 2024-01-10 15:30',
                'custom-admin'
            );
        }
        
        function testQRRegistration() {
            log('📝 Testing QR registration notification...', 'info');
            sendNotification(
                '📝 New QR Registration',
                'New student registration: David Miller\nCourse: Computer Science\nEmail: <EMAIL>',
                'qr-registration'
            );
        }
        
        function testPaymentReceived() {
            log('💳 Testing payment received notification...', 'info');
            sendNotification(
                '💳 Payment Received',
                'Amount: ₹3,000\nFrom: Rachel Green\nInvoice: INV-2024-002\nMethod: Online',
                'payment-received'
            );
        }
        
        function testFacilityBooking() {
            log('📚 Testing facility booking notification...', 'info');
            sendNotification(
                '📚 Facility Booking',
                'Facility: Study Room A\nBooked by: Chris Evans\nTime: 2:00 PM - 4:00 PM\nDate: 2024-01-11',
                'facility-booking'
            );
        }
        
        function testEmergencyAlert() {
            log('🚨 Testing emergency alert...', 'info');
            sendNotification(
                '🚨 EMERGENCY ALERT',
                'Emergency: Fire Drill\nLocation: Main Building\nInstructions: Evacuate immediately\nTime: 2024-01-10 14:30',
                'emergency-alert'
            );
        }
        
        function testSystemMaintenance() {
            log('🔧 Testing system maintenance notification...', 'info');
            sendNotification(
                '🔧 System Maintenance',
                'Scheduled maintenance: 2024-01-11 02:00 AM\nDuration: 2 hours\nAffected services: Online payments, QR registration',
                'system-maintenance'
            );
        }
        
        function testAllNotifications() {
            log('🚀 Testing all notifications in sequence...', 'warning');
            
            const tests = [
                testQRRegistration,
                testVisitorCallback,
                testAdmissionProcessed,
                testMemberExpiry10,
                testInvoiceCreated,
                testPaymentReceived,
                testFacilityBooking,
                testGallaReminder,
                testCustomNotification
            ];
            
            tests.forEach((test, index) => {
                setTimeout(() => {
                    test();
                }, index * 2000); // 2 second delay between each
            });
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Comprehensive notification test loaded', 'success');
            updateStats();
            
            // Check permission status
            if (Notification.permission === 'default') {
                log('⚠️ Notification permission not requested yet', 'warning');
            } else if (Notification.permission === 'denied') {
                log('❌ Notification permission denied', 'error');
            } else {
                log('✅ Notification permission granted', 'success');
            }
        });
        
        // Request permission if needed
        if (Notification.permission === 'default') {
            setTimeout(() => {
                if (confirm('Enable notifications to test the comprehensive notification system?')) {
                    Notification.requestPermission().then(permission => {
                        updateStats();
                        if (permission === 'granted') {
                            log('🎉 Notification permission granted!', 'success');
                        } else {
                            log('❌ Notification permission denied', 'error');
                        }
                    });
                }
            }, 2000);
        }
    </script>
</body>
</html>
