from django.shortcuts import get_object_or_404, render, redirect, HttpResponse
import requests
from libraryCommander.models import *

import threading
from librarian.models import *
from membership.models import Plan
from subLibrarian.models import *
from django.conf import settings
import threading
from django.db import IntegrityError
from smtplib import SMTPException
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from urllib.parse import quote
from django.http import HttpResponse


def home(request):

    if request.user.is_authenticated:

        # Check if the user is a librarian
        if Librarian_param.objects.filter(user=request.user).exists():
            return redirect("/librarian/dashboard/")
        # Check if the user is a sublibrarian
        elif Sublibrarian_param.objects.filter(user=request.user).exists():
            return redirect("/sublibrarian/dashboard/")
        else:
            # Handle the case where the user is neither a librarian nor a sublibrarian
            pass

    user_group = request.user.groups.first()
    plans = Plan.objects.all()[1:]
    return render(request, "index.html", {"user_group": user_group, "plans": plans})


def robots_txt(request):
    host = request.get_host()
    content = f"""User-agent: *
        Disallow: /superadmin/
        Disallow: /private/
        Disallow: /cdn-cgi/
        Disallow: /*?next=
        Disallow: /librarycommander/
        Disallow: /manager/
        Disallow: /about/
        Disallow: /contact/
        Allow: /librarian/library-details/
        Allow: /librarian/library-list/
        Allow: /sitemap.xml
        Allow: /*.css$
        Allow: /*.js$

Sitemap: http://{host}/sitemap.xml"""
    return HttpResponse(content, content_type="text/plain")


def blog(request):
    return render(request, "blog.html")


def contact(request):
    # Return 410 Gone status to indicate this page is permanently removed
    return HttpResponse("This page has been permanently removed.", status=410)


def about(request):
    # Return 410 Gone status to indicate this page is permanently removed
    return HttpResponse("This page has been permanently removed.", status=410)


def services(request):
    return render(request, "services.html")


def page_not_found_view(request, exception=None):
    return render(request, "404.html", status=404)


def send_sms(sender, number, message, template_id):

    # Format the URL with the variables
    url = (
        f"https://bulksmsplans.com/api/send_sms?"
        f"api_id=APIm4H3ebTY132362&api_password=o9TpHXI1&"
        f"sms_type=Transactional&sms_encoding=text&"
        f"sender={sender}&number={number}&"
        f"message={message}&template_id={template_id}"
    )

    # Send the GET request
    response = requests.get(url)

    return HttpResponse(response.text)


def qrcode_redirect(request):
    return render(request, "downalod_app.html")


def jharkhand_launch(request):
    """
    Jharkhand Launch Landing Page with Local SEO optimization
    """
    # Get some sample data for the page
    plans = Plan.objects.all()[1:]  # Exclude free plan for better conversion

    # Context data for the Jharkhand launch page
    context = {
        'plans': plans,
        'launch_date': '2025-06-09',
        'state': 'Jharkhand',
        'benefits': [
            {
                'title': 'Digital Transformation',
                'description': 'Transform your library from paper-based to fully digital operations',
                'icon': 'fas fa-digital-tachograph'
            },
            {
                'title': 'Real-time Management',
                'description': 'Track students, seats, and payments in real-time from anywhere',
                'icon': 'fas fa-clock'
            },
            {
                'title': 'Cost Reduction',
                'description': 'Reduce operational costs by up to 70% with automated processes',
                'icon': 'fas fa-rupee-sign'
            },
            {
                'title': 'Student Engagement',
                'description': 'Improve student satisfaction with instant notifications and updates',
                'icon': 'fas fa-users'
            },
            {
                'title': 'Data Analytics',
                'description': 'Make informed decisions with comprehensive reports and analytics',
                'icon': 'fas fa-chart-line'
            },
            {
                'title': 'Mobile First',
                'description': 'Manage your library on-the-go with our mobile-optimized platform',
                'icon': 'fas fa-mobile-alt'
            }
        ],
        'features': [
            'Student Registration & Management',
            'Seat Booking & Allocation',
            'Fee Collection & Invoicing',
            'SMS & Email Notifications',
            'Attendance Tracking',
            'Revenue Analytics',
            'Multi-location Support',
            'Cloud-based Security'
        ],
        'testimonials': [
            {
                'name': 'Rajesh Kumar',
                'library': 'Success Library, Ranchi',
                'text': 'Librainian transformed our operations completely. Student management is now effortless!',
                'rating': 5
            },
            {
                'name': 'Priya Sharma',
                'library': 'Elite Study Center, Dhanbad',
                'text': 'The automated fee collection feature saved us hours of manual work every day.',
                'rating': 5
            }
        ],
        'stats': {
            'libraries': '500+',
            'students': '50,000+',
            'cities': '25+',
            'satisfaction': '98%'
        }
    }

    return render(request, "jharkhand.html", context)
