<!DOCTYPE html>
<html lang="en">
<head>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Form</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts - Comfortaa -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Material Design for Bootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.0/mdb.min.css" rel="stylesheet">
    <style>
        :root {
            /* Light mode colors */
            --primary-color: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --secondary-color: #06b6d4;
            --secondary-dark: #0891b2;
            --surface-color: rgba(255, 255, 255, 0.25);
            --surface-hover: rgba(255, 255, 255, 0.35);
            --background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --error-color: #ef4444;
            --success-color: #10b981;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --border-color: rgba(255, 255, 255, 0.2);
            --shadow-light: rgba(255, 255, 255, 0.25);
            --shadow-dark: rgba(0, 0, 0, 0.1);
        }

        /* Dark mode detection and variables */
        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #818cf8;
                --primary-light: #a5b4fc;
                --primary-dark: #6366f1;
                --secondary-color: #22d3ee;
                --secondary-dark: #06b6d4;
                --surface-color: rgba(17, 24, 39, 0.25);
                --surface-hover: rgba(17, 24, 39, 0.35);
                --background-color: linear-gradient(135deg, #1e293b 0%, #334155 100%);
                --error-color: #f87171;
                --success-color: #34d399;
                --text-primary: #f9fafb;
                --text-secondary: #d1d5db;
                --text-light: #9ca3af;
                --border-color: rgba(255, 255, 255, 0.1);
                --shadow-light: rgba(255, 255, 255, 0.1);
                --shadow-dark: rgba(0, 0, 0, 0.3);
            }
        }

        /* Fix for asterisks and other inline elements */
        span {
            display: inline !important;
        }

        * {
            box-sizing: border-box;
        }

        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-primary);
            line-height: 1.6;
            background: var(--background-color) fixed;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            backdrop-filter: blur(10px);
        }

        .registration-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .registration-container {
            background: var(--surface-color);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 24px;
            box-shadow:
                0 8px 32px var(--shadow-dark),
                inset 0 1px 0 var(--shadow-light);
            padding: 40px;
            max-width: 800px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        /* Glassmorphism accent bar */
        .registration-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 24px 24px 0 0;
        }

        /* Glassmorphism background pattern */
        .registration-container::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, var(--shadow-light) 1px, transparent 1px);
            background-size: 20px 20px;
            opacity: 0.1;
            pointer-events: none;
            z-index: -1;
        }

        .registration-container h2 {
            color: var(--text-primary);
            margin-bottom: 24px;
            text-align: center;
            font-weight: 600;
            font-size: 1.875rem;
            text-shadow: 0 2px 4px var(--shadow-dark);
        }

        /* Form header with icon */
        .form-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 32px;
            flex-direction: column;
            gap: 16px;
        }

        .form-header .material-icons {
            font-size: 48px;
            color: var(--primary-color);
            background: var(--surface-hover);
            backdrop-filter: blur(10px);
            border-radius: 50%;
            padding: 16px;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 16px var(--shadow-dark);
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: block;
            font-size: 0.875rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            letter-spacing: 0.025em;
        }

        /* Glassmorphism form fields */
        .form-control,
        .form-select {
            padding: 14px 16px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: var(--surface-color);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: var(--text-primary);
            box-shadow: inset 0 1px 0 var(--shadow-light);
        }

        .form-control:focus,
        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow:
                0 0 0 3px rgba(99, 102, 241, 0.1),
                inset 0 1px 0 var(--shadow-light);
            outline: none;
            background: var(--surface-hover);
            transform: translateY(-1px);
        }

        .form-control::placeholder {
            color: var(--text-light);
            opacity: 0.8;
        }

        /* Input with icon - glassmorphism style */
        .input-with-icon {
            position: relative;
        }

        .input-with-icon .material-icons {
            position: absolute;
            left: 14px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 20px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 2;
        }

        .input-with-icon .form-control,
        .input-with-icon .form-select {
            padding-left: 48px;
        }

        .input-focused .material-icons {
            color: var(--primary-color) !important;
            transform: translateY(-50%) scale(1.1);
        }

        /* Glassmorphism button */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: 1px solid var(--border-color);
            padding: 14px 32px;
            font-weight: 600;
            text-transform: none;
            letter-spacing: 0.025em;
            border-radius: 12px;
            box-shadow:
                0 4px 16px rgba(99, 102, 241, 0.3),
                inset 0 1px 0 var(--shadow-light);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: white;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover, .btn-primary:focus {
            background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            box-shadow:
                0 6px 20px rgba(99, 102, 241, 0.4),
                inset 0 1px 0 var(--shadow-light);
            transform: translateY(-2px);
            color: white;
        }

        .btn-primary:active {
            transform: translateY(0);
            box-shadow:
                0 2px 8px rgba(99, 102, 241, 0.3),
                inset 0 1px 0 var(--shadow-light);
        }

        /* Glassmorphism file input styling */
        .file-input-container {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .file-input-label {
            display: flex;
            align-items: center;
            padding: 14px 16px;
            background: var(--surface-color);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: var(--text-primary);
        }

        .file-input-label:hover {
            background: var(--surface-hover);
            border-color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px var(--shadow-dark);
        }

        .file-input-label .material-icons {
            margin-right: 12px;
            color: var(--primary-color);
            font-size: 24px;
        }

        .file-input-text {
            flex-grow: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 500;
        }

        /* Helper text and error messages */
        .text-danger {
            color: var(--error-color) !important;
            font-size: 0.8rem;
            margin-top: 6px;
            display: block;
            font-weight: 500;
        }

        .form-text {
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin-top: 6px;
            opacity: 0.8;
        }

        /* Glassmorphism alert styling */
        .alert {
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 24px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 4px 16px var(--shadow-dark);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border-color: rgba(16, 185, 129, 0.2);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .registration-wrapper {
                padding: 16px;
            }

            .registration-container {
                padding: 24px 20px;
                width: 100%;
                margin: 0;
                border-radius: 20px;
            }

            .form-header .material-icons {
                font-size: 40px;
                padding: 12px;
            }

            .registration-container h2 {
                font-size: 1.5rem;
            }

            .form-group {
                margin-bottom: 20px;
            }

            .btn-primary {
                width: 100%;
                padding: 16px;
                font-size: 1.1rem;
            }

            .input-with-icon .form-control,
            .input-with-icon .form-select {
                padding-left: 44px;
            }

            .input-with-icon .material-icons {
                left: 12px;
                font-size: 18px;
            }
        }

        @media (max-width: 480px) {
            .registration-container {
                padding: 20px 16px;
            }

            .form-header {
                gap: 12px;
            }

            .registration-container h2 {
                font-size: 1.375rem;
            }
        }

        /* Smooth animations for glassmorphism */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes slideInFromLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .registration-container {
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .form-group {
            animation: slideInFromLeft 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            opacity: 0;
        }

        .form-group:nth-child(1) { animation-delay: 0.1s; }
        .form-group:nth-child(2) { animation-delay: 0.15s; }
        .form-group:nth-child(3) { animation-delay: 0.2s; }
        .form-group:nth-child(4) { animation-delay: 0.25s; }
        .form-group:nth-child(5) { animation-delay: 0.3s; }
        .form-group:nth-child(6) { animation-delay: 0.35s; }
        .form-group:nth-child(7) { animation-delay: 0.4s; }
        .form-group:nth-child(8) { animation-delay: 0.45s; }

        /* Glassmorphism button ripple effect */
        .btn-primary::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .btn-primary:active::before {
            width: 300px;
            height: 300px;
        }

        /* Loading state for button */
        .btn-primary.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .btn-primary.loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>


    <div class="registration-wrapper">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12 col-md-10 col-lg-8">
                {% if messages %}
                    <div id="messages">
                        {% for message in messages %}
                            <div class="alert alert-success" role="alert">
                                {{ message }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                    <div class="registration-container">
                        <div class="form-header">
                            <span class="material-icons">how_to_reg</span>
                            <div>
                                <h2 class="mb-0">{{librarian.library_name}}</h2>
                                <p class="mb-0 text-center" style="color: var(--text-secondary); font-size: 1.1rem; font-weight: 500;">Student Registration</p>
                            </div>
                        </div>
                        <p class="mb-3 text-center" style="color: var(--text-secondary);">Please fill in the following details to complete your registration.</p>

                        <!-- Security Deposit Notice -->
                        {% if librarian.security_deposit_enabled %}
                        <div class="alert alert-info mb-3" style="background: rgba(13, 202, 240, 0.1); border: 1px solid rgba(13, 202, 240, 0.3); border-radius: 12px; padding: 12px;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <span class="material-icons" style="color: #0dcaf0; font-size: 20px;">info</span>
                                <div>
                                    <strong style="color: var(--text-primary);">Security Deposit Required</strong>
                                    <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;">
                                        This library requires a security deposit of ₹{{ librarian.security_deposit_amount }} for all students.
                                    </p>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        <p class="mb-4 text-center" style="color: var(--text-secondary);">Fields marked with <span class="text-danger" style="display: inline;">*</span> are mandatory.</p>
                        <div class="mb-4" style="height: 1px; background: linear-gradient(to right, transparent, var(--border-color), var(--primary-color), var(--border-color), transparent);"></div>
                        <form method="POST" enctype="multipart/form-data">
                            {% csrf_token %}

                            <div class="row">
                                <div class="col-md-6 form-group">
                                    <label for="name" class="form-label">Full Name <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">person</span>
                                        <input type="text" class="form-control" id="name" name="name" required maxlength="150" placeholder="Enter full name">
                                    </div>
                                </div>
                                <div class="col-md-6 form-group">
                                    <label for="f_name" class="form-label">Father's Name</label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">people</span>
                                        <input type="text" class="form-control" id="f_name" name="f_name" maxlength="100" placeholder="Enter father's name">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 form-group">
                                    <label for="age" class="form-label">Age</label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">cake</span>
                                        <input type="number" class="form-control" id="age" name="age" min="0" max="150" placeholder="Enter age">
                                    </div>
                                </div>
                                <div class="col-md-6 form-group">
                                    <label for="gender" class="form-label">Gender <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">wc</span>
                                        <select class="form-select" id="gender" name="gender" required>
                                            <option value="">Select Gender</option>
                                            <option value="male">Male</option>
                                            <option value="female">Female</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 form-group">
                                    <label for="email" class="form-label">Email</label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">email</span>
                                        <input type="email" class="form-control" id="email" name="email" maxlength="100" placeholder="Enter email address (optional)">
                                    </div>
                                </div>
                                <div class="col-md-6 form-group">
                                    <label for="mobile" class="form-label">Mobile Number <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">phone</span>
                                        <input type="tel" class="form-control" id="mobile" name="mobile" required pattern="[0-9]{10}" placeholder="Enter 10-digit mobile number">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 form-group">
                                    <label for="locality" class="form-label">Locality <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">location_on</span>
                                        <input type="text" class="form-control" id="locality" name="locality" required maxlength="100" placeholder="Enter locality">
                                    </div>
                                </div>
                                <div class="col-md-6 form-group">
                                    <label for="city" class="form-label">City <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">location_city</span>
                                        <input type="text" class="form-control" id="city" name="city" maxlength="100" placeholder="Enter city name" required pattern="[A-Za-z ]+" title="Please enter only alphabets">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 form-group">
                                    <label for="state" class="form-label">State <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">map</span>
                                        <select class="form-select" id="states" name="state" required>
                                            <option value="">Select a state</option>
                                            {% for state in states %}
                                            <option value="{{ state.id }}">{{ state.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6 form-group">
                                    <label for="course" class="form-label">Course <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">school</span>
                                        <select class="form-select" id="courseName" name="course" required>
                                            <option value="">Select a course</option>
                                            {% for course in courses %}
                                            <option value="{{ course.id }}">{{ course.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Proof of Identity Section (conditional) -->
                            {% if librarian.proof_of_identity_enabled %}
                            <div class="row">
                                <div class="col-md-6 form-group">
                                    <label for="identity_type" class="form-label">Identity Document Type</label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">badge</span>
                                        <select class="form-select" id="identity_type" name="identity_type">
                                            <option value="">Select identity type (Optional)</option>
                                            <option value="pan">PAN Card</option>
                                            <option value="aadhaar">Aadhaar Card</option>
                                            <option value="driving_license">Driving License</option>
                                            <option value="voter_id">Voter ID Card</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6 form-group">
                                    <label for="identity_number" class="form-label">Identity Document Number</label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">confirmation_number</span>
                                        <input type="text" class="form-control" id="identity_number" name="identity_number"
                                               maxlength="50" placeholder="Enter document number" onchange="validateIdentityNumber()">
                                    </div>
                                    <div class="help-text" style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 4px;">
                                        This information will be masked for privacy protection
                                    </div>
                                    <div id="identity_validation_message" style="font-size: 0.8rem; margin-top: 4px; display: none;"></div>
                                </div>
                            </div>
                            {% endif %}

                            <div class="form-group mb-4">
                                <label for="image" class="form-label">Student Image</label>
                                <div class="file-input-container">
                                    <label for="image" class="file-input-label">
                                        <span class="material-icons">add_photo_alternate</span>
                                        <span class="file-input-text">Choose a file...</span>
                                    </label>
                                    <input type="file" class="form-control d-none" id="image" name="image" accept="image/*">
                                </div>
                                <small class="form-text text-muted">Upload a clear passport-size photograph (Max 5MB)</small>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <span class="material-icons align-middle me-2">check_circle</span>
                                    Register Now
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Material Design for Bootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.0/mdb.min.js"></script>

    <!-- Loader Script already included in head -->

    <!-- Enhanced Glassmorphism Form Script -->
    <script>
        (function() {
            'use strict';

            // File input enhancement with glassmorphism effects
            const fileInput = document.getElementById('image');
            const fileInputText = document.querySelector('.file-input-text');
            const fileInputLabel = document.querySelector('.file-input-label');

            if (fileInput && fileInputText && fileInputLabel) {
                fileInput.addEventListener('change', function() {
                    if (fileInput.files.length > 0) {
                        const fileName = fileInput.files[0].name;
                        fileInputText.textContent = fileName;
                        fileInputLabel.style.borderColor = 'var(--primary-color)';
                        fileInputLabel.style.background = 'var(--surface-hover)';
                    } else {
                        fileInputText.textContent = 'Choose a file...';
                        fileInputLabel.style.borderColor = 'var(--border-color)';
                        fileInputLabel.style.background = 'var(--surface-color)';
                    }
                });
            }

            // Enhanced form validation with glassmorphism feedback
            window.addEventListener('load', function() {
                const form = document.querySelector('form');
                const submitBtn = document.querySelector('.btn-primary');

                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();

                        // Find the first invalid input and focus it
                        const invalidInputs = form.querySelectorAll(':invalid');
                        if (invalidInputs.length > 0) {
                            invalidInputs[0].focus();
                            invalidInputs[0].scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });

                            // Add error styling
                            invalidInputs[0].style.borderColor = 'var(--error-color)';
                            invalidInputs[0].style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
                        }
                    } else {
                        // Add loading state to button
                        submitBtn.classList.add('loading');
                        submitBtn.innerHTML = '<span class="material-icons align-middle me-2">hourglass_empty</span>Processing...';
                    }
                    form.classList.add('was-validated');
                }, false);
            }, false);

            // Enhanced focus effects for glassmorphism
            const inputs = document.querySelectorAll('.form-control, .form-select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('input-focused');
                    // Remove any error styling on focus
                    this.style.borderColor = '';
                    this.style.boxShadow = '';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('input-focused');
                });

                // Add typing effect
                input.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        this.style.background = 'var(--surface-hover)';
                    } else {
                        this.style.background = 'var(--surface-color)';
                    }
                });
            });

            // Add glassmorphism hover effects to form groups
            const formGroups = document.querySelectorAll('.form-group');
            formGroups.forEach(group => {
                group.addEventListener('mouseenter', function() {
                    const input = this.querySelector('.form-control, .form-select');
                    if (input && !input.matches(':focus')) {
                        input.style.transform = 'translateY(-1px)';
                        input.style.boxShadow = '0 4px 12px var(--shadow-dark), inset 0 1px 0 var(--shadow-light)';
                    }
                });

                group.addEventListener('mouseleave', function() {
                    const input = this.querySelector('.form-control, .form-select');
                    if (input && !input.matches(':focus')) {
                        input.style.transform = '';
                        input.style.boxShadow = '';
                    }
                });
            });

            // Smooth scroll to top on page load
            window.scrollTo({ top: 0, behavior: 'smooth' });

        })();

        // Identity document validation
        function validateIdentityNumber() {
            const identityType = document.getElementById('identity_type').value;
            const identityNumber = document.getElementById('identity_number').value.trim();
            const messageDiv = document.getElementById('identity_validation_message');

            if (!identityType || !identityNumber) {
                messageDiv.style.display = 'none';
                return true;
            }

            let isValid = false;
            let message = '';

            switch(identityType) {
                case 'aadhaar':
                    // Aadhaar: 12 digits
                    isValid = /^\d{12}$/.test(identityNumber);
                    message = isValid ? '✓ Valid Aadhaar format' : '✗ Aadhaar must be 12 digits';
                    break;
                case 'pan':
                    // PAN: 10 alphanumeric (5 letters, 4 digits, 1 letter)
                    isValid = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(identityNumber.toUpperCase());
                    message = isValid ? '✓ Valid PAN format' : '✗ PAN must be 10 characters (**********)';
                    break;
                case 'driving_license':
                    // Driving License: 15 alphanumeric
                    isValid = /^[A-Z0-9]{15}$/.test(identityNumber.toUpperCase());
                    message = isValid ? '✓ Valid Driving License format' : '✗ Driving License must be 15 alphanumeric characters';
                    break;
                case 'voter_id':
                    // Voter ID: 10 alphanumeric
                    isValid = /^[A-Z0-9]{10}$/.test(identityNumber.toUpperCase());
                    message = isValid ? '✓ Valid Voter ID format' : '✗ Voter ID must be 10 alphanumeric characters';
                    break;
            }

            messageDiv.textContent = message;
            messageDiv.style.color = isValid ? '#28a745' : '#dc3545';
            messageDiv.style.display = 'block';

            return isValid;
        }

        // Add event listeners
        document.getElementById('identity_type').addEventListener('change', validateIdentityNumber);
        document.getElementById('identity_number').addEventListener('input', validateIdentityNumber);
    </script>
</body>
</html>