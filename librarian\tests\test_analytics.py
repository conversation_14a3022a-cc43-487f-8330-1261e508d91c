from django.test import TestCase
from django.utils import timezone
from django.contrib.auth.models import User
from datetime import timedelta, date
from dateutil.relativedelta import relativedelta
import json

from librarian.models import Librarian_param, AnalyticsCache
from librarian.views import calculate_dashboard_analytics
from studentsData.models import StudentData, Invoice, Courses, States
from visitorsData.models import Visitor
from manager.models import Manager_param


class AnalyticsCalculationTests(TestCase):
    """Test suite for analytics calculations"""
    
    def setUp(self):
        """Set up test data"""
        # Create manager
        manager_user = User.objects.create_user(
            username='testmanager',
            email='<EMAIL>',
            password='password123'
        )
        self.manager = Manager_param.objects.create(
            user=manager_user,
            is_manager=True
        )
        
        # Create librarian
        librarian_user = User.objects.create_user(
            username='testlibrarian',
            email='<EMAIL>',
            password='password123'
        )
        self.librarian = Librarian_param.objects.create(
            user=librarian_user,
            manager=self.manager,
            library_name='Test Library',
            librarian_address='Test Address',
            is_librarian=True
        )
        
        # Create course and state
        self.course = Courses.objects.create(name='Test Course')
        self.state = States.objects.create(name='Test State')
        
        # Current date for testing
        self.current_date = timezone.now().date()
        self.current_month_start = date(self.current_date.year, self.current_date.month, 1)
        self.last_month_start = (self.current_month_start - relativedelta(months=1))
        self.last_month_end = self.current_month_start - timedelta(days=1)
        
        # Create test data
        self.create_test_data()
    
    def create_test_data(self):
        """Create test data for analytics calculations"""
        # Create students for current month
        for i in range(5):
            student = StudentData.objects.create(
                librarian=self.librarian,
                course=self.course,
                name=f'Current Month Student {i+1}',
                gender='male' if i % 2 == 0 else 'female',
                email=f'current{i+1}@example.com',
                mobile=9000000000 + i,
                locality='Test Area',
                city='Test City',
                state=self.state,
                registration_date=self.current_date - timedelta(days=i),
                registration_fee=5000
            )
            
            # Create invoice for this student
            Invoice.objects.create(
                student=student,
                issue_date=student.registration_date,
                due_date=student.registration_date + timedelta(days=30),
                total_amount=5000,
                is_active=True
            )
        
        # Create students for last month
        for i in range(3):
            student = StudentData.objects.create(
                librarian=self.librarian,
                course=self.course,
                name=f'Last Month Student {i+1}',
                gender='male' if i % 2 == 0 else 'female',
                email=f'last{i+1}@example.com',
                mobile=8000000000 + i,
                locality='Test Area',
                city='Test City',
                state=self.state,
                registration_date=self.last_month_start + timedelta(days=i),
                registration_fee=5000
            )
            
            # Create invoice for this student
            Invoice.objects.create(
                student=student,
                issue_date=student.registration_date,
                due_date=student.registration_date + timedelta(days=30),
                total_amount=5000,
                is_active=True
            )
        
        # Create invoices due today
        student = StudentData.objects.filter(librarian=self.librarian).first()
        Invoice.objects.create(
            student=student,
            issue_date=self.current_date - timedelta(days=15),
            due_date=self.current_date,
            total_amount=3000,
            is_active=True
        )
        
        # Create visitors
        for i in range(10):
            Visitor.objects.create(
                librarian=self.librarian,
                date=self.current_date - timedelta(days=i),
                name=f'Test Visitor {i+1}',
                contact=7000000000 + i,
                email=f'visitor{i+1}@example.com',
                notes='Test visitor inquiry',
                status='pending'
            )
    
    def test_students_this_month_calculation(self):
        """Test students this month calculation"""
        analytics = calculate_dashboard_analytics(self.librarian)
        
        # We created 5 students this month
        self.assertEqual(analytics['students_this_month'], 5)
        
        # Growth should be positive (5 this month vs 3 last month)
        self.assertTrue(analytics['students_growth_positive'])
        self.assertGreater(analytics['students_growth_percent'], 0)
    
    def test_new_registrations_calculation(self):
        """Test new registrations calculation"""
        analytics = calculate_dashboard_analytics(self.librarian)
        
        # We created 5 new registrations this month
        self.assertEqual(analytics['new_registrations_this_month'], 5)
        
        # Growth should be positive (5 this month vs 3 last month)
        self.assertTrue(analytics['registrations_growth_positive'])
        self.assertGreater(analytics['registrations_growth_percent'], 0)
    
    def test_todays_collection_calculation(self):
        """Test today's collection calculation"""
        analytics = calculate_dashboard_analytics(self.librarian)
        
        # We created one invoice due today with amount 3000
        self.assertEqual(analytics['todays_collection'], 3000)
    
    def test_chart_data_format(self):
        """Test chart data format"""
        analytics = calculate_dashboard_analytics(self.librarian)
        
        # Check growth months and counts
        self.assertIsInstance(analytics['growth_months'], list)
        self.assertIsInstance(analytics['growth_counts'], list)
        self.assertEqual(len(analytics['growth_months']), len(analytics['growth_counts']))
        
        # Check revenue months and amounts
        self.assertIsInstance(analytics['revenue_months'], list)
        self.assertIsInstance(analytics['revenue_amounts'], list)
        self.assertEqual(len(analytics['revenue_months']), len(analytics['revenue_amounts']))
        
        # Check visitor days and counts
        self.assertIsInstance(analytics['visitor_days'], list)
        self.assertIsInstance(analytics['visitor_counts'], list)
        self.assertEqual(len(analytics['visitor_days']), len(analytics['visitor_counts']))
    
    def test_cache_functionality(self):
        """Test analytics cache functionality"""
        # First calculation should create cache
        analytics1 = calculate_dashboard_analytics(self.librarian)
        cache = AnalyticsCache.objects.get(librarian=self.librarian)
        
        # Verify cache values
        self.assertEqual(cache.students_this_month, analytics1['students_this_month'])
        self.assertEqual(cache.new_registrations_this_month, analytics1['new_registrations_this_month'])
        self.assertEqual(float(cache.todays_collection), analytics1['todays_collection'])
        
        # Second calculation should use cache
        analytics2 = calculate_dashboard_analytics(self.librarian)
        self.assertEqual(analytics1, analytics2)
        
        # Force refresh should recalculate
        cache.force_refresh()
        analytics3 = calculate_dashboard_analytics(self.librarian)
        
        # Values should be the same (since data hasn't changed)
        self.assertEqual(analytics1['students_this_month'], analytics3['students_this_month'])
        
        # But cache should be updated
        cache.refresh_from_db()
        self.assertFalse(cache.force_recalculate)
    
    def test_cache_invalidation(self):
        """Test cache invalidation when data changes"""
        # Initial calculation
        analytics1 = calculate_dashboard_analytics(self.librarian)
        
        # Create a new student (should trigger cache invalidation via signal)
        student = StudentData.objects.create(
            librarian=self.librarian,
            course=self.course,
            name='New Test Student',
            gender='male',
            email='<EMAIL>',
            mobile=9999999999,
            locality='Test Area',
            city='Test City',
            state=self.state,
            registration_date=self.current_date,
            registration_fee=5000
        )
        
        # Get cache and check if it's marked for recalculation
        cache = AnalyticsCache.objects.get(librarian=self.librarian)
        self.assertIsNotNone(cache.last_student_activity)
        
        # Recalculate analytics
        analytics2 = calculate_dashboard_analytics(self.librarian)
        
        # Students this month should increase by 1
        self.assertEqual(analytics1['students_this_month'] + 1, analytics2['students_this_month'])
        
        # New registrations should increase by 1
        self.assertEqual(analytics1['new_registrations_this_month'] + 1, analytics2['new_registrations_this_month'])
    
    def test_negative_growth_scenario(self):
        """Test negative growth scenario"""
        # Delete all current month students
        StudentData.objects.filter(
            librarian=self.librarian,
            registration_date__gte=self.current_month_start
        ).delete()
        
        # Calculate analytics
        analytics = calculate_dashboard_analytics(self.librarian)
        
        # Students this month should be 0
        self.assertEqual(analytics['students_this_month'], 0)
        
        # Growth should be negative (0 this month vs 3 last month)
        self.assertFalse(analytics['students_growth_positive'])
        self.assertLess(analytics['students_growth_percent'], 0)
    
    def test_zero_data_scenario(self):
        """Test scenario with no data"""
        # Delete all data
        StudentData.objects.filter(librarian=self.librarian).delete()
        Visitor.objects.filter(librarian=self.librarian).delete()
        
        # Calculate analytics
        analytics = calculate_dashboard_analytics(self.librarian)
        
        # All counts should be 0
        self.assertEqual(analytics['students_this_month'], 0)
        self.assertEqual(analytics['new_registrations_this_month'], 0)
        self.assertEqual(analytics['todays_collection'], 0)
        
        # Lists should be empty but valid
        self.assertEqual(analytics['growth_months'], [])
        self.assertEqual(analytics['growth_counts'], [])
        self.assertEqual(analytics['revenue_months'], [])
        self.assertEqual(analytics['revenue_amounts'], [])
        self.assertEqual(analytics['visitor_days'], [])
        self.assertEqual(analytics['visitor_counts'], [])
