{% extends "base.html" %}

{% block title %}Blog Management - Librainian{% endblock %}

{% block page_title %}Blog Management{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Blogs</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Blog List Specific Styles - using unified glass system */
    .blog-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .glass-card {
        /* Additional styling for blog cards */
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .blog-card-header {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
    }

    .blog-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 0.75rem;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .blog-description {
        color: var(--gray-600);
        font-size: 0.875rem;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .blog-card-body {
        padding: 1.5rem;
        flex-grow: 1;
        position: relative;
        z-index: 2;
    }

    .blog-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-top: 1rem;
    }

    .action-btn {
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        font-size: 0.75rem;
        text-decoration: none;
        transition: var(--transition);
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        flex: 1;
        justify-content: center;
        min-width: 70px;
    }

    .action-btn-view {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
    }

    .action-btn-view:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
        color: white;
        text-decoration: none;
    }

    .action-btn-copy {
        background: rgba(107, 114, 128, 0.1);
        color: var(--gray-600);
        border: 1px solid rgba(107, 114, 128, 0.3);
    }

    .action-btn-copy:hover {
        background: rgba(107, 114, 128, 0.2);
        color: var(--gray-700);
        text-decoration: none;
    }

    .action-btn-edit {
        background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);
        color: white;
    }

    .action-btn-edit:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
        color: white;
        text-decoration: none;
    }

    .action-btn-delete {
        background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
        color: white;
    }

    .action-btn-delete:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
        color: white;
    }

    .blog-controls {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-lg);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .create-btn {
        background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
        border: none;
        border-radius: var(--border-radius);
        padding: 0.75rem 1.5rem;
        color: white;
        font-weight: 600;
        font-size: 0.875rem;
        text-decoration: none;
        transition: var(--transition-slow);
        box-shadow: var(--shadow-md);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .create-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
        color: white;
        text-decoration: none;
    }

    .search-control {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius);
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        transition: var(--transition);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .search-control:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
        background: white;
    }

    .entries-select {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius);
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        transition: var(--transition);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        cursor: pointer;
    }

    .entries-select:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
        background: white;
    }

    .pagination-modern {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 2rem;
    }

    .page-btn {
        padding: 0.75rem 1rem;
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius);
        color: var(--gray-700);
        text-decoration: none;
        font-weight: 600;
        font-size: 0.875rem;
        transition: var(--transition);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        cursor: pointer;
    }

    .page-btn:hover {
        background: var(--primary);
        border-color: var(--primary);
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .page-btn.active {
        background: var(--primary);
        border-color: var(--primary);
        color: white;
    }

    .copy-status {
        color: var(--success);
        font-size: 0.75rem;
        font-weight: 600;
    }

    .no-blogs {
        text-align: center;
        padding: 3rem;
        color: var(--gray-500);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-lg);
    }

    .no-blogs i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: var(--gray-400);
    }

    @media (max-width: 768px) {
        .blog-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        .blog-controls {
            padding: 1rem;
        }
        
        .blog-actions {
            flex-direction: column;
        }
        
        .action-btn {
            flex: none;
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-content fade-in">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="modern-card">
            <div class="modern-card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="page-title mb-2">
                            <i class="fas fa-blog me-2 text-primary"></i>
                            Blog Management
                        </h2>
                        <p class="page-subtitle text-muted mb-0">
                            Manage your blog posts, create new content, and track performance
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="page-actions">
                            <span class="badge bg-info-subtle text-info px-3 py-2">
                                <i class="fas fa-list me-1"></i>
                                {{ blogs|length }} Blog{{ blogs|length|pluralize }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Blog Controls -->
    <div class="blog-controls">
        <div class="row align-items-center">
            <div class="col-md-4">
                <a href="/blogs/create-blog/" class="create-btn">
                    <i class="fas fa-plus"></i>
                    Create New Blog
                </a>
            </div>
            <div class="col-md-4">
                <select id="entriesPerPage" class="entries-select w-100">
                    <option value="6">Show 6 blogs</option>
                    <option value="12">Show 12 blogs</option>
                    <option value="24">Show 24 blogs</option>
                    <option value="48">Show 48 blogs</option>
                </select>
            </div>
            <div class="col-md-4">
                <input type="text" 
                       id="searchInput" 
                       class="search-control w-100" 
                       placeholder="Search blogs...">
            </div>
        </div>
    </div>

    <!-- Blog Grid -->
    <div class="blog-grid" id="blogList">
        {% for blog in blogs %}
        <div class="glass-card">
            <div class="blog-card-header">
                <h5 class="blog-title">{{ blog.title }}</h5>
                <p class="blog-description">{{ blog.description|truncatewords:20 }}</p>
            </div>
            <div class="blog-card-body">
                <div class="blog-meta mb-3">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        {{ blog.date_created|date:"M d, Y" }}
                        <span class="mx-2">|</span>
                        <i class="fas fa-tag me-1"></i>
                        {{ blog.category }}
                    </small>
                </div>

                <div class="blog-actions">
                    <a href="/blogs/p/{{ blog.slug }}/"
                       target="_blank"
                       class="action-btn action-btn-view"
                       title="View Blog">
                        <i class="fas fa-eye"></i>
                        View
                    </a>
                    <button class="action-btn action-btn-copy"
                            onclick="copyLink('https://librainian.com/blogs/p/{{ blog.slug }}/', this)"
                            title="Copy Link">
                        <i class="fas fa-link"></i>
                        <span class="copy-text">Copy</span>
                        <span class="copy-status" style="display: none;">Copied!</span>
                    </button>
                    <a href="/blogs/update/{{ blog.slug }}/"
                       class="action-btn action-btn-edit"
                       title="Edit Blog">
                        <i class="fas fa-edit"></i>
                        Edit
                    </a>
                    <button class="action-btn action-btn-delete"
                            onclick="confirmDelete('{{ blog.slug }}')"
                            title="Delete Blog">
                        <i class="fas fa-trash-alt"></i>
                        Delete
                    </button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="no-blogs">
            <i class="fas fa-blog"></i>
            <h4>No blogs found</h4>
            <p>Start by creating your first blog post!</p>
            <a href="/blogs/create-blog/" class="create-btn mt-3">
                <i class="fas fa-plus"></i>
                Create Your First Blog
            </a>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    <nav aria-label="Blog pagination">
        <div id="pagination" class="pagination-modern"></div>
    </nav>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-0">Are you sure you want to delete this blog post? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    Cancel
                </button>
                <a id="confirmDeleteButton" href="#" class="btn btn-danger">
                    <i class="fas fa-trash-alt me-1"></i>
                    Delete Blog
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const blogList = document.getElementById('blogList');
        const searchInput = document.getElementById('searchInput');
        const entriesPerPage = document.getElementById('entriesPerPage');
        const paginationContainer = document.getElementById('pagination');

        let currentPage = 1;
        let allBlogs = [...blogList.children];
        let filteredBlogs = allBlogs;

        // Add staggered animation delays
        const cards = document.querySelectorAll('.modern-card, .blog-controls, .glass-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${(index + 1) * 0.05}s`;
            card.classList.add('slide-up');
        });

        function updatePagination() {
            const pageCount = Math.ceil(filteredBlogs.length / parseInt(entriesPerPage.value));
            paginationContainer.innerHTML = '';

            if (pageCount <= 1) return;

            for (let i = 1; i <= pageCount; i++) {
                const button = document.createElement('button');
                button.className = `page-btn ${i === currentPage ? 'active' : ''}`;
                button.textContent = i;
                button.onclick = () => changePage(i);
                paginationContainer.appendChild(button);
            }
        }

        function changePage(page) {
            currentPage = page;
            displayBlogs();
            updatePagination();
        }

        function displayBlogs() {
            blogList.innerHTML = '';
            const startIndex = (currentPage - 1) * parseInt(entriesPerPage.value);
            const endIndex = startIndex + parseInt(entriesPerPage.value);

            const blogsToShow = filteredBlogs.slice(startIndex, endIndex);
            blogsToShow.forEach(blog => {
                blogList.appendChild(blog);
            });

            if (blogsToShow.length === 0 && filteredBlogs.length === 0) {
                blogList.innerHTML = `
                    <div class="no-blogs">
                        <i class="fas fa-search"></i>
                        <h4>No blogs found</h4>
                        <p>Try adjusting your search criteria.</p>
                    </div>
                `;
            }
        }

        function filterBlogs() {
            const searchTerm = searchInput.value.toLowerCase();
            filteredBlogs = allBlogs.filter(blog => {
                const title = blog.querySelector('.blog-title').textContent.toLowerCase();
                const description = blog.querySelector('.blog-description').textContent.toLowerCase();
                return title.includes(searchTerm) || description.includes(searchTerm);
            });
            currentPage = 1;
            displayBlogs();
            updatePagination();
        }

        // Event listeners
        searchInput.addEventListener('input', filterBlogs);
        entriesPerPage.addEventListener('change', () => {
            currentPage = 1;
            displayBlogs();
            updatePagination();
        });

        // Initialize
        displayBlogs();
        updatePagination();

        // Show welcome notification
        setTimeout(() => {
            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Blog Management Ready!',
                    'Manage your blog posts and create new content.',
                    'info'
                );
            }
        }, 1000);
    });

    // Copy link function
    function copyLink(url, button) {
        navigator.clipboard.writeText(url).then(() => {
            const copyText = button.querySelector('.copy-text');
            const copyStatus = button.querySelector('.copy-status');

            copyText.style.display = 'none';
            copyStatus.style.display = 'inline';

            setTimeout(() => {
                copyText.style.display = 'inline';
                copyStatus.style.display = 'none';
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy: ', err);
        });
    }

    // Delete confirmation function
    function confirmDelete(slug) {
        const deleteButton = document.getElementById('confirmDeleteButton');
        deleteButton.href = `/blogs/delete/${slug}/`;

        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }
</script>
{% endblock %}
