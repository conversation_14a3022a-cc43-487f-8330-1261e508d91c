{% extends "base.html" %}

{% block title %}Help & Support - Librainian{% endblock %}



{% block extra_css %}
<style>
    /* Enhanced Glassmorphism Variables */
    :root {
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-bg-light: rgba(255, 255, 255, 0.15);
        --glass-bg-medium: rgba(255, 255, 255, 0.08);
        --glass-border: rgba(255, 255, 255, 0.2);
        --glass-border-light: rgba(255, 255, 255, 0.3);
        --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        --glass-shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        --glass-blur: blur(20px);
        --glass-blur-strong: blur(25px);
        --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --primary: #6366f1;
        --primary-dark: #4f46e5;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Global Background */
    body {
        background: var(--gradient-hero);
        background-attachment: fixed;
        min-height: 100vh;
    }

    /* Glass Background Effects */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    /* Help & Support Styles */
    .help-container {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 24px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .help-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        z-index: 1;
    }

    .help-container > * {
        position: relative;
        z-index: 2;
    }

    .help-header {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 24px;
        padding: 3rem 2rem;
        color: white;
        margin-bottom: 3rem;
        text-align: center;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .help-header:hover {
        transform: translateY(-5px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
        border-color: var(--glass-border-light);
    }

    .help-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
        pointer-events: none;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .help-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 2rem;
        background: var(--glass-bg-light);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 3px solid var(--glass-border-light);
        position: relative;
        z-index: 1;
        animation: pulse 2s ease-in-out infinite;
        box-shadow: var(--glass-shadow);
        transition: var(--transition);
    }

    .help-icon:hover {
        transform: scale(1.1);
        box-shadow: var(--glass-shadow-lg);
        border-color: rgba(255, 255, 255, 0.5);
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .help-icon i {
        font-size: 2rem;
        color: white;
    }

    .help-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        position: relative;
        z-index: 1;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        color: white;
    }

    .help-header p {
        font-size: 1.25rem;
        opacity: 0.95;
        margin: 0;
        position: relative;
        z-index: 1;
        line-height: 1.6;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        color: rgba(255, 255, 255, 0.95);
    }

    .help-grid {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .faq-section {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 24px;
        padding: 2rem;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .faq-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        z-index: 1;
    }

    .faq-section > * {
        position: relative;
        z-index: 2;
    }

    .faq-section:hover {
        transform: translateY(-3px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset;
        border-color: var(--glass-border-light);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: white;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    .section-title i {
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .search-box {
        position: relative;
        margin-bottom: 2rem;
    }

    .search-input {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 0.875rem 1rem 0.875rem 3rem;
        font-size: 1rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        width: 100%;
        color: white;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .search-input::placeholder {
        color: rgba(255, 255, 255, 0.8);
    }

    .search-input:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        background: rgba(255, 255, 255, 0.2);
    }

    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: white;
        font-size: 1rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .faq-item {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        margin-bottom: 1rem;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .faq-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        z-index: 1;
    }

    .faq-item:hover {
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset;
        transform: translateY(-3px);
        border-color: var(--glass-border-light);
    }

    .faq-question {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        color: white;
        padding: 1.25rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        z-index: 2;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .faq-question:hover {
        background: rgba(255, 255, 255, 0.25);
    }

    .faq-question i {
        transition: transform 0.3s ease;
    }

    .faq-question.active i {
        transform: rotate(180deg);
    }

    .faq-answer {
        padding: 1.5rem;
        color: white;
        line-height: 1.6;
        display: none;
        animation: slideDown 0.3s ease-out;
        position: relative;
        z-index: 2;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        background: rgba(255, 255, 255, 0.08);
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .ticket-section {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 24px;
        padding: 2rem;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        height: fit-content;
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .ticket-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        z-index: 1;
    }

    .ticket-section > * {
        position: relative;
        z-index: 2;
    }

    .ticket-section:hover {
        transform: translateY(-3px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset;
        border-color: var(--glass-border-light);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
        display: block;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    .form-control {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        width: 100%;
        color: white;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.8);
    }

    .form-control:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        background: rgba(255, 255, 255, 0.2);
    }

    .form-select {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        width: 100%;
        cursor: pointer;
        color: white;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .form-select:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        background: rgba(255, 255, 255, 0.2);
    }

    .btn-submit {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 0.875rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        position: relative;
        overflow: hidden;
    }

    .btn-submit::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        z-index: 1;
    }

    .btn-submit > * {
        position: relative;
        z-index: 2;
    }

    .btn-submit:hover {
        transform: translateY(-3px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.25);
    }

    .priority-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        margin-left: 0.5rem;
    }

    .priority-low {
        background: rgba(34, 197, 94, 0.2);
        color: #15803d;
    }

    .priority-medium {
        background: rgba(245, 158, 11, 0.2);
        color: #d97706;
    }

    .priority-high {
        background: rgba(239, 68, 68, 0.2);
        color: #dc2626;
    }

    /* WhatsApp Button Styles */
    .whatsapp-section {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 24px;
        padding: 2rem;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .whatsapp-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        z-index: 1;
    }

    .whatsapp-section > * {
        position: relative;
        z-index: 2;
    }

    .whatsapp-section:hover {
        transform: translateY(-3px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset;
        border-color: var(--glass-border-light);
    }

    .whatsapp-btn {
        background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
        border: none;
        border-radius: 16px;
        padding: 1rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        min-width: 250px;
        position: relative;
        overflow: hidden;
    }

    .whatsapp-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .whatsapp-btn:hover::before {
        left: 100%;
    }

    .whatsapp-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
        color: white;
        text-decoration: none;
    }

    .whatsapp-btn i {
        font-size: 1.5rem;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-5px);
        }
        60% {
            transform: translateY(-3px);
        }
    }

    .whatsapp-info {
        color: white;
        font-size: 0.95rem;
        margin-top: 1rem;
        line-height: 1.5;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .whatsapp-features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .whatsapp-feature {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 1rem;
        text-align: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .whatsapp-feature::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(37, 211, 102, 0.1) 0%,
            rgba(37, 211, 102, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        z-index: 1;
    }

    .whatsapp-feature > * {
        position: relative;
        z-index: 2;
    }

    .whatsapp-feature:hover {
        transform: translateY(-2px);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        border-color: rgba(37, 211, 102, 0.5);
    }

    .whatsapp-feature i {
        color: #25d366;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .whatsapp-feature h6 {
        color: white;
        font-weight: 600;
        margin-bottom: 0.25rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    .whatsapp-feature p {
        color: white;
        font-size: 0.875rem;
        margin: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .help-container {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .help-header {
            padding: 2rem 1rem;
        }

        .help-header h1 {
            font-size: 2rem;
        }

        .help-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .faq-section,
        .ticket-section,
        .whatsapp-section {
            padding: 1rem;
        }

        .whatsapp-btn {
            min-width: 200px;
            padding: 0.875rem 1.5rem;
            font-size: 1rem;
        }

        .whatsapp-features {
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .whatsapp-feature {
            padding: 0.75rem;
        }
    }

    /* Animation */
    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="help-container fade-in">
    <!-- Help Header -->
    <div class="help-header">
        <div class="help-icon">
            <i class="fas fa-question-circle"></i>
        </div>
        <h1>Help & Support Center</h1>
        <p>Find answers to common questions or submit a support ticket for personalized assistance</p>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    {% endif %}

    <!-- WhatsApp Support Section -->
    <div class="whatsapp-section fade-in">
        <div class="section-title justify-content-center">
            <i class="fab fa-whatsapp"></i>
            Instant WhatsApp Support
        </div>

        <p class="whatsapp-info">
            Get immediate assistance from our customer care team via WhatsApp.
            Available 24/7 for urgent issues and quick responses.
        </p>

        <a href="https://wa.me/916207628282?text=Hello%2C%20I%20need%20help%20with%20Librainian%20system"
           class="whatsapp-btn"
           target="_blank"
           rel="noopener noreferrer">
            <i class="fab fa-whatsapp"></i>
            Chat with Customer Care
        </a>

        <div class="whatsapp-features">
            <div class="whatsapp-feature">
                <i class="fas fa-clock"></i>
                <h6>24/7 Available</h6>
                <p>Round the clock support</p>
            </div>
            <div class="whatsapp-feature">
                <i class="fas fa-bolt"></i>
                <h6>Instant Response</h6>
                <p>Quick replies within minutes</p>
            </div>
            <div class="whatsapp-feature">
                <i class="fas fa-user-headset"></i>
                <h6>Expert Support</h6>
                <p>Trained customer care team</p>
            </div>
        </div>
    </div>

    <!-- Help Grid -->
    <div class="help-grid">
        <!-- FAQ Section -->
        <div class="faq-section">
            <div class="section-title">
                <i class="fas fa-question-circle"></i>
                Frequently Asked Questions
            </div>

            <!-- Search Box -->
            <div class="search-box">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search FAQs..." id="faqSearch">
            </div>

            <!-- FAQ Items -->
            <div class="faq-list" id="faqList">
                <div class="faq-item" data-keywords="account login password reset">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>How do I reset my password?</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        To reset your password, go to the login page and click on "Forgot Password". Enter your email address and follow the instructions sent to your email to create a new password.
                    </div>
                </div>

                <div class="faq-item" data-keywords="subscription billing payment cancel">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>How do I cancel my subscription?</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        To cancel your subscription, go to the "Subscription" section in your account settings and follow the cancellation instructions provided. You can also contact our support team for assistance.
                    </div>
                </div>

                <div class="faq-item" data-keywords="email address change profile update">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>How can I change my email address?</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        To change your email address, go to the "Account" or "Profile" section in your dashboard and update your email information. You may need to verify the new email address.
                    </div>
                </div>

                <div class="faq-item" data-keywords="library management features books students">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>What features are included in the library management system?</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        Our system includes student management, book cataloging, seat reservations, invoice generation, attendance tracking, and comprehensive reporting features.
                    </div>
                </div>

                <div class="faq-item" data-keywords="data backup security privacy">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>How secure is my data?</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        We use industry-standard encryption and security measures to protect your data. Regular backups are performed, and we comply with data protection regulations.
                    </div>
                </div>

                <div class="faq-item" data-keywords="mobile app download android ios">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>Is there a mobile app available?</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        Yes, we have a mobile app available for Android devices. You can download it from the "Download App" section in your dashboard.
                    </div>
                </div>
            </div>
        </div>

        <!-- Support Ticket Section -->
        <div class="ticket-section">
            <div class="section-title">
                <i class="fas fa-ticket-alt"></i>
                Submit Support Ticket
            </div>

            <form method="POST" id="ticketForm" novalidate>
                {% csrf_token %}

                <div class="form-group">
                    <label for="issueType" class="form-label">Issue Type</label>
                    <select class="form-select" id="issueType" name="issueType" required>
                        <option value="">Choose an option</option>
                        <option value="frontend">Frontend Issue</option>
                        <option value="backend">Backend Issue</option>
                        <option value="fullstack">General System Issue</option>
                        <option value="design">Design/UI Issue</option>
                        <option value="devops">Performance Issue</option>
                        <option value="billing">Billing/Payment</option>
                        <option value="account">Account Management</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="priority" class="form-label">Priority Level</label>
                    <select class="form-select" id="priority" name="priority" required>
                        <option value="">Select priority</option>
                        <option value="low">Low - General inquiry</option>
                        <option value="medium">Medium - Affects workflow</option>
                        <option value="high">High - System down/critical</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="title" class="form-label">Issue Title</label>
                    <textarea class="form-control" name="title" id="title" rows="2"
                              placeholder="Brief description of the issue" required maxlength="200"></textarea>
                    <small class="text-muted">Maximum 200 characters</small>
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">Detailed Description</label>
                    <textarea class="form-control" name="description" id="description" rows="5"
                              placeholder="Please provide detailed information about the issue, including steps to reproduce if applicable"
                              required maxlength="1000"></textarea>
                    <small class="text-muted">Maximum 1000 characters</small>
                </div>

                <button type="submit" class="btn-submit" id="submitBtn">
                    <i class="fas fa-paper-plane"></i>
                    Submit Ticket
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced Help & Support System
    class HelpManager {
        constructor() {
            this.form = document.getElementById('ticketForm');
            this.submitBtn = document.getElementById('submitBtn');
            this.searchInput = document.getElementById('faqSearch');
            this.faqList = document.getElementById('faqList');
            this.init();
        }

        init() {
            this.setupFormValidation();
            this.setupFormSubmission();
            this.setupFaqSearch();
            this.setupCharacterCounters();
        }

        setupFormValidation() {
            const inputs = this.form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearErrors(input));
            });
        }

        validateField(field) {
            const value = field.value.trim();
            let isValid = true;
            let errorMessage = '';

            // Required field validation
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                errorMessage = 'This field is required.';
            }

            // Title validation
            if (field.name === 'title' && value) {
                if (value.length < 5) {
                    isValid = false;
                    errorMessage = 'Title must be at least 5 characters long.';
                }
            }

            // Description validation
            if (field.name === 'description' && value) {
                if (value.length < 20) {
                    isValid = false;
                    errorMessage = 'Description must be at least 20 characters long.';
                }
            }

            this.showFieldError(field, isValid ? '' : errorMessage);
            return isValid;
        }

        showFieldError(field, message) {
            const existingError = field.parentNode.querySelector('.invalid-feedback');
            if (existingError) {
                existingError.remove();
            }

            if (message) {
                field.classList.add('is-invalid');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.style.display = 'block';
                errorDiv.style.fontSize = '0.875rem';
                errorDiv.style.color = '#ef4444';
                errorDiv.style.marginTop = '0.25rem';
                errorDiv.textContent = message;
                field.parentNode.appendChild(errorDiv);
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        }

        clearErrors(field) {
            field.classList.remove('is-invalid', 'is-valid');
            const errorDiv = field.parentNode.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        }

        setupCharacterCounters() {
            const titleTextarea = document.getElementById('title');
            const descriptionTextarea = document.getElementById('description');

            this.addCharacterCounter(titleTextarea, 200);
            this.addCharacterCounter(descriptionTextarea, 1000);
        }

        addCharacterCounter(textarea, maxLength) {
            const counterDiv = document.createElement('div');
            counterDiv.className = 'character-counter';
            counterDiv.style.cssText = 'text-align: right; font-size: 0.875rem; color: #6b7280; margin-top: 0.25rem;';
            counterDiv.innerHTML = `<span class="char-count">0</span>/${maxLength} characters`;

            textarea.parentNode.appendChild(counterDiv);

            textarea.addEventListener('input', () => {
                const currentLength = textarea.value.length;
                const charCountSpan = counterDiv.querySelector('.char-count');
                charCountSpan.textContent = currentLength;

                if (currentLength > maxLength * 0.9) {
                    counterDiv.style.color = '#ef4444';
                } else if (currentLength > maxLength * 0.7) {
                    counterDiv.style.color = '#f59e0b';
                } else {
                    counterDiv.style.color = '#6b7280';
                }
            });
        }

        setupFaqSearch() {
            this.searchInput.addEventListener('input', (e) => {
                this.filterFaqs(e.target.value);
            });
        }

        filterFaqs(searchTerm) {
            const faqItems = this.faqList.querySelectorAll('.faq-item');
            const term = searchTerm.toLowerCase();

            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question span').textContent.toLowerCase();
                const answer = item.querySelector('.faq-answer').textContent.toLowerCase();
                const keywords = item.dataset.keywords.toLowerCase();

                const matches = question.includes(term) || answer.includes(term) || keywords.includes(term);
                item.style.display = matches ? 'block' : 'none';
            });
        }

        setupFormSubmission() {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();

                // Validate all fields
                const inputs = this.form.querySelectorAll('input[required], textarea[required], select[required]');
                let isFormValid = true;

                inputs.forEach(input => {
                    if (!this.validateField(input)) {
                        isFormValid = false;
                    }
                });

                if (isFormValid) {
                    this.submitForm();
                } else {
                    this.showNotification('Please fix the errors before submitting.', 'error');
                    const firstError = this.form.querySelector('.is-invalid');
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }
            });
        }

        submitForm() {
            const originalText = this.submitBtn.innerHTML;
            this.submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
            this.submitBtn.disabled = true;

            // Submit the form
            this.form.submit();
        }

        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    }

    // Global FAQ toggle function
    function toggleFaq(element) {
        const faqItem = element.parentNode;
        const answer = faqItem.querySelector('.faq-answer');
        const icon = element.querySelector('i');

        // Close all other FAQs
        document.querySelectorAll('.faq-item').forEach(item => {
            if (item !== faqItem) {
                const otherAnswer = item.querySelector('.faq-answer');
                const otherIcon = item.querySelector('.faq-question i');
                const otherQuestion = item.querySelector('.faq-question');

                otherAnswer.style.display = 'none';
                otherQuestion.classList.remove('active');
            }
        });

        // Toggle current FAQ
        if (answer.style.display === 'block') {
            answer.style.display = 'none';
            element.classList.remove('active');
        } else {
            answer.style.display = 'block';
            element.classList.add('active');
        }
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        window.helpManager = new HelpManager();
    });
</script>
{% endblock %}
