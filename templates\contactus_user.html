<!DOCTYPE html>
<html lang="en" translate="no">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Thank You - Contact Confirmation | Librainian</title>
    <meta name="robots" content="noindex, nofollow">
    <link rel="icon" href="/static/img/librainian-logo-black-transparent.png" type="image/x-icon">

    <style>

        /* Modern Thank You Email Template */
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #10b981;
            --success: #10b981;
            --warning: #f59e0b;
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-500: #6b7280;
            --gray-700: #374151;
            --gray-900: #111827;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--gray-900);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .thank-you-container {
            max-width: 500px;
            width: 100%;
            background: var(--white);
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .thank-you-header {
            background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
            padding: 3rem 2rem;
            text-align: center;
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .thank-you-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .success-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 3px solid rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 1;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .success-icon i {
            font-size: 2rem;
            color: var(--white);
        }

        .thank-you-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .thank-you-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .thank-you-content {
            padding: 2.5rem 2rem;
            text-align: center;
        }

        .greeting {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
        }

        .message-text {
            color: var(--gray-700);
            font-size: 1.125rem;
            line-height: 1.7;
            margin-bottom: 2rem;
        }

        .highlight-box {
            background: linear-gradient(135deg, var(--gray-50) 0%, #e0e7ff 100%);
            border-radius: 16px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid var(--primary);
            position: relative;
            overflow: hidden;
        }

        .highlight-box::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
        }

        .highlight-text {
            color: var(--primary);
            font-weight: 600;
            font-size: 1.125rem;
            position: relative;
            z-index: 1;
        }

        .signature {
            color: var(--gray-700);
            font-size: 1.125rem;
            margin-top: 2rem;
        }

        .signature strong {
            color: var(--gray-900);
            font-weight: 700;
        }

        .thank-you-footer {
            background: var(--gray-100);
            padding: 1.5rem 2rem;
            text-align: center;
            border-top: 1px solid var(--gray-200);
        }

        .footer-logo {
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary);
            margin-bottom: 0.5rem;
            letter-spacing: 1px;
        }

        .footer-text {
            color: var(--gray-500);
            font-size: 0.875rem;
        }

        .footer-text a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .footer-text a:hover {
            text-decoration: underline;
        }

        /* Responsive Design */
        @media (max-width: 640px) {
            body {
                padding: 1rem 0.5rem;
            }

            .thank-you-container {
                border-radius: 20px;
            }

            .thank-you-header {
                padding: 2rem 1.5rem;
            }

            .thank-you-content {
                padding: 2rem 1.5rem;
            }

            .thank-you-title {
                font-size: 1.75rem;
            }

            .thank-you-subtitle {
                font-size: 1rem;
            }

            .greeting {
                font-size: 1.125rem;
            }

            .message-text {
                font-size: 1rem;
            }

            .success-icon {
                width: 70px;
                height: 70px;
            }

            .success-icon i {
                font-size: 1.75rem;
            }
        }
    </style>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
</head>

<body>
    <div class="thank-you-container">
        <!-- Thank You Header -->
        <div class="thank-you-header">
            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>
            <h1 class="thank-you-title">Thank You!</h1>
            <p class="thank-you-subtitle">Your message has been received</p>
        </div>

        <!-- Thank You Content -->
        <div class="thank-you-content">
            <div class="greeting">
                Hello, {{ name }}!
            </div>

            <div class="message-text">
                Thank you so much for reaching out to us. We have received your message and truly appreciate you taking the time to contact us.
            </div>

            <div class="highlight-box">
                <div class="highlight-text">
                    <i class="fas fa-clock me-2"></i>
                    We will get back to you shortly with a response to your inquiry.
                </div>
            </div>

            <div class="signature">
                Best regards,<br>
                <strong>The Librainian Team</strong>
            </div>
        </div>

        <!-- Thank You Footer -->
        <div class="thank-you-footer">
            <div class="footer-logo">LIBRAINIAN</div>
            <div class="footer-text">
                If you no longer wish to receive emails from us, you can
                <a href="#">unsubscribe here</a>.
            </div>
        </div>
    </div>
</body>
</html>
