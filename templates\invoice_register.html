{% extends "base.html" %}

{% block title %}Invoice Register - Librainian{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">

<style>
    .invoice-register-container {
        min-height: 100vh;
        padding: 2rem 0;
    }

    /* Mobile Controls */
    .mobile-controls {
        margin-bottom: 1rem;
    }

    .mobile-search {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    .mobile-search::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }

    .mobile-search:focus {
        outline: none;
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }

    /* Mobile Filter and Sort Controls */
    .mobile-filter,
    .mobile-sort {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    .mobile-filter:focus,
    .mobile-sort:focus {
        outline: none;
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }

    .mobile-filter option,
    .mobile-sort option {
        background: #1a1a1a;
        color: white;
    }

    /* Mobile Results Info */
    .mobile-results-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .mobile-clear-filters {
        background: rgba(239, 68, 68, 0.2);
        border: 1px solid rgba(239, 68, 68, 0.3);
        color: #ef4444;
        padding: 0.25rem 0.75rem;
        border-radius: 6px;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .mobile-clear-filters:hover {
        background: rgba(239, 68, 68, 0.3);
        border-color: #ef4444;
    }

    /* Mobile Pagination */
    .mobile-pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
    }

    .mobile-page-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .mobile-page-btn:hover {
        background: rgba(99, 102, 241, 0.3);
        border-color: #6366f1;
    }

    .mobile-page-btn.active {
        background: linear-gradient(135deg, #6366f1, #8b5cf6);
        border-color: #6366f1;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
    }

    .mobile-page-btn:disabled {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.4);
        cursor: not-allowed;
    }

    .mobile-page-info {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.875rem;
        margin: 0 0.5rem;
    }

    /* Invoice Cards */
    .invoice-card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1rem;
        margin-bottom: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .invoice-card:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .mobile-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .invoice-info-mobile h6 {
        color: white;
        margin: 0;
        font-weight: 600;
    }

    .student-name-mobile {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-size: 0.875rem;
    }

    .mobile-field {
        margin-bottom: 0.5rem;
    }

    .mobile-field label {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.75rem;
        font-weight: 500;
        display: block;
        margin-bottom: 0.25rem;
    }

    .mobile-value {
        color: white;
        font-size: 0.875rem;
        font-weight: 500;
    }

    /* Status Badges */
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .status-paid {
        background: rgba(34, 197, 94, 0.2);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.3);
    }

    .status-pending {
        background: rgba(251, 191, 36, 0.2);
        color: #fbbf24;
        border: 1px solid rgba(251, 191, 36, 0.3);
    }

    .status-overdue {
        background: rgba(239, 68, 68, 0.2);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.3);
    }

    /* Action Buttons */
    .action-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.5rem;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .action-btn:hover {
        background: rgba(59, 130, 246, 0.3);
        border-color: #3b82f6;
        color: white;
    }

    .btn-action-view {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.5rem;
        border-radius: 0.5rem;
        margin: 0 0.25rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-action-view:hover {
        background: rgba(59, 130, 246, 0.3);
        border-color: #3b82f6;
    }

    /* Table Styling */
    .table {
        color: white !important;
        margin-bottom: 0;
    }

    .table thead th {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        color: white !important;
        font-weight: 600;
        padding: 1rem 0.75rem;
    }

    .table tbody td {
        border: none;
        padding: 0.875rem 0.75rem;
        background: rgba(255, 255, 255, 0.05);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: white !important;
    }

    .table tbody tr:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .table tbody tr:hover td {
        color: white !important;
    }

    .table-row-clickable {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .desktop-table-container {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    .table-header h5 {
        color: white;
        margin-bottom: 1rem;
    }

    .amount-display {
        font-weight: 600;
    }

    .currency {
        color: rgba(255, 255, 255, 0.7);
        margin-right: 0.25rem;
    }

    .amount {
        color: white;
    }

    .student-info {
        display: flex;
        flex-direction: column;
    }

    .student-name {
        color: white;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .student-id {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.875rem;
    }

    .empty-state {
        text-align: center;
        color: rgba(255, 255, 255, 0.7);
        padding: 2rem;
    }

    .empty-state i {
        opacity: 0.5;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: white;
        margin-bottom: 0.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .desktop-table-container {
            display: none;
        }

        .invoice-register-container {
            padding: 1rem 0;
        }
    }

    /* Desktop Table Styles */
    .desktop-table-container {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .table {
        color: white !important;
        margin-bottom: 0;
    }

    .table thead th {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        color: white !important;
        font-weight: 600;
        padding: 1rem 0.75rem;
    }

    .table tbody td {
        border: none;
        padding: 0.875rem 0.75rem;
        background: rgba(255, 255, 255, 0.05);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: white !important;
    }

    .table tbody tr {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateX(5px);
    }

    .table tbody tr:hover td {
        color: white !important;
    }

    /* Force white text for all table elements */
    .table th,
    .table td,
    .table thead th,
    .table tbody td,
    .table tfoot td {
        color: white !important;
    }

    /* Mobile Card Styles */
    .mobile-cards-container {
        display: none;
    }

    .student-card, .invoice-card {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .student-card:hover, .invoice-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        background: rgba(255, 255, 255, 0.15);
    }

    .student-card-header, .invoice-card-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .student-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        margin-right: 1rem;
    }

    .invoice-icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        margin-right: 1rem;
    }

    .card-info h6 {
        color: white;
        margin: 0;
        font-weight: 600;
    }

    .card-info p {
        color: rgba(255, 255, 255, 0.7);
        margin: 0;
        font-size: 0.9rem;
    }

    .card-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .detail-item {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.85rem;
    }

    .detail-label {
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
    }

    /* Back Button */
    .back-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .back-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        text-decoration: none;
        transform: translateX(-3px);
    }

    /* Status Badges */
    .status-paid {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-pending {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-overdue {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    /* Mobile Controls */
    .mobile-controls {
        display: none;
        margin-bottom: 1rem;
    }

    .mobile-search {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        color: white;
        width: 100%;
        margin-bottom: 1rem;
    }

    .mobile-search::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    /* DataTables Styling */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        color: white;
    }

    .dataTables_wrapper .dataTables_filter input {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        color: white;
        padding: 0.5rem;
    }

    .dataTables_wrapper .dataTables_length select {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        color: white;
        padding: 0.25rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .desktop-table-container {
            display: none;
        }

        .mobile-cards-container,
        .mobile-controls {
            display: block;
        }

        .invoice-register-container {
            padding: 1rem;
        }

        .card-details {
            grid-template-columns: 1fr;
        }
    }

    /* Dark mode adjustments */
    [data-theme="dark"] .desktop-table-container,
    [data-theme="dark"] .student-card,
    [data-theme="dark"] .invoice-card {
        background: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.1);
    }

    [data-theme="dark"] .table thead th {
        background: rgba(0, 0, 0, 0.3);
        color: white !important;
    }

    [data-theme="dark"] .table tbody td {
        background: rgba(0, 0, 0, 0.1);
        color: white !important;
    }

    [data-theme="dark"] .table th,
    [data-theme="dark"] .table td,
    [data-theme="dark"] .table thead th,
    [data-theme="dark"] .table tbody td,
    [data-theme="dark"] .table tfoot td {
        color: white !important;
    }

    /* DataTables Pagination Styling */
    .dataTables_wrapper .dataTables_paginate {
        margin-top: 1rem;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        border-radius: 8px !important;
        margin: 0 2px !important;
        padding: 8px 16px !important;
        transition: all 0.3s ease !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: rgba(168, 85, 247, 0.3) !important;
        border-color: rgba(168, 85, 247, 0.5) !important;
        color: white !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3) !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: linear-gradient(135deg, #a855f7, #ec4899) !important;
        border-color: #a855f7 !important;
        color: white !important;
        box-shadow: 0 4px 12px rgba(168, 85, 247, 0.4) !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: rgba(255, 255, 255, 0.05) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.4) !important;
        cursor: not-allowed !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
        background: rgba(255, 255, 255, 0.05) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.4) !important;
        transform: none !important;
        box-shadow: none !important;
    }

    /* DataTables Info and Length Styling */
    .dataTables_wrapper .dataTables_info {
        color: rgba(255, 255, 255, 0.8) !important;
        margin-top: 1rem;
    }

    .dataTables_wrapper .dataTables_length {
        color: rgba(255, 255, 255, 0.8) !important;
        margin-bottom: 1rem;
    }

    .dataTables_wrapper .dataTables_length select {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        border-radius: 8px !important;
        padding: 4px 8px !important;
    }

    .dataTables_wrapper .dataTables_filter {
        color: rgba(255, 255, 255, 0.8) !important;
        margin-bottom: 1rem;
    }

    .dataTables_wrapper .dataTables_filter input {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        border-radius: 8px !important;
        padding: 8px 12px !important;
    }

    .dataTables_wrapper .dataTables_filter input::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="invoice-register-container">
    <div class="container-fluid">
        <!-- Mobile Controls -->
        <div class="mobile-controls d-md-none mb-3">
            <div class="row g-2 mb-3">
                <div class="col-12">
                    <input type="text" class="mobile-search" placeholder="Search invoices..." id="mobileSearchInput">
                </div>
            </div>

            <!-- Mobile Filter and Sort Controls -->
            <div class="row g-2 mb-3">
                <div class="col-6">
                    <select class="mobile-filter" id="mobileStatusFilter">
                        <option value="">All Status</option>
                        <option value="paid">Paid</option>
                        <option value="pending">Pending</option>
                        <option value="overdue">Overdue</option>
                    </select>
                </div>
                <div class="col-6">
                    <select class="mobile-sort" id="mobileSortSelect">
                        <option value="date-desc">Newest First</option>
                        <option value="date-asc">Oldest First</option>
                        <option value="amount-desc">Amount (High-Low)</option>
                        <option value="amount-asc">Amount (Low-High)</option>
                        <option value="student-asc">Student (A-Z)</option>
                        <option value="student-desc">Student (Z-A)</option>
                    </select>
                </div>
            </div>

            <!-- Mobile Results Info -->
            <div class="mobile-results-info">
                <span id="mobileResultsCount">Showing all invoices</span>
                <button class="mobile-clear-filters" id="mobileClearFilters" style="display: none;">
                    <i class="fas fa-times"></i> Clear Filters
                </button>
            </div>
        </div>

        <!-- Desktop Table View -->
        <div class="desktop-table-container">
            <div class="table-header mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    All Invoices ({{ total_invoices|default:0 }})
                </h5>
            </div>
            <table class="table table-hover" id="invoicesTable">
                <thead>
                    <tr>
                        <th>Invoice ID</th>
                        <th>Student</th>
                        <th>Course</th>
                        <th>Issue Date</th>
                        <th>Due Date</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in all_invoices %}
                    <tr class="table-row-clickable" onclick="window.location.href='/students/invoice_student/{{ invoice.slug }}'">
                        <td>{{ invoice.invoice_id|default:invoice.id }}</td>
                        <td>
                            <div class="student-info">
                                <div class="student-name">{{ invoice.student.name }}</div>
                                <div class="student-id">ID: {{ invoice.student.student_id|default:invoice.student.id }}</div>
                            </div>
                        </td>
                        <td>{{ invoice.student.course.name|default:"N/A" }}</td>
                        <td>{{ invoice.issue_date|date:"M d, Y" }}</td>
                        <td>{{ invoice.due_date|date:"M d, Y" }}</td>
                        <td>
                            <div class="amount-display">
                                <span class="currency">₹</span>
                                <span class="amount">{{ invoice.total_amount }}</span>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-{{ invoice.payment_status|lower|cut:' ' }}">
                                <i class="fas fa-{% if invoice.payment_status == 'Paid' %}check-circle{% elif invoice.payment_status == 'Overdue' %}exclamation-triangle{% else %}clock{% endif %} me-1"></i>
                                {{ invoice.payment_status }}
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-action-view" onclick="event.stopPropagation(); viewInvoice('{{ invoice.slug }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="empty-state">
                                <i class="fas fa-file-invoice fa-3x mb-3"></i>
                                <h5>No Invoices Found</h5>
                                <p class="text-muted">There are no invoices to display at the moment.</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Mobile Cards View -->
        <div class="mobile-cards-container d-md-none" id="mobileCardsContainer">
            {% for invoice in all_invoices %}
            <div class="invoice-card"
                 data-search="{{ invoice.student_name|lower }} {{ invoice.student_id|lower }} {{ invoice.id|lower }} {{ invoice.status|lower }}"
                 data-status="{{ invoice.status|lower }}"
                 data-amount="{{ invoice.total_amount }}"
                 data-date="{{ invoice.issue_date|date:'Y-m-d' }}"
                 onclick="window.location.href='/students/invoice_student/{{ invoice.slug }}'">
                <div class="mobile-card-header">
                    <div class="invoice-info-mobile">
                        <h6 class="invoice-id-mobile">Invoice #{{ invoice.id }}</h6>
                        <p class="student-name-mobile">{{ invoice.student_name }}</p>
                    </div>
                    <div class="mobile-actions-header">
                        <button class="action-btn view-btn" onclick="event.stopPropagation(); viewInvoice('{{ invoice.slug }}')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="mobile-card-body">
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="mobile-field">
                                <label>Student ID</label>
                                <div class="mobile-value">{{ invoice.student_id }}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mobile-field">
                                <label>Course</label>
                                <div class="mobile-value">{{ invoice.student_course }}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mobile-field">
                                <label>Issue Date</label>
                                <div class="mobile-value">{{ invoice.issue_date|date:"M d, Y" }}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mobile-field">
                                <label>Due Date</label>
                                <div class="mobile-value">{{ invoice.due_date|date:"M d, Y" }}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mobile-field">
                                <label>Amount</label>
                                <div class="mobile-value amount-display">
                                    <span class="currency">₹</span>
                                    <span class="amount">{{ invoice.total_amount }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mobile-field">
                                <label>Status</label>
                                <div class="mobile-value">
                                    <span class="status-badge status-{{ invoice.status }}">
                                        <i class="fas fa-{% if invoice.status == 'paid' %}check-circle{% elif invoice.status == 'overdue' %}exclamation-triangle{% else %}clock{% endif %} me-1"></i>
                                        {{ invoice.status|title }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="text-center" style="color: rgba(255, 255, 255, 0.7); padding: 2rem;">
                <i class="fas fa-file-invoice fa-3x mb-3" style="opacity: 0.5;"></i>
                <h5>No Invoices Found</h5>
                <p>There are no invoices to display at the moment.</p>
            </div>
            {% endfor %}
        </div>

        <!-- Mobile Pagination -->
        <div class="mobile-pagination d-md-none" id="mobilePagination" style="display: none;">
            <button class="mobile-page-btn" id="prevBtn">
                <i class="fas fa-chevron-left"></i> Previous
            </button>
            <span class="mobile-page-info" id="pageInfo">Page 1 of 1</span>
            <button class="mobile-page-btn" id="nextBtn">
                Next <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

<script>
    let invoicesTable;

    document.addEventListener('DOMContentLoaded', function() {
        // Initialize invoices DataTable
        invoicesTable = $('#invoicesTable').DataTable({
            responsive: true,
            pageLength: 25,
            language: {
                search: "Search invoices:",
                lengthMenu: "Show _MENU_ invoices per page",
                info: "Showing _START_ to _END_ of _TOTAL_ invoices"
            },
            columnDefs: [
                { responsivePriority: 1, targets: 0 }, // Invoice ID
                { responsivePriority: 2, targets: 1 }, // Student
                { responsivePriority: 3, targets: 6 }, // Status
                { responsivePriority: 4, targets: 7 }  // Actions
            ],
            order: [[3, 'desc']] // Sort by issue date descending
        });

        // Mobile controls
        const mobileSearchInput = document.getElementById('mobileSearchInput');
        const mobileStatusFilter = document.getElementById('mobileStatusFilter');
        const mobileSortSelect = document.getElementById('mobileSortSelect');
        const mobileResultsCount = document.getElementById('mobileResultsCount');
        const mobileClearFilters = document.getElementById('mobileClearFilters');
        const mobilePagination = document.getElementById('mobilePagination');

        let invoiceCards = document.querySelectorAll('.invoice-card');
        let filteredCards = Array.from(invoiceCards);
        let currentPage = 1;
        const cardsPerPage = 10;

        // Mobile search functionality
        if (mobileSearchInput) {
            mobileSearchInput.addEventListener('input', function() {
                applyMobileFilters();
            });
        }

        // Mobile status filter
        if (mobileStatusFilter) {
            mobileStatusFilter.addEventListener('change', function() {
                applyMobileFilters();
            });
        }

        // Mobile sort
        if (mobileSortSelect) {
            mobileSortSelect.addEventListener('change', function() {
                applyMobileFilters();
            });
        }

        // Clear filters
        if (mobileClearFilters) {
            mobileClearFilters.addEventListener('click', function() {
                mobileSearchInput.value = '';
                mobileStatusFilter.value = '';
                mobileSortSelect.value = 'date-desc';
                applyMobileFilters();
            });
        }

        // Apply mobile filters function
        function applyMobileFilters() {
            const searchTerm = mobileSearchInput.value.toLowerCase();
            const statusFilter = mobileStatusFilter.value.toLowerCase();
            const sortOption = mobileSortSelect.value;

            // Filter cards
            filteredCards = Array.from(invoiceCards).filter(card => {
                const searchData = card.getAttribute('data-search');
                const statusData = card.getAttribute('data-status') || '';

                const matchesSearch = !searchTerm || (searchData && searchData.includes(searchTerm));
                const matchesStatus = !statusFilter || statusData.toLowerCase() === statusFilter;

                return matchesSearch && matchesStatus;
            });

            // Sort cards
            filteredCards.sort((a, b) => {
                const aAmount = parseFloat(a.getAttribute('data-amount')) || 0;
                const bAmount = parseFloat(b.getAttribute('data-amount')) || 0;
                const aDate = a.getAttribute('data-date') || '';
                const bDate = b.getAttribute('data-date') || '';
                const aStudent = a.querySelector('.student-name-mobile').textContent.trim();
                const bStudent = b.querySelector('.student-name-mobile').textContent.trim();

                switch (sortOption) {
                    case 'date-asc':
                        return new Date(aDate) - new Date(bDate);
                    case 'amount-desc':
                        return bAmount - aAmount;
                    case 'amount-asc':
                        return aAmount - bAmount;
                    case 'student-asc':
                        return aStudent.localeCompare(bStudent);
                    case 'student-desc':
                        return bStudent.localeCompare(aStudent);
                    default: // date-desc
                        return new Date(bDate) - new Date(aDate);
                }
            });

            // Update results count
            const totalInvoices = invoiceCards.length;
            const filteredCount = filteredCards.length;

            if (searchTerm || statusFilter) {
                mobileResultsCount.textContent = `Showing ${filteredCount} of ${totalInvoices} invoices`;
                mobileClearFilters.style.display = 'inline-block';
            } else {
                mobileResultsCount.textContent = `Showing all ${totalInvoices} invoices`;
                mobileClearFilters.style.display = 'none';
            }

            currentPage = 1;
            updateMobilePagination();
        }

        // Update mobile pagination
        function updateMobilePagination() {
            const totalPages = Math.ceil(filteredCards.length / cardsPerPage);
            const startIndex = (currentPage - 1) * cardsPerPage;
            const endIndex = startIndex + cardsPerPage;

            // Hide all cards first
            invoiceCards.forEach(card => card.style.display = 'none');

            // Show filtered cards for current page
            filteredCards.slice(startIndex, endIndex).forEach(card => {
                card.style.display = 'block';
                card.style.animation = 'fadeIn 0.3s ease';
            });

            // Update pagination controls
            if (totalPages > 1) {
                mobilePagination.style.display = 'flex';
                const pageInfo = document.getElementById('pageInfo');
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');

                pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;

                prevBtn.disabled = currentPage === 1;
                nextBtn.disabled = currentPage === totalPages;
            } else {
                mobilePagination.style.display = 'none';
            }

            // Show/hide empty state
            const emptyState = document.querySelector('.mobile-cards-container .text-center');
            if (emptyState) {
                emptyState.style.display = filteredCards.length === 0 ? 'block' : 'none';
            }
        }

        // Mobile pagination controls
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        if (prevBtn) {
            prevBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage > 1) {
                    currentPage--;
                    updateMobilePagination();
                }
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', function(e) {
                e.preventDefault();
                const totalPages = Math.ceil(filteredCards.length / cardsPerPage);
                if (currentPage < totalPages) {
                    currentPage++;
                    updateMobilePagination();
                }
            });
        }

        // Initialize mobile view
        if (window.innerWidth <= 768) {
            applyMobileFilters();
        }

        // Entrance animations
        const tableContainer = document.querySelector('.desktop-table-container');
        if (tableContainer) {
            tableContainer.style.opacity = '0';
            tableContainer.style.transform = 'translateY(20px)';

            setTimeout(() => {
                tableContainer.style.transition = 'all 0.5s ease';
                tableContainer.style.opacity = '1';
                tableContainer.style.transform = 'translateY(0)';
            }, 100);
        }
    });





    function viewInvoice(invoiceSlug) {
        window.location.href = `/students/invoice_student/${invoiceSlug}/`;
    }
</script>
{% endblock %}
