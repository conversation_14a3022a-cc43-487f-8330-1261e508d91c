{% extends "base.html" %}

{% block title %}Edit Sublibrarian - Librainian{% endblock %}

{% block page_title %}Edit Sublibrarian{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="/{{ role }}/sublibrarians/">Sublibrarians</a></li>
<li class="breadcrumb-item active" aria-current="page">Edit Sublibrarian</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Edit Sublibrarian Styles */
    .sublibrarian-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    .sublibrarian-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 2rem;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .sublibrarian-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .sublibrarian-header h1 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 1;
    }

    .sublibrarian-header p {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .user-info-card {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border-radius: 16px;
        padding: 1.5rem;
        color: white;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .user-info-title {
        font-size: 1.25rem;
        font-weight: 700;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .user-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .user-info-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .info-label {
        font-size: 0.875rem;
        opacity: 0.8;
        margin-bottom: 0.25rem;
        font-weight: 500;
    }

    .info-value {
        font-size: 1rem;
        font-weight: 600;
    }

    .form-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid rgba(99, 102, 241, 0.2);
    }

    .section-title i {
        color: #6366f1;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        display: block;
    }

    .required-label::after {
        content: " *";
        color: #ef4444;
        font-size: 0.875rem;
        font-weight: 700;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(156, 163, 175, 0.3);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        width: 100%;
    }

    .form-control:focus {
        outline: none;
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        background: rgba(255, 255, 255, 1);
    }

    .input-group {
        position: relative;
        display: flex;
        align-items: center;
    }

    .input-group-text {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6b7280;
        font-size: 1rem;
        z-index: 2;
        background: none;
        border: none;
        pointer-events: none;
    }

    .input-group .form-control {
        padding-left: 2.5rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border: none;
        border-radius: 12px;
        padding: 0.875rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: center;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        border: none;
        border-radius: 12px;
        padding: 0.875rem 2rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
    }

    .btn-secondary:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid rgba(156, 163, 175, 0.2);
    }

    .help-text {
        font-size: 0.875rem;
        color: #6b7280;
        margin-top: 0.25rem;
    }

    .invalid-feedback {
        display: block;
        font-size: 0.875rem;
        color: #ef4444;
        margin-top: 0.25rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .sublibrarian-container,
        .form-section {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .sublibrarian-header {
            padding: 1.5rem 1rem;
        }

        .sublibrarian-header h1 {
            font-size: 1.5rem;
        }

        .form-row {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .form-actions {
            flex-direction: column-reverse;
            gap: 1rem;
        }

        .btn-primary,
        .btn-secondary {
            width: 100%;
            justify-content: center;
        }

        .user-info-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Animation */
    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="sublibrarian-container fade-in">
    <!-- Sublibrarian Header -->
    <div class="sublibrarian-header">
        <h1><i class="fas fa-user-edit me-2"></i>Edit Sublibrarian</h1>
        <p>Update sublibrarian information and contact details</p>
    </div>

    <!-- Current User Information -->
    <div class="user-info-card">
        <div class="user-info-title">
            <i class="fas fa-user-circle"></i>
            Current Sublibrarian Information
        </div>
        <div class="user-info-grid">
            <div class="user-info-item">
                <div class="info-label">Full Name</div>
                <div class="info-value">{{ sub.user.first_name }} {{ sub.user.last_name }}</div>
            </div>
            <div class="user-info-item">
                <div class="info-label">Username</div>
                <div class="info-value">{{ sub.user.username }}</div>
            </div>
            <div class="user-info-item">
                <div class="info-label">Email</div>
                <div class="info-value">{{ sub.user.email }}</div>
            </div>
            <div class="user-info-item">
                <div class="info-label">Phone</div>
                <div class="info-value">{{ sub.sublibrarian_phone_num|default:"Not provided" }}</div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    {% endif %}

    <form method="POST" id="sublibrarianForm" novalidate>
        {% csrf_token %}

        <!-- Personal Information Section -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-user"></i>
                Personal Information
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="first_name" class="form-label required-label">First Name</label>
                    <div class="input-group">
                        <i class="fas fa-user input-group-text"></i>
                        <input type="text" name="first_name" id="first_name" class="form-control"
                               value="{{ sub.user.first_name }}" required maxlength="30">
                    </div>
                    <div class="help-text">Enter the sublibrarian's first name</div>
                </div>

                <div class="form-group">
                    <label for="last_name" class="form-label required-label">Last Name</label>
                    <div class="input-group">
                        <i class="fas fa-user input-group-text"></i>
                        <input type="text" name="last_name" id="last_name" class="form-control"
                               value="{{ sub.user.last_name }}" required maxlength="30">
                    </div>
                    <div class="help-text">Enter the sublibrarian's last name</div>
                </div>
            </div>

            <div class="form-group">
                <label for="email" class="form-label required-label">Email Address</label>
                <div class="input-group">
                    <i class="fas fa-envelope input-group-text"></i>
                    <input type="email" name="email" id="email" class="form-control"
                           value="{{ sub.user.email }}" required>
                </div>
                <div class="help-text">Email address for notifications and login</div>
            </div>
        </div>

        <!-- Contact Information Section -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-address-book"></i>
                Contact Information
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="phone" class="form-label required-label">Phone Number</label>
                    <div class="input-group">
                        <i class="fas fa-phone input-group-text"></i>
                        <input type="tel" name="phone" id="phone" class="form-control"
                               value="{{ sub.sublibrarian_phone_num }}" required pattern="[0-9]{10}">
                    </div>
                    <div class="help-text">10-digit phone number for contact</div>
                </div>

                <div class="form-group">
                    <label for="address" class="form-label required-label">Address</label>
                    <div class="input-group">
                        <i class="fas fa-map-marker-alt input-group-text"></i>
                        <textarea name="address" id="address" class="form-control" rows="3"
                                  required>{{ sub.sublibrarian_address }}</textarea>
                    </div>
                    <div class="help-text">Complete residential address</div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <a href="javascript:history.back()" class="btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Cancel
            </a>
            <button type="submit" class="btn-primary" id="submitBtn">
                <i class="fas fa-save"></i>
                Save Changes
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced Sublibrarian Edit Form
    class SublibrarianEditor {
        constructor() {
            this.form = document.getElementById('sublibrarianForm');
            this.submitBtn = document.getElementById('submitBtn');
            this.init();
        }

        init() {
            this.setupValidation();
            this.setupFormSubmission();
        }

        setupValidation() {
            // Real-time validation
            const inputs = this.form.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearErrors(input));
            });

            // Phone number formatting
            const phoneInput = this.form.querySelector('input[type="tel"]');
            if (phoneInput) {
                phoneInput.addEventListener('input', (e) => this.formatPhoneNumber(e.target));
            }
        }

        validateField(field) {
            const value = field.value.trim();
            let isValid = true;
            let errorMessage = '';

            // Required field validation
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                errorMessage = 'This field is required.';
            }

            // Email validation
            if (field.type === 'email' && value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid email address.';
                }
            }

            // Phone validation
            if (field.type === 'tel' && value) {
                const phoneRegex = /^[0-9]{10}$/;
                if (!phoneRegex.test(value.replace(/\D/g, ''))) {
                    isValid = false;
                    errorMessage = 'Please enter a valid 10-digit phone number.';
                }
            }

            // Name validation
            if ((field.name === 'first_name' || field.name === 'last_name') && value) {
                if (value.length < 2) {
                    isValid = false;
                    errorMessage = 'Name must be at least 2 characters long.';
                } else if (!/^[a-zA-Z\s]+$/.test(value)) {
                    isValid = false;
                    errorMessage = 'Name can only contain letters and spaces.';
                }
            }

            this.showFieldError(field, isValid ? '' : errorMessage);
            return isValid;
        }

        showFieldError(field, message) {
            // Remove existing error
            const existingError = field.parentNode.parentNode.querySelector('.invalid-feedback');
            if (existingError) {
                existingError.remove();
            }

            if (message) {
                field.classList.add('is-invalid');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = message;
                field.parentNode.parentNode.appendChild(errorDiv);
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        }

        clearErrors(field) {
            field.classList.remove('is-invalid', 'is-valid');
            const errorDiv = field.parentNode.parentNode.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        }

        formatPhoneNumber(input) {
            let value = input.value.replace(/\D/g, '');
            if (value.length > 10) {
                value = value.substring(0, 10);
            }
            input.value = value;
        }

        setupFormSubmission() {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();

                // Validate all fields
                const inputs = this.form.querySelectorAll('input[required], textarea[required]');
                let isFormValid = true;

                inputs.forEach(input => {
                    if (!this.validateField(input)) {
                        isFormValid = false;
                    }
                });

                if (isFormValid) {
                    this.submitForm();
                } else {
                    this.showNotification('Please fix the errors before submitting.', 'error');
                    // Scroll to first error
                    const firstError = this.form.querySelector('.is-invalid');
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }
            });
        }

        submitForm() {
            // Show loading state
            const originalText = this.submitBtn.innerHTML;
            this.submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
            this.submitBtn.disabled = true;

            // Submit the form
            this.form.submit();
        }

        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        window.sublibrarianEditor = new SublibrarianEditor();
    });
</script>
{% endblock %}
