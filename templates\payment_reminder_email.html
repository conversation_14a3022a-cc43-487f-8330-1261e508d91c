{% extends "email_base.html" %}

{% block email_title %}Payment Reminder - {{ library_name|default:'Librainian' }}{% endblock %}

{% block email_subject %}Payment Reminder - Invoice #{{ invoice.invoice_id }}{% endblock %}

{% block email_description %}Payment reminder for outstanding balance on library services subscription.{% endblock %}

{% block preview_text %}Friendly reminder: You have an outstanding balance of ₹{{ invoice.remaining_due }} on invoice #{{ invoice.invoice_id }}.{% endblock %}

{% block header_icon %}⏰{% endblock %}

{% block email_header_title %}Payment Reminder{% endblock %}

{% block extra_css %}
<style>
  body {
    font-family: Arial, sans-serif;
    background-color: #f9fafb;
    color: #333;
    margin: 0;
    padding: 20px;
  }

  .greeting {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .message {
    margin-bottom: 20px;
  }

  .reminder-alert {
    background-color: #fffbe6;
    border: 1px solid #facc15;
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
  }

  .payment-status,
  .payment-progress,
  .payment-history,
  .payment-options,
  .contact-section,
  .urgent-payment-action {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #e5e7eb;
    background-color: #ffffff;
    border-radius: 8px;
  }

  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .status-item,
  .breakdown-row,
  .history-item,
  .option-card,
  .contact-item {
    margin-bottom: 10px;
  }

  .highlight {
    color: #dc2626;
    font-weight: bold;
    font-size: 28px;
  }

  .card-value {
    font-size: 18px;
    font-weight: 600;
    margin: 5px 0;
  }

  .card-value.highlight {
    font-size: 32px;
    font-weight: 700;
    color: #dc2626;
    text-shadow: 1px 1px 2px rgba(220, 38, 38, 0.2);
  }

  /* Make pending payment amount bigger in alert message */
  .alert-message strong {
    font-size: 24px;
    color: #dc2626;
    background-color: #fef2f2;
    padding: 4px 8px;
    border-radius: 6px;
    border: 2px solid #fecaca;
  }

  /* Make pending payment amount bigger in pay now button */
  .pay-now-button {
    font-size: 18px;
    padding: 15px 25px;
  }

  .btn-primary-action,
  .btn-secondary-action,
  .pay-now-button {
    display: inline-block;
    padding: 10px 20px;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
  }

  .btn-primary-action {
    background-color: #dc2626;
  }

  .btn-secondary-action {
    background-color: #f59e0b;
  }

  .pay-now-button {
    background-color: #10b981;
  }

  .pay-now-button:hover,
  .btn-primary-action:hover,
  .btn-secondary-action:hover {
    opacity: 0.9;
  }

  @media only screen and (max-width: 600px) {
    .status-grid,
    .options-grid {
      display: block;
    }

    .btn-primary-action,
    .btn-secondary-action,
    .pay-now-button {
      display: block;
      width: 100%;
      text-align: center;
      margin-top: 10px;
    }
  }
</style>
{% endblock %}
 

{% block email_content %}
<h2 class="greeting">Hello {{ student.name }},</h2>
<p class="message">
    We hope you're enjoying our library services! This is a friendly reminder about your outstanding payment balance.
</p>

<!-- Reminder Alert -->
<div class="reminder-alert">
    <div class="alert-icon">
        <i class="fas fa-exclamation-triangle"></i>
    </div>
    <div class="alert-content">
        <h3 class="alert-title">Payment Reminder</h3>
        <p class="alert-message">
            Your invoice #{{ invoice.invoice_id }} has an outstanding balance of
            <strong>₹{{ invoice.remaining_due }}</strong> that was due {{ days_overdue }} days ago.
        </p>
    </div>
</div>

<!-- Payment Highlight Schema -->
<div class="payment-highlight-schema">
    <h3 class="schema-title">
        <i class="fas fa-clipboard-list"></i> Payment Summary
    </h3>

    <div class="highlight-grid">
        <div class="highlight-card pending-payment">
            <div class="card-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="card-content">
                <h4 class="card-title">Invoice Number</h4>
                <div class="card-value">#{{ invoice.invoice_id }}</div>
            </div>
            <div class="card-content">
                <h4 class="card-title">Due Date</h4>
                <div class="card-value">{{ invoice.due_date|date:"d M Y" }}</div>
                <div class="card-subtitle">
                    {% if days_overdue > 0 %}
                        <span class="overdue-text">{{ days_overdue }} days overdue</span>
                    {% else %}
                        Payment Due
                    {% endif %}
                </div>
            </div>
            <div class="card-content">
                <h4 class="card-title">Outstanding Balance</h4>
                <div class="card-value highlight">₹{{ invoice.remaining_due }}</div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Progress -->
<div class="payment-progress">
    <h3 class="section-title">📊 Payment Progress</h3>
    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" style="width: {{ invoice.get_payment_percentage }}%"></div>
        </div>
        <div class="progress-text">
            {{ invoice.get_payment_percentage }}% paid (₹{{ invoice.total_paid }} of ₹{{ invoice.total_amount }})
        </div>
    </div>
</div>

<!-- Payment History -->
{% if payments %}
<div class="payment-history">
    <h3 class="section-title">📋 Payment History</h3>
    <div class="history-list">
        {% for payment in payments %}
        <div class="history-item">
            <div class="history-date">{{ payment.payment_date|date:"M j, Y" }}</div>
            <div class="history-amount">₹{{ payment.amount_paid }}</div>
            <div class="history-mode">{{ payment.payment_mode }}</div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}
<!-- Payment Options -->
<div class="payment-options">
    <h3 class="section-title">💰 How to Pay</h3>
    <div class="options-grid">
        <div class="option-card">
            <div class="option-icon">💳</div>
            <h4>Online Payment</h4>
            <p>Pay securely online using UPI, cards, or net banking</p>
        </div>
        
        <div class="option-card">
            <div class="option-icon">🏦</div>
            <h4>Bank Transfer</h4>
            <p>Transfer directly to our bank account</p>
        </div>
        
        <div class="option-card">
            <div class="option-icon">🏢</div>
            <h4>Visit Library</h4>
            <p>Pay in person at our library counter</p>
        </div>
        
        <div class="option-card">
            <div class="option-icon">📱</div>
            <h4>UPI Payment</h4>
            <p>Quick payment using any UPI app</p>
        </div>
    </div>
</div>

<!-- Contact Information -->
<div class="contact-section">
    <h3 class="section-title">📞 Need Help?</h3>
    <p>If you have any questions about your payment or need assistance, please contact us:</p>
    <div class="contact-details">
        <div class="contact-item">
            <i class="fas fa-envelope"></i>
            <span><EMAIL></span>
        </div>
        <div class="contact-item">
            <i class="fas fa-phone"></i>
            <span>{{ librarian.librarian_phone_num }}</span>
        </div>
        <div class="contact-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>{{ library_name }}</span>
        </div>
    </div>
</div>

<!-- Action Button -->
<div class="action-section">
    <a href="#" class="pay-now-button">
        <i class="fas fa-credit-card"></i>
        Pay Now - ₹{{ invoice.remaining_due }}
    </a>
    <p class="action-note">Click above to make your payment securely online</p>
</div>

<p class="closing-message">
    Thank you for your prompt attention to this matter. We appreciate your business and look forward to continuing to serve you!
</p>
{% endblock %}


