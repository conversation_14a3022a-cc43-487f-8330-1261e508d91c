from django.core.management.base import BaseCommand
from django.contrib.sites.models import Site
from allauth.socialaccount.models import SocialApp
from django.conf import settings
import os


class Command(BaseCommand):
    help = 'Set up Google OAuth application for django-allauth'

    def add_arguments(self, parser):
        parser.add_argument(
            '--domain',
            type=str,
            help='Domain for the site (e.g., localhost:8001, librainian.com)',
        )
        parser.add_argument(
            '--production',
            action='store_true',
            help='Configure for production environment',
        )

    def handle(self, *args, **options):
        # Get Google OAuth credentials from environment
        client_id = os.getenv('GOOGLE_CLIENT_ID')
        client_secret = os.getenv('GOOGLE_SECRET')

        if not client_id or not client_secret:
            self.stdout.write(
                self.style.ERROR('GOOGLE_CLIENT_ID and GOOGLE_SECRET must be set in environment variables')
            )
            return

        # Determine the domain
        if options['domain']:
            domain = options['domain']
        elif options['production']:
            domain = 'librainian.com'
        else:
            domain = 'localhost:8001'

        # Get or create the default site
        site, created = Site.objects.get_or_create(
            id=1,
            defaults={
                'domain': domain,
                'name': 'Librainian'
            }
        )

        # Update the site domain if it's different
        if site.domain != domain:
            site.domain = domain
            site.save()
            self.stdout.write(
                self.style.SUCCESS(f'Updated site domain to: {site.domain}')
            )

        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created site: {site.domain}')
            )

        # Remove any existing Google apps to avoid conflicts
        existing_apps = SocialApp.objects.filter(provider='google')
        if existing_apps.exists():
            existing_apps.delete()
            self.stdout.write(
                self.style.WARNING('Removed existing Google OAuth apps to avoid conflicts')
            )

        # Create new Google app
        google_app = SocialApp.objects.create(
            provider='google',
            name='Google OAuth',
            client_id=client_id,
            secret=client_secret,
        )

        self.stdout.write(
            self.style.SUCCESS('Created new Google OAuth app')
        )

        # Add the site to the app
        google_app.sites.add(site)

        # Verify configuration
        self.stdout.write(
            self.style.SUCCESS(
                f'Google OAuth app configured successfully!\n'
                f'Client ID: {client_id}\n'
                f'Site: {site.domain}\n'
                f'Environment: {"Production" if options["production"] else "Development"}'
            )
        )

        # Show redirect URIs that need to be configured in Google Console
        redirect_uris = [
            f'http://{domain}/accounts/google/login/callback/' if not options['production'] else f'https://{domain}/accounts/google/login/callback/',
            f'http://{domain}/accounts/google/login/callback/' if domain.startswith('localhost') else f'https://{domain}/accounts/google/login/callback/',
        ]

        self.stdout.write(
            self.style.WARNING(
                f'\nIMPORTANT: Make sure these redirect URIs are configured in your Google OAuth app:\n'
                + '\n'.join(f'  - {uri}' for uri in set(redirect_uris))
            )
        )
