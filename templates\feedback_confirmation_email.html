{% extends "email_base.html" %}

{% block email_title %}Thank You for Your Feedback - Librainian{% endblock %}

{% block email_subject %}Thank You for Your Feedback{% endblock %}

{% block email_description %}Confirmation email thanking the user for providing feedback to <PERSON><PERSON><PERSON>.{% endblock %}

{% block preview_text %}Thank you {{ feedback.name }} for your feedback on "{{ feedback.subject }}". We appreciate your input.{% endblock %}

{% block header_icon %}💬{% endblock %}

{% block email_header_title %}Thank You for Your Feedback!{% endblock %}

{% block email_header_subtitle %}Your input helps us improve our services{% endblock %}

{% block email_styles %}
<style>
    .feedback-confirmation {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border-radius: 16px;
        padding: 25px;
        margin: 25px 0;
        border: 2px solid #22c55e;
        position: relative;
        overflow: hidden;
    }

    .feedback-confirmation::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(34, 197, 94, 0.1), transparent);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(30deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(30deg); }
    }

    .thank-you-icon {
        font-size: 48px;
        text-align: center;
        margin-bottom: 20px;
        position: relative;
        z-index: 1;
    }

    .confirmation-title {
        font-size: 20px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 20px 0;
        text-align: center;
        position: relative;
        z-index: 1;
    }

    .feedback-highlight {
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: 12px;
        padding: 20px;
        margin: 20px 0;
        position: relative;
        z-index: 1;
        border-left: 4px solid #22c55e;
    }

    .feedback-subject {
        font-weight: 600;
        color: #22c55e;
        font-size: 16px;
        margin-bottom: 10px;
    }

    .feedback-text {
        color: #2c3e50;
        font-size: 15px;
        line-height: 1.6;
        margin: 0;
    }

    .appreciation-section {
        background-color: #fef3c7;
        border: 1px solid #fbbf24;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        border-left: 4px solid #f59e0b;
    }

    .appreciation-title {
        font-size: 16px;
        font-weight: 600;
        color: #92400e;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
    }

    .appreciation-title::before {
        content: "⭐";
        margin-right: 8px;
        font-size: 18px;
    }

    .appreciation-text {
        font-size: 14px;
        color: #92400e;
        margin: 0;
        line-height: 1.5;
    }

    .signature-section {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid rgba(34, 197, 94, 0.2);
        position: relative;
        z-index: 1;
    }

    .team-name {
        font-weight: 600;
        color: #22c55e;
        font-size: 18px;
    }

    @media only screen and (max-width: 600px) {
        .feedback-confirmation {
            padding: 20px !important;
            margin: 20px 0 !important;
        }

        .feedback-highlight {
            padding: 15px !important;
            margin: 15px 0 !important;
        }
    }
</style>
{% endblock %}

{% block email_content %}
<h2 class="greeting">Dear {{ feedback.name }}!</h2>
<p class="message">
    Thank you for taking the time to provide your valuable feedback. Your input is incredibly important to us and helps us continuously improve our services.
</p>

<!-- Feedback Confirmation Section -->
<div class="feedback-confirmation">
    <div class="thank-you-icon">👍</div>
    <h3 class="confirmation-title">Feedback Received Successfully</h3>

    <div class="feedback-highlight">
        <div class="feedback-subject">Subject: "{{ feedback.subject }}"</div>
        <p class="feedback-text">
            We have received your feedback and will review it carefully. Your insights help us understand what we're doing well and where we can improve.
        </p>
    </div>
</div>

<!-- Appreciation Section -->
<div class="appreciation-section">
    <h4 class="appreciation-title">We Value Your Opinion</h4>
    <p class="appreciation-text">
        Your feedback contributes to making Librainian better for everyone. We take all suggestions seriously and use them to enhance our platform and services.
    </p>
</div>

<p class="message">
    If you have any further questions, comments, or additional feedback, please don't hesitate to contact us. We're always here to help and listen.
</p>

<div class="signature-section">
    <p class="message">Best regards,</p>
    <p class="message team-name">The Librainian Team</p>
</div>
{% endblock %}
