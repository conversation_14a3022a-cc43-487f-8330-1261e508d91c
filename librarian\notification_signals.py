"""
Signal handlers for QR registration notifications
"""

from django.db.models.signals import post_save
from django.dispatch import receiver
from .notification_service import notification_service
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender='studentsData.TempStudentData')
def qr_registration_notification(sender, instance, created, **kwargs):
    """Send notification when someone submits registration via QR code"""
    if created:
        try:
            # Send notification to librarian about new QR registration
            notification_service.qr_registration_submitted(instance)
            logger.info(f"QR registration notification sent for {instance.name}")
        except Exception as e:
            logger.error(f"Failed to send QR registration notification: {str(e)}")
