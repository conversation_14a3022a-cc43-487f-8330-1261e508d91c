{% extends "base.html" %}

{% block title %}Transaction Report - Librainian{% endblock %}

{% block page_title %}Transaction Report{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Transaction Report</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced Transaction Report Glass Design */
    .modern-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius-lg);
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.05) inset;
        padding: 0;
        transition: var(--transition-slow);
        position: relative;
        overflow: hidden;
    }

    .modern-card:hover {
        transform: translateY(-3px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .modern-card-body {
        padding: 2rem;
        position: relative;
        z-index: 2;
    }

    .modern-card-header {
        padding: 1.5rem 2rem 1rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
    }

    .report-form-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius-lg);
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset,
            0 2px 4px rgba(255, 255, 255, 0.1) inset;
        transition: var(--transition-slow);
        position: relative;
        overflow: hidden;
    }

    .report-form-card:hover {
        transform: translateY(-3px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .report-form-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        opacity: 0.8;
    }

    .date-input-group {
        position: relative;
        z-index: 2;
    }

    .date-input-group .form-control {
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius);
        padding: 0.875rem 1rem;
        font-size: 0.875rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        color: white;
    }

    .date-input-group .form-control:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        background: rgba(255, 255, 255, 0.2);
        color: white;
    }

    .date-input-group .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    /* Text visibility improvements - Only for content cards */
    .modern-card .modern-card-header h5,
    .modern-card .modern-card-header .card-subtitle,
    .modern-card .page-title,
    .modern-card .page-subtitle {
        color: white !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    .modern-card .text-muted,
    .report-form-card .text-muted {
        color: rgba(255, 255, 255, 0.8) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .report-form-card .form-label {
        color: white !important;
        font-weight: 500 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .report-form-card .text-gray-700 {
        color: white !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    /* Small text elements - Only within cards */
    .report-form-card small.text-muted {
        color: rgba(255, 255, 255, 0.7) !important;
        font-size: 0.8rem;
    }

    /* Summary statistics styling */
    .stat-item {
        color: white !important;
    }

    .stat-item h4,
    .stat-item p {
        color: white !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .generate-btn {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        border: none;
        border-radius: var(--border-radius);
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 0.875rem;
        color: white;
        transition: var(--transition-slow);
        box-shadow: var(--shadow-md);
        position: relative;
        overflow: hidden;
        z-index: 2;
    }

    .generate-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
        color: white;
    }

    .generate-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .generate-btn:hover::before {
        left: 100%;
    }

    .table-glass {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    }

    .table-glass thead th {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
        font-weight: 600;
        border: none;
        padding: 1rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .table-glass tbody td {
        padding: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.7);
        font-size: 0.875rem;
        font-weight: 500;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .table-glass tbody tr:hover td {
        background: rgba(99, 102, 241, 0.1);
        transform: scale(1.01);
        transition: var(--transition);
    }

    .summary-stats {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius-lg);
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .summary-stats::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        opacity: 0.8;
    }

    .stat-item {
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius);
        padding: 1rem;
        transition: var(--transition);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        position: relative;
        z-index: 2;
    }

    .stat-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        background: rgba(255, 255, 255, 0.9);
    }

    .stat-value {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--gray-900);
        margin: 0;
    }

    .stat-label {
        font-size: 0.75rem;
        color: var(--gray-600);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 600;
        margin: 0.25rem 0 0 0;
    }

    .download-btn {
        background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
        border: none;
        border-radius: var(--border-radius);
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        font-size: 0.875rem;
        color: white;
        transition: var(--transition-slow);
        box-shadow: var(--shadow-md);
        position: relative;
        overflow: hidden;
    }

    .download-btn:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
        color: white;
    }

    .download-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .download-btn:hover::before {
        left: 100%;
    }

    .no-data-message {
        background: rgba(239, 68, 68, 0.1);
        border: 2px solid rgba(239, 68, 68, 0.3);
        border-radius: var(--border-radius);
        padding: 2rem;
        text-align: center;
        color: var(--danger);
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    @media (max-width: 768px) {
        .table-glass {
            font-size: 0.75rem;
        }

        .table-glass thead th,
        .table-glass tbody td {
            padding: 0.75rem 0.5rem;
        }

        .generate-btn {
            padding: 0.875rem 1.5rem;
            font-size: 0.8rem;
        }

        .report-form-card {
            margin: 1rem;
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-content fade-in">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="modern-card">
            <div class="modern-card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="page-title mb-2">
                            <i class="fas fa-chart-line me-2 text-primary"></i>
                            Transaction Report Generator
                        </h2>
                        <p class="page-subtitle text-muted mb-0">
                            Generate detailed financial reports for your library operations
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="page-actions">
                            <span class="badge bg-primary-subtle text-primary px-3 py-2">
                                <i class="fas fa-calendar me-1"></i>
                                Financial Analytics
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Generation Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="report-form-card">
                <div class="modern-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        Report Configuration
                    </h5>
                    <p class="card-subtitle mb-0">Select date range to generate transaction report</p>
                </div>
                <div class="modern-card-body">
                    {% if role == 'sublibrarian' %}
                    <form method="POST" action="{% url 'transaction-report-sublib' %}">
                    {% else %}
                    <form method="POST" action="{% url 'transaction-report' %}">
                    {% endif %}
                        {% csrf_token %}
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="date-input-group">
                                    <label for="start_date" class="form-label fw-semibold text-gray-700">
                                        <i class="fas fa-calendar-alt me-1"></i>
                                        Start Date
                                    </label>
                                    <input type="date"
                                           name="start_date"
                                           id="start_date"
                                           min="{{three_months_ago|date:'Y-m-d'}}"
                                           value="{{ start_date|date:'Y-m-d' }}"
                                           class="form-control"
                                           required>
                                    <small class="text-muted">Up to last 3 months</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="date-input-group">
                                    <label for="end_date" class="form-label fw-semibold text-gray-700">
                                        <i class="fas fa-calendar-check me-1"></i>
                                        End Date
                                    </label>
                                    <input type="date"
                                           name="end_date"
                                           id="end_date"
                                           value="{{ end_date|date:'Y-m-d' }}"
                                           class="form-control"
                                           required>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit"
                                        name="generate_report"
                                        value="true"
                                        class="generate-btn w-100">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    Generate Transaction Report
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction Report Results -->
    {% if transactions %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="modern-card">
                <div class="modern-card-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-0">
                                <i class="fas fa-table me-2"></i>
                                Transaction Report
                            </h5>
                            <p class="card-subtitle mb-0">
                                Detailed transaction data from {{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <button id="downloadBtn" class="download-btn">
                                <i class="fas fa-download me-2"></i>
                                Export to Excel
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modern-card-body p-0">
                    <div class="table-responsive">
                        <table id="transactionTable" class="table table-glass mb-0">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-calendar me-1"></i>Date</th>
                                    <th><i class="fas fa-money-bill me-1"></i>Sales (Cash)</th>
                                    <th><i class="fas fa-credit-card me-1"></i>Sales (Online)</th>
                                    <th><i class="fas fa-chart-line me-1"></i>Total Daily Collection</th>
                                    <th><i class="fas fa-wallet me-1"></i>Closing Balance (Online)</th>
                                    <th><i class="fas fa-coins me-1"></i>Closing Balance (Cash)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions %}
                                <tr>
                                    <td class="fw-semibold">{{ transaction.date }}</td>
                                    <td class="text-success fw-semibold">₹{{ transaction.sales_cash }}</td>
                                    <td class="text-info fw-semibold">₹{{ transaction.sales_online }}</td>
                                    <td class="text-primary fw-semibold">₹{{ transaction.total_daily_collection }}</td>
                                    <td class="text-warning fw-semibold">₹{{ transaction.closing_balance_online }}</td>
                                    <td class="text-secondary fw-semibold">₹{{ transaction.closing_balance_cash }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="no-data-message">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        No transactions found for the selected date range.
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="summary-stats">
                <div class="row align-items-center mb-3">
                    <div class="col-md-8">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            Summary Statistics
                        </h5>
                        <p class="text-muted mb-0">Calculated totals for the selected period</p>
                    </div>
                </div>
                <div class="row g-4">
                    <div class="col-lg-4 col-md-6">
                        <div class="stat-item text-center">
                            <div class="stat-value text-success">₹{{ total_cash }}</div>
                            <div class="stat-label">Total Cash Sales</div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="stat-item text-center">
                            <div class="stat-value text-info">₹{{ total_online }}</div>
                            <div class="stat-label">Total Online Sales</div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="stat-item text-center">
                            <div class="stat-value text-primary">₹{{ total_daily_collection }}</div>
                            <div class="stat-label">Total Daily Collection</div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div class="stat-item text-center">
                            <div class="stat-value text-warning">₹{{ closing_balance_online }}</div>
                            <div class="stat-label">Closing Balance (Online)</div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div class="stat-item text-center">
                            <div class="stat-value text-secondary">₹{{ closing_balance_cash }}</div>
                            <div class="stat-label">Closing Balance (Cash)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Include xlsx library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.0/xlsx.full.min.js"></script>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add staggered animation delays
        const cards = document.querySelectorAll('.modern-card, .report-form-card, .summary-stats');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${(index + 1) * 0.1}s`;
            card.classList.add('slide-up');
        });

        // Excel download functionality
        const downloadBtn = document.getElementById('downloadBtn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', function() {
                // Get the table data
                var wb = XLSX.utils.table_to_book(document.getElementById('transactionTable'), {
                    sheet: "Transaction Report"
                });

                // Add totals as a new sheet
                var totalsData = [
                    ["Calculated Totals", "", ""],
                    ["Total Cash Sales", "{{ total_cash }}"],
                    ["Total Online Sales", "{{ total_online }}"],
                    ["Total Daily Collection", "{{ total_daily_collection }}"],
                    ["Closing Balance (Online)", "{{ closing_balance_online }}"],
                    ["Closing Balance (Cash)", "{{ closing_balance_cash }}"]
                ];

                var totalsSheet = XLSX.utils.aoa_to_sheet(totalsData);
                XLSX.utils.book_append_sheet(wb, totalsSheet, "Summary");

                // Download the file
                XLSX.writeFile(wb, "transaction_report_{{ start_date|date:'Y-m-d' }}_to_{{ end_date|date:'Y-m-d' }}.xlsx");

                // Show success toast
                if (window.modernDashboard) {
                    window.modernDashboard.showToast(
                        'Export Successful!',
                        'Transaction report has been downloaded.',
                        'success'
                    );
                }
            });
        }

        // Show welcome notification
        setTimeout(() => {
            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Transaction Report Ready!',
                    'Generate reports by selecting date range.',
                    'info'
                );
            }
        }, 1000);
    });
</script>
{% endblock %}