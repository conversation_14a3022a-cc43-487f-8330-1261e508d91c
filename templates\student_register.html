{% extends "base.html" %}

{% block title %}Student Register - Librainian{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">

<style>
    .student-register-container {
        min-height: 100vh;
        padding: 2rem 0;
    }

    /* Desktop Table Styles */
    .desktop-table-container {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .table-header h5 {
        color: white;
        margin-bottom: 0.5rem;
    }

    .table-header p {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.875rem;
    }

    .table {
        color: white !important;
        margin-bottom: 0;
    }

    .table thead th {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        color: white !important;
        font-weight: 600;
        padding: 1rem 0.75rem;
    }

    .table tbody td {
        border: none;
        padding: 0.875rem 0.75rem;
        background: rgba(255, 255, 255, 0.05);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: white !important;
    }

    .table tbody tr:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .table tbody tr:hover td {
        color: white !important;
    }

    /* Clickable Row Styling */
    .table-row-clickable {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .table-row-clickable:hover {
        background: rgba(255, 255, 255, 0.15) !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    /* Force white text for all table elements */
    .table th,
    .table td,
    .table thead th,
    .table tbody td,
    .table tfoot td {
        color: white !important;
    }

    /* Mobile Card Styles */
    .mobile-cards-container {
        display: none;
    }

    .student-card {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .student-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .student-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(99, 102, 241, 0.3);
    }

    .student-card:hover::before {
        left: 100%;
    }

    .student-card:active {
        transform: translateY(0);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    /* Click Indicator */
    .card-click-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1rem;
        padding: 0.75rem;
        background: rgba(99, 102, 241, 0.1);
        border: 1px solid rgba(99, 102, 241, 0.2);
        border-radius: 10px;
        color: #6366f1;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
        opacity: 0.8;
    }

    .student-card:hover .card-click-indicator {
        background: rgba(99, 102, 241, 0.2);
        border-color: #6366f1;
        opacity: 1;
        transform: translateX(5px);
    }

    .card-click-indicator i {
        transition: transform 0.3s ease;
    }

    .student-card:hover .card-click-indicator i {
        transform: translateX(3px);
    }

    .student-card-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .student-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        margin-right: 1rem;
    }

    .student-info h6 {
        color: white;
        margin: 0;
        font-weight: 600;
    }

    .student-info p {
        color: rgba(255, 255, 255, 0.7);
        margin: 0;
        font-size: 0.9rem;
    }

    .student-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .detail-item {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.85rem;
    }

    .detail-label {
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
    }

    /* Mobile Search and Pagination */
    .mobile-controls {
        display: none;
        margin-bottom: 1rem;
    }

    .mobile-search {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        color: white;
        width: 100%;
        margin-bottom: 1rem;
    }

    .mobile-search::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .mobile-search:focus {
        outline: none;
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }

    /* Mobile Filter and Sort Controls */
    .mobile-filter,
    .mobile-sort {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    .mobile-filter:focus,
    .mobile-sort:focus {
        outline: none;
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }

    .mobile-filter option,
    .mobile-sort option {
        background: #1a1a1a;
        color: white;
    }

    /* Mobile Results Info */
    .mobile-results-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .mobile-clear-filters {
        background: rgba(239, 68, 68, 0.2);
        border: 1px solid rgba(239, 68, 68, 0.3);
        color: #ef4444;
        padding: 0.25rem 0.75rem;
        border-radius: 6px;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .mobile-clear-filters:hover {
        background: rgba(239, 68, 68, 0.3);
        border-color: #ef4444;
    }

    .mobile-pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-top: 1rem;
    }

    .pagination-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        padding: 0.5rem 1rem;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .pagination-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        text-decoration: none;
    }

    .pagination-btn.disabled {
        opacity: 0.5;
        pointer-events: none;
    }

    /* DataTables Styling */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        color: white;
    }

    .dataTables_wrapper .dataTables_filter input {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        color: white;
        padding: 0.5rem;
    }

    .dataTables_wrapper .dataTables_length select {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        color: white;
        padding: 0.25rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .desktop-table-container {
            display: none;
        }

        .mobile-cards-container,
        .mobile-controls {
            display: block;
        }

        .student-register-container {
            padding: 1rem;
        }

        .student-details {
            grid-template-columns: 1fr;
        }
    }

    /* Dark mode adjustments */
    [data-theme="dark"] .desktop-table-container,
    [data-theme="dark"] .student-card {
        background: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.1);
    }

    [data-theme="dark"] .table thead th {
        background: rgba(0, 0, 0, 0.3);
        color: white !important;
    }

    [data-theme="dark"] .table tbody td {
        background: rgba(0, 0, 0, 0.1);
        color: white !important;
    }

    [data-theme="dark"] .table th,
    [data-theme="dark"] .table td,
    [data-theme="dark"] .table thead th,
    [data-theme="dark"] .table tbody td,
    [data-theme="dark"] .table tfoot td {
        color: white !important;
    }

    /* DataTables Pagination Styling */
    .dataTables_wrapper .dataTables_paginate {
        margin-top: 1rem;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        border-radius: 8px !important;
        margin: 0 2px !important;
        padding: 8px 16px !important;
        transition: all 0.3s ease !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: rgba(99, 102, 241, 0.3) !important;
        border-color: rgba(99, 102, 241, 0.5) !important;
        color: white !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;
        border-color: #6366f1 !important;
        color: white !important;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4) !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: rgba(255, 255, 255, 0.05) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.4) !important;
        cursor: not-allowed !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
        background: rgba(255, 255, 255, 0.05) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.4) !important;
        transform: none !important;
        box-shadow: none !important;
    }

    /* DataTables Info and Length Styling */
    .dataTables_wrapper .dataTables_info {
        color: rgba(255, 255, 255, 0.8) !important;
        margin-top: 1rem;
    }

    .dataTables_wrapper .dataTables_length {
        color: rgba(255, 255, 255, 0.8) !important;
        margin-bottom: 1rem;
    }

    .dataTables_wrapper .dataTables_length select {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        border-radius: 8px !important;
        padding: 4px 8px !important;
    }

    .dataTables_wrapper .dataTables_filter {
        color: rgba(255, 255, 255, 0.8) !important;
        margin-bottom: 1rem;
    }

    .dataTables_wrapper .dataTables_filter input {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        border-radius: 8px !important;
        padding: 8px 12px !important;
    }

    .dataTables_wrapper .dataTables_filter input::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="student-register-container">
    <div class="container-fluid">
        <!-- Mobile Controls -->
        <div class="mobile-controls">
            <div class="row g-2 mb-3">
                <div class="col-12">
                    <input type="text" class="mobile-search" placeholder="Search students..." id="mobileSearchInput">
                </div>
            </div>

            <!-- Mobile Filter and Sort Controls -->
            <div class="row g-2 mb-3">
                <div class="col-6">
                    <select class="mobile-filter" id="mobileStatusFilter">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
                <div class="col-6">
                    <select class="mobile-sort" id="mobileSortSelect">
                        <option value="name-asc">Name (A-Z)</option>
                        <option value="name-desc">Name (Z-A)</option>
                        <option value="date-desc">Newest First</option>
                        <option value="date-asc">Oldest First</option>
                        <option value="course-asc">Course (A-Z)</option>
                    </select>
                </div>
            </div>

            <!-- Mobile Results Info -->
            <div class="mobile-results-info">
                <span id="mobileResultsCount">Showing all students</span>
                <button class="mobile-clear-filters" id="mobileClearFilters" style="display: none;">
                    <i class="fas fa-times"></i> Clear Filters
                </button>
            </div>
        </div>

        <!-- Desktop Table View -->
        <div class="desktop-table-container">
            <div class="table-header mb-3">
                <h5 class="mb-1">
                    <i class="fas fa-users me-2"></i>
                    All Students
                </h5>
                <p class="mb-0 text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Click on any row to view student profile
                </p>
            </div>
            <table class="table table-hover" id="studentsTable">
                    <thead>
                        <tr>
                            <th>Student ID</th>
                            <th>Name</th>
                            <th>Course</th>
                            <th>Email</th>
                            <th>Mobile</th>
                            <th>Registration Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for student in students %}
                        <tr class="table-row-clickable" onclick="window.location.href='/students/{{ student.slug }}/'" title="Click to view {{ student.name }}'s profile">
                            <td>{{ student.unique_id }}</td>
                            <td>{{ student.name }}</td>
                            <td>{{ student.course }}</td>
                            <td>{{ student.email }}</td>
                            <td>{{ student.mobile }}</td>
                            <td>{{ student.registration_date|date:"M d, Y" }}</td>
                            <td>
                                <span class="badge bg-success">Active</span>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">No students found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Mobile Cards View -->
            <div class="mobile-cards-container" id="mobileCardsContainer">
                {% for student in students %}
                <div class="student-card"
                     data-search="{{ student.name|lower }} {{ student.unique_id|lower }} {{ student.course|lower }}"
                     data-status="active"
                     data-date="{{ student.registration_date|date:'Y-m-d' }}"
                     onclick="window.location.href='/students/{{ student.slug }}/'">
                    <div class="student-details">
                        <div class="detail-item">
                            <span class="detail-label">Name:</span> <span class="student-name">{{ student.name }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">ID:</span> {{ student.unique_id }}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Course:</span> <span class="student-course">{{ student.course }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Email:</span> {{ student.email }}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Mobile:</span> {{ student.mobile }}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Registered:</span> {{ student.registration_date|date:"M d, Y" }}
                        </div>
                    </div>

                    <!-- Click indicator -->
                    <div class="card-click-indicator">
                        <i class="fas fa-arrow-right"></i>
                        <span>View Profile</span>
                    </div>
                </div>
                {% empty %}
                <div class="text-center" style="color: rgba(255, 255, 255, 0.7); padding: 2rem;">
                    <i class="fas fa-users fa-3x mb-3" style="opacity: 0.5;"></i>
                    <p>No students found</p>
                </div>
                {% endfor %}
            </div>

            <!-- Mobile Pagination -->
            <div class="mobile-pagination" id="mobilePagination" style="display: none;">
                <a href="#" class="pagination-btn" id="prevBtn">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
                <span style="color: white;" id="pageInfo">Page 1 of 1</span>
                <a href="#" class="pagination-btn" id="nextBtn">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
            </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTable for desktop
        $('#studentsTable').DataTable({
            responsive: true,
            pageLength: 25,
            language: {
                search: "Search students:",
                lengthMenu: "Show _MENU_ students per page",
                info: "Showing _START_ to _END_ of _TOTAL_ students",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                }
            },
            columnDefs: [
                { responsivePriority: 1, targets: 0 }, // Student ID
                { responsivePriority: 2, targets: 1 }, // Name
                { responsivePriority: 3, targets: 2 }  // Course
            ]
        });

        // Mobile search and pagination functionality
        const mobileSearchInput = document.getElementById('mobileSearchInput');
        const studentCards = document.querySelectorAll('.student-card');

        let currentPage = 1;
        const cardsPerPage = 10;
        let filteredCards = Array.from(studentCards);

        function updateMobilePagination() {
            const totalPages = Math.ceil(filteredCards.length / cardsPerPage);
            const startIndex = (currentPage - 1) * cardsPerPage;
            const endIndex = startIndex + cardsPerPage;

            // Hide all cards
            studentCards.forEach(card => card.style.display = 'none');

            // Show cards for current page
            filteredCards.slice(startIndex, endIndex).forEach(card => {
                card.style.display = 'block';
            });

            // Update pagination controls
            const pageInfo = document.getElementById('pageInfo');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const mobilePagination = document.getElementById('mobilePagination');

            if (filteredCards.length > cardsPerPage) {
                mobilePagination.style.display = 'flex';
                pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;

                prevBtn.classList.toggle('disabled', currentPage === 1);
                nextBtn.classList.toggle('disabled', currentPage === totalPages);
            } else {
                mobilePagination.style.display = 'none';
            }
        }

        // Mobile controls
        const mobileStatusFilter = document.getElementById('mobileStatusFilter');
        const mobileSortSelect = document.getElementById('mobileSortSelect');
        const mobileResultsCount = document.getElementById('mobileResultsCount');
        const mobileClearFilters = document.getElementById('mobileClearFilters');

        // Mobile search
        if (mobileSearchInput) {
            mobileSearchInput.addEventListener('input', function() {
                applyMobileFilters();
            });
        }

        // Mobile status filter
        if (mobileStatusFilter) {
            mobileStatusFilter.addEventListener('change', function() {
                applyMobileFilters();
            });
        }

        // Mobile sort
        if (mobileSortSelect) {
            mobileSortSelect.addEventListener('change', function() {
                applyMobileFilters();
            });
        }

        // Clear filters
        if (mobileClearFilters) {
            mobileClearFilters.addEventListener('click', function() {
                mobileSearchInput.value = '';
                mobileStatusFilter.value = '';
                mobileSortSelect.value = 'name-asc';
                applyMobileFilters();
            });
        }

        // Apply mobile filters function
        function applyMobileFilters() {
            const searchTerm = mobileSearchInput.value.toLowerCase();
            const statusFilter = mobileStatusFilter.value.toLowerCase();
            const sortOption = mobileSortSelect.value;

            // Filter cards
            filteredCards = Array.from(studentCards).filter(card => {
                const searchData = card.getAttribute('data-search');
                const statusData = card.getAttribute('data-status') || 'active';

                const matchesSearch = !searchTerm || (searchData && searchData.includes(searchTerm));
                const matchesStatus = !statusFilter || statusData.toLowerCase() === statusFilter;

                return matchesSearch && matchesStatus;
            });

            // Sort cards
            filteredCards.sort((a, b) => {
                const aName = a.querySelector('.student-name').textContent.trim();
                const bName = b.querySelector('.student-name').textContent.trim();
                const aDate = a.getAttribute('data-date') || '';
                const bDate = b.getAttribute('data-date') || '';
                const aCourse = a.querySelector('.student-course').textContent.trim();
                const bCourse = b.querySelector('.student-course').textContent.trim();

                switch (sortOption) {
                    case 'name-desc':
                        return bName.localeCompare(aName);
                    case 'date-desc':
                        return new Date(bDate) - new Date(aDate);
                    case 'date-asc':
                        return new Date(aDate) - new Date(bDate);
                    case 'course-asc':
                        return aCourse.localeCompare(bCourse);
                    default: // name-asc
                        return aName.localeCompare(bName);
                }
            });

            // Update results count
            const totalStudents = studentCards.length;
            const filteredCount = filteredCards.length;

            if (searchTerm || statusFilter) {
                mobileResultsCount.textContent = `Showing ${filteredCount} of ${totalStudents} students`;
                mobileClearFilters.style.display = 'inline-block';
            } else {
                mobileResultsCount.textContent = `Showing all ${totalStudents} students`;
                mobileClearFilters.style.display = 'none';
            }

            currentPage = 1;
            updateMobilePagination();
        }

        // Mobile pagination controls
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        if (prevBtn) {
            prevBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage > 1) {
                    currentPage--;
                    updateMobilePagination();
                }
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', function(e) {
                e.preventDefault();
                const totalPages = Math.ceil(filteredCards.length / cardsPerPage);
                if (currentPage < totalPages) {
                    currentPage++;
                    updateMobilePagination();
                }
            });
        }

        // Initialize mobile pagination
        updateMobilePagination();

        // Entrance animations
        const tableContainer = document.querySelector('.desktop-table-container');
        if (tableContainer) {
            tableContainer.style.opacity = '0';
            tableContainer.style.transform = 'translateY(20px)';

            setTimeout(() => {
                tableContainer.style.transition = 'all 0.5s ease';
                tableContainer.style.opacity = '1';
                tableContainer.style.transform = 'translateY(0)';
            }, 100);
        }
    });


</script>
{% endblock %}
