{% extends "email_base.html" %}

{% block email_title %}Welcome to {{ library_name|default:'Librainian' }} - Registration Successful{% endblock %}

{% block email_subject %}Welcome to {{ library_name|default:'Librainian' }}!{% endblock %}

{% block email_description %}Welcome email for successful student registration at {{ library_name|default:'Librainian' }}.{% endblock %}

{% block preview_text %}Welcome to {{ library_name|default:'Librainian' }}! Your registration is successful. Student ID: {{ student.unique_id }}{% endblock %}

{% block header_icon %}🎓{% endblock %}

{% block email_header_title %}Welcome to {{ library_name|default:'Librainian' }}!{% endblock %}

{% block email_header_subtitle %}Your registration has been successfully completed{% endblock %}
{% block email_styles %}
<style>
    .welcome-section {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-radius: 16px;
        padding: 30px;
        text-align: center;
        margin: 30px 0;
        border: 2px solid #6366f1;
        position: relative;
        overflow: hidden;
    }

    .welcome-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.1), transparent);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(30deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(30deg); }
    }

    .welcome-icon {
        font-size: 48px;
        margin-bottom: 20px;
        position: relative;
        z-index: 1;
    }

    .welcome-title {
        font-size: 24px;
        font-weight: 700;
        color: #2c3e50;
        margin: 0 0 15px 0;
        position: relative;
        z-index: 1;
    }

    .welcome-description {
        font-size: 16px;
        color: #6c757d;
        margin: 0;
        position: relative;
        z-index: 1;
    }
    .details-table {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 25px;
        margin: 30px 0;
        border-left: 4px solid #6366f1;
    }

    .table-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 20px 0;
        display: flex;
        align-items: center;
    }

    .table-title::before {
        content: "📋";
        margin-right: 10px;
        font-size: 20px;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .detail-row:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 14px;
        min-width: 120px;
    }

    .detail-value {
        font-weight: 500;
        color: #2c3e50;
        font-size: 16px;
        text-align: right;
        flex: 1;
    }
    .next-steps {
        background-color: #f0fdf4;
        border: 1px solid #bbf7d0;
        border-radius: 8px;
        padding: 20px;
        margin: 25px 0;
        border-left: 4px solid #10b981;
    }

    .steps-title {
        font-size: 16px;
        font-weight: 600;
        color: #065f46;
        margin: 0 0 15px 0;
        display: flex;
        align-items: center;
    }

    .steps-title::before {
        content: "✅";
        margin-right: 8px;
        font-size: 18px;
    }

    .steps-list {
        font-size: 14px;
        color: #065f46;
        margin: 0;
        line-height: 1.6;
        padding-left: 20px;
    }

    .steps-list li {
        margin-bottom: 8px;
    }

    .action-button {
        display: inline-block;
        background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        color: #ffffff !important;
        text-decoration: none;
        padding: 16px 32px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3);
        margin: 20px 0;
    }

    .action-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        text-decoration: none;
        color: #ffffff !important;
    }
    @media only screen and (max-width: 600px) {
        .welcome-section {
            padding: 25px 20px !important;
            margin: 20px 0 !important;
        }

        .welcome-title {
            font-size: 20px !important;
        }

        .details-table,
        .next-steps {
            padding: 20px !important;
            margin: 20px 0 !important;
        }

        .detail-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
        }

        .detail-value {
            text-align: left !important;
        }

        .action-button {
            padding: 14px 28px !important;
            font-size: 14px !important;
        }
    }
</style>
{% endblock %}

{% block email_content %}
<h2 class="greeting">Dear {{ student.name }}!</h2>
<p class="message">
    Congratulations! We are thrilled to welcome you to {{ library_name|default:'Librainian' }}. Your registration has been successfully processed,
    and you are now part of our learning community. We look forward to supporting your educational journey.
</p>

<!-- Welcome Section -->
<div class="welcome-section">
    <div class="welcome-icon">🌟</div>
    <h3 class="welcome-title">Registration Successful!</h3>
    <p class="welcome-description">
        Your student account has been created and is ready to use. Below are your registration details for your records.
    </p>
</div>

<!-- Student Details -->
<div class="details-table">
    <h3 class="table-title">Your Registration Details</h3>

    <div class="detail-row">
        <span class="detail-label">Student ID</span>
        <span class="detail-value">{{ student.unique_id }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Full Name</span>
        <span class="detail-value">{{ student.name }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Course</span>
        <span class="detail-value">{{ student.course }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Email</span>
        <span class="detail-value">{{ student.email }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Mobile</span>
        <span class="detail-value">{{ student.mobile }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Registration Date</span>
        <span class="detail-value">{{ student.registration_date }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Registration Fee</span>
        <span class="detail-value">₹{{ student.registration_fee }}</span>
    </div>
</div>

<!-- Next Steps -->
<div class="next-steps">
    <h4 class="steps-title">Next Steps</h4>
    <ul class="steps-list">
        <li>Keep your Student ID safe - you'll need it for all library services</li>
        <li>Complete your fee payment if not already done</li>
        <li>Visit the library to collect your library card</li>
        <li>Familiarize yourself with library rules and timings</li>
        <li>Contact us if you have any questions or need assistance</li>
    </ul>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{ student_portal_url|default:'#' }}" class="action-button">Access Student Portal</a>
</div>

<p style="margin: 30px 0 0 0; color: #6c757d; font-size: 14px; text-align: center;">
    Welcome to our learning community! We're excited to have you with us.
</p>
{% endblock %}
