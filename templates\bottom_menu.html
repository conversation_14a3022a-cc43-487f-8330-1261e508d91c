<style>
    .bottom-menu {
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: var(--bottom-menu-height);
        background: var(--bg-primary);
        border-top: 1px solid var(--border-color);
        padding: 0.5rem;
        position: relative;
        z-index: 9999;
        transition: all 0.3s ease;
    }

    /* Show bottom menu on mobile devices */
    @media (max-width: 991.98px) {
        .bottom-menu-mobile {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .bottom-menu {
            display: flex !important;
        }
    }

    /* Hide bottom menu container on desktop - multiple selectors for maximum specificity */
    @media (min-width: 992px) {
        .bottom-menu-mobile,
        .bottom-menu-mobile.d-lg-none,
        div.bottom-menu-mobile,
        body .bottom-menu-mobile,
        html body .bottom-menu-mobile {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            min-height: 0 !important;
            max-height: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            top: -9999px !important;
            left: -9999px !important;
            z-index: -1 !important;
        }

        /* Also hide the bottom menu itself on desktop */
        .bottom-menu,
        body .bottom-menu,
        html body .bottom-menu {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
        }

        /* Override any fixed positioning on desktop */
        .bottom-menu-mobile[style*="position: fixed"] {
            position: absolute !important;
            top: -9999px !important;
        }
    }

    /* Dark Mode Bottom Menu */
    body.dark-mode .bottom-menu {
        background: var(--bg-secondary);
        border-top-color: var(--border-color);
    }

    .bottom-menu-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 0.25rem;
        padding: 0.5rem;
        border-radius: var(--border-radius);
        text-decoration: none;
        color: var(--text-muted);
        transition: var(--transition);
        min-width: 60px;
        position: relative;
    }

    .bottom-menu-item:hover {
        color: var(--primary);
        background: rgba(99, 102, 241, 0.1);
    }

    .bottom-menu-item.active {
        color: var(--primary);
        background: rgba(99, 102, 241, 0.1);
    }

    /* Dark Mode Bottom Menu Items */
    body.dark-mode .bottom-menu-item {
        color: var(--text-muted);
    }

    body.dark-mode .bottom-menu-item:hover {
        color: var(--primary);
        background: rgba(99, 102, 241, 0.2);
    }

    body.dark-mode .bottom-menu-item.active {
        color: var(--primary);
        background: rgba(99, 102, 241, 0.2);
    }

    /* Ensure button elements match link styling */
    .bottom-menu-item[type="button"],
    button.bottom-menu-item {
        background: transparent;
        border: none;
        cursor: pointer;
        font-family: inherit;
    }

    /* Dark mode for button elements */
    body.dark-mode .bottom-menu-item[type="button"],
    body.dark-mode button.bottom-menu-item {
        background: transparent;
        color: var(--text-muted);
    }

    body.dark-mode .bottom-menu-item[type="button"]:hover,
    body.dark-mode button.bottom-menu-item:hover {
        color: var(--primary);
        background: rgba(99, 102, 241, 0.2);
    }

    body.dark-mode .bottom-menu-item[type="button"].active,
    body.dark-mode button.bottom-menu-item.active {
        color: var(--primary);
        background: rgba(99, 102, 241, 0.2);
    }

    .bottom-menu-icon {
        font-size: 1.25rem;
        transition: var(--transition);
    }

    .bottom-menu-text {
        font-size: 0.75rem;
        font-weight: 500;
        text-align: center;
        line-height: 1;
    }

    .bottom-menu-badge {
        position: absolute;
        top: 0.25rem;
        right: 0.75rem;
        width: 8px;
        height: 8px;
        background: var(--danger);
        border-radius: 50%;
        border: 2px solid white;
    }

    /* Dark mode badge */
    body.dark-mode .bottom-menu-badge {
        border-color: var(--bg-secondary);
    }

    /* Animation for active state */
    .bottom-menu-item.active .bottom-menu-icon {
        transform: scale(1.1);
    }

    /* Responsive adjustments */
    @media (max-width: 480px) {
        .bottom-menu-text {
            font-size: 0.6875rem;
        }

        .bottom-menu-icon {
            font-size: 1.125rem;
        }

        .bottom-menu-item {
            min-width: 45px;
            padding: 0.25rem;
            gap: 0.125rem;
        }
    }

    @media (max-width: 360px) {
        .bottom-menu-text {
            font-size: 0.625rem;
        }

        .bottom-menu-icon {
            font-size: 1rem;
        }

        .bottom-menu-item {
            min-width: 40px;
            padding: 0.25rem;
        }
    }

    /* Ensure icons are visible */
    .bottom-menu-icon {
        display: inline-block !important;
        opacity: 1 !important;
        visibility: visible !important;
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 900 !important;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* Ensure all FontAwesome icons render properly */
    .bottom-menu-icon.fas,
    .bottom-menu-icon.fa {
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 900 !important;
    }

    /* Fix for specific icons */
    .fa-tachometer-alt:before { content: "\f3fd"; }
    .fa-users:before { content: "\f0c0"; }
    .fa-chart-line:before { content: "\f201"; }
    .fa-table:before { content: "\f0ce"; }
    .fa-bullhorn:before { content: "\f0a1"; }
    .fa-money-bill:before { content: "\f0d6"; }
    .fa-exclamation-circle:before { content: "\f06a"; }
    .fa-bars:before { content: "\f0c9"; }
</style>

<div class="bottom-menu">
    <!-- Dashboard -->
    <a href="/{{ role }}/dashboard/" class="bottom-menu-item" onclick="setActiveBottomMenu(this)">
        <i class="fas fa-tachometer-alt bottom-menu-icon"></i>
        <span class="bottom-menu-text">Dashboard</span>
    </a>

    <!-- Fees Due -->
    <a href="/students/" class="bottom-menu-item" onclick="setActiveBottomMenu(this)">
        <i class="fas fa-users bottom-menu-icon"></i>
        <span class="bottom-menu-text">Fees Due</span>
        {% if role == "librarian" %}
        <span class="bottom-menu-badge"></span>
        {% endif %}
    </a>

    <!-- Analytics/Tables -->
    {% if role == "librarian" %}
    <a href="/{{ role }}/analytics/" class="bottom-menu-item" onclick="setActiveBottomMenu(this)">
        <i class="fas fa-chart-line bottom-menu-icon"></i>
        <span class="bottom-menu-text">Analytics</span>
    </a>
    {% else %}
    <a href="/{{ role }}/table/" class="bottom-menu-item" onclick="setActiveBottomMenu(this)">
        <i class="fas fa-table bottom-menu-icon"></i>
        <span class="bottom-menu-text">Tables</span>
    </a>
    {% endif %}

    <!-- Transactions/Management -->
    {% if role == "librarian" %}
    <a href="/{{ role }}/marketing/" class="bottom-menu-item" onclick="setActiveBottomMenu(this)">
        <i class="fas fa-bullhorn bottom-menu-icon"></i>
        <span class="bottom-menu-text">Marketing</span>
    </a>
    {% elif role == "sublibrarian" %}
    <a href="/{{ role }}/daily-transaction/" class="bottom-menu-item" onclick="setActiveBottomMenu(this)">
        <i class="fas fa-money-bill bottom-menu-icon"></i>
        <span class="bottom-menu-text">Transactions</span>
    </a>
    {% elif role == "librarycommander" %}
    <a href="/{{ role }}/complaint_dashboard/" class="bottom-menu-item" onclick="setActiveBottomMenu(this)">
        <i class="fas fa-exclamation-circle bottom-menu-icon"></i>
        <span class="bottom-menu-text">Complaints</span>
    </a>
    {% endif %}

    <!-- More/Menu -->
    <button class="bottom-menu-item" onclick="toggleMobileSidebar()">
        <i class="fas fa-bars bottom-menu-icon"></i>
        <span class="bottom-menu-text">More</span>
    </button>
</div>

<script>
    // Set active bottom menu item
    function setActiveBottomMenu(element) {
        // Remove active class from all bottom menu items
        document.querySelectorAll('.bottom-menu-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to clicked item
        element.classList.add('active');
        
        // Store active item in localStorage
        localStorage.setItem('activeBottomMenuItem', element.getAttribute('href'));
    }

    // Set active bottom menu item on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Force hide bottom menu on desktop screens
        function hideBottomMenuOnDesktop() {
            const bottomMenuMobile = document.querySelector('.bottom-menu-mobile');
            if (bottomMenuMobile && window.innerWidth >= 992) {
                bottomMenuMobile.style.display = 'none';
                bottomMenuMobile.style.visibility = 'hidden';
                bottomMenuMobile.style.opacity = '0';
                bottomMenuMobile.style.height = '0';
                bottomMenuMobile.style.overflow = 'hidden';
                bottomMenuMobile.style.position = 'absolute';
                bottomMenuMobile.style.top = '-9999px';
                bottomMenuMobile.style.left = '-9999px';
                bottomMenuMobile.style.zIndex = '-1';
            } else if (bottomMenuMobile && window.innerWidth < 992) {
                // Ensure it's visible on mobile
                bottomMenuMobile.style.display = 'block';
                bottomMenuMobile.style.visibility = 'visible';
                bottomMenuMobile.style.opacity = '1';
                bottomMenuMobile.style.position = 'fixed';
                bottomMenuMobile.style.bottom = '0';
                bottomMenuMobile.style.left = '0';
                bottomMenuMobile.style.right = '0';
                bottomMenuMobile.style.top = 'auto';
                bottomMenuMobile.style.zIndex = '9999';
                bottomMenuMobile.style.height = 'auto';
                bottomMenuMobile.style.overflow = 'visible';
            }
        }

        // Run on load
        hideBottomMenuOnDesktop();

        // Run on resize
        window.addEventListener('resize', hideBottomMenuOnDesktop);

        const currentPath = window.location.pathname;
        const bottomMenuItems = document.querySelectorAll('.bottom-menu-item[href]');

        bottomMenuItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href && (currentPath === href || currentPath.startsWith(href + '/'))) {
                item.classList.add('active');
            }
        });
    });

    // Handle bottom menu visibility on scroll (optional enhancement)
    let lastScrollTop = 0;
    let bottomMenu = null;

    document.addEventListener('DOMContentLoaded', function() {
        bottomMenu = document.querySelector('.bottom-menu-mobile');

        if (bottomMenu) {
            // Ensure menu is always visible initially
            bottomMenu.style.transform = 'translateY(0)';
            bottomMenu.style.zIndex = '9999';
            bottomMenu.style.position = 'fixed';
            bottomMenu.style.bottom = '0';

            // Disable auto-hide on scroll for now to ensure visibility
            // window.addEventListener('scroll', function() {
            //     const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            //
            //     if (scrollTop > lastScrollTop && scrollTop > 100) {
            //         // Scrolling down - hide menu
            //         bottomMenu.style.transform = 'translateY(100%)';
            //     } else {
            //         // Scrolling up - show menu
            //         bottomMenu.style.transform = 'translateY(0)';
            //     }
            //
            //     lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
            // }, false);
        }
    });

    // Fallback function for mobile sidebar toggle
    function toggleMobileSidebar() {
        if (window.modernDashboard && window.modernDashboard.toggleMobileSidebar) {
            window.modernDashboard.toggleMobileSidebar();
        } else {
            // Fallback: toggle sidebar manually
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                const isVisible = sidebar.style.transform !== 'translateX(-100%)';
                if (isVisible) {
                    sidebar.style.transform = 'translateX(-100%)';
                    // Remove overlay if it exists
                    const overlay = document.querySelector('.sidebar-overlay');
                    if (overlay) {
                        overlay.remove();
                    }
                } else {
                    sidebar.style.transform = 'translateX(0)';
                    // Add overlay for mobile
                    if (window.innerWidth < 992) {
                        const overlay = document.createElement('div');
                        overlay.className = 'sidebar-overlay';
                        overlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 999;';
                        overlay.onclick = () => toggleMobileSidebar();
                        document.body.appendChild(overlay);
                    }
                }
            }
        }
    }
</script>
