<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="google" content="notranslate">
    <meta name="theme-color" content="#6366f1">
    <meta name="robots" content="noindex, nofollow">

    <title>Membership Checkout - Librainian</title>

    <!-- Favicon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">

    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #10b981;
            --secondary-dark: #059669;
            --success: #22c55e;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #3b82f6;
            --light: #f8fafc;
            --dark: #1e293b;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
        }

        * {
            box-sizing: border-box;
            transition: all 0.3s ease;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        /* Glass Design Components */
        .glass-container {
            min-height: 100vh;
            padding: 2rem 1rem;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .glass-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 1.5rem 2rem;
            position: relative;
            overflow: hidden;
        }

        .glass-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            transform: rotate(-45deg);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) rotate(-45deg); }
            50% { transform: translateX(100%) rotate(-45deg); }
        }

        .glass-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.5rem;
            position: relative;
            z-index: 1;
        }

        .glass-body {
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        /* Plan Info Section */
        .plan-info {
            display: flex;
            align-items: flex-start;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .plan-details {
            flex: 1;
        }

        .plan-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .plan-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .plan-features li {
            padding: 0.5rem 0;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
        }

        .plan-features li::before {
            content: '✓';
            color: var(--secondary);
            font-weight: bold;
            margin-right: 0.75rem;
        }

        .price-section {
            text-align: right;
            min-width: 200px;
        }

        .price-main {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .price-discount {
            font-size: 0.9rem;
            color: var(--danger);
            margin-bottom: 1rem;
        }

        .remove-btn {
            background: linear-gradient(135deg, var(--danger), #dc2626);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .remove-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(239, 68, 68, 0.4);
            color: white;
            text-decoration: none;
        }
        /* Pricing Details */
        .pricing-details {
            margin-top: 2rem;
        }

        .pricing-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .pricing-row:last-child {
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
            font-weight: 600;
            font-size: 1.1rem;
            margin-top: 1rem;
            padding-top: 1rem;
        }

        .pricing-label {
            color: var(--text-secondary);
            display: flex;
            align-items: center;
        }

        .pricing-value {
            color: var(--text-primary);
            font-weight: 600;
        }

        .pricing-value.success {
            color: var(--secondary);
        }

        /* Checkout Button */
        .checkout-btn {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            margin-top: 2rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3);
        }

        .checkout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
        }

        /* Footer */
        .glass-footer {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1.5rem 2rem;
            text-align: center;
        }

        .footer-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .summary-text {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .summary-total {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary);
        }
        /* Responsive Design */
        @media (max-width: 768px) {
            .glass-container {
                padding: 1rem 0.5rem;
            }

            .glass-card {
                border-radius: 20px;
            }

            .glass-body {
                padding: 1.5rem;
            }

            .plan-info {
                flex-direction: column;
                gap: 1rem;
            }

            .price-section {
                text-align: left;
                min-width: auto;
            }
        }

        @media (max-width: 480px) {
            .glass-body {
                padding: 1rem;
            }

            .plan-title {
                font-size: 1.5rem;
            }

            .price-main {
                font-size: 1.75rem;
            }
        }

        /* Disable zoom on mobile */
        @media (max-width: 768px) {
            body {
                touch-action: pan-x pan-y;
            }
        }
    </style>
</head>
<body class="modern-dashboard">
    <div class="glass-container">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <div class="glass-card">
                        <div class="glass-header">
                            <h4><i class="fas fa-shopping-cart me-2"></i>Library Membership Checkout</h4>
                        </div>
                        <div class="glass-body">
                            <div class="plan-info">
                                <div class="plan-details">
                                    <h1 class="plan-title">{{plan.name}}</h1>
                                    <ul class="plan-features">
                                        <li>{{plan.description_line_01}}</li>
                                        <li>{{plan.description_line_02}}</li>
                                        <li>{{plan.description_line_03}}</li>
                                    </ul>
                                </div>
                                <div class="price-section">
                                    <div class="price-main">₹{{plan.price}}</div>
                                    <div class="price-discount">
                                        <del>₹{{plan.price}}</del> 50% off
                                    </div>
                                    <a href="/membership/plans/" class="remove-btn">
                                        <i class="fas fa-trash me-2"></i>Remove
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="glass-footer">
                            <div class="footer-summary">
                                <span class="summary-text">Items in Cart: <strong>1</strong></span>
                                <span class="summary-total">Total: ₹{{plan.price}}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="glass-card">
                        <div class="glass-header">
                            <h4><i class="fas fa-calculator me-2"></i>Order Summary</h4>
                        </div>
                        <div class="glass-body">
                            <div class="pricing-details">
                                <div class="pricing-row">
                                    <span class="pricing-label">Subtotal:</span>
                                    <span class="pricing-value">₹{{plan.price}}</span>
                                </div>
                                <div class="pricing-row">
                                    <span class="pricing-label">
                                        <i class="fas fa-percentage me-2"></i>App Discount:
                                    </span>
                                    <span class="pricing-value success">50% OFF</span>
                                </div>
                                <div class="pricing-row">
                                    <span class="pricing-label">
                                        <i class="fas fa-info-circle me-2"></i>Tax:
                                    </span>
                                    <span class="pricing-value">₹0</span>
                                </div>
                                <div class="pricing-row">
                                    <span class="pricing-label">Order Total:</span>
                                    <span class="pricing-value">₹{{plan.price}}</span>
                                </div>
                                <div class="pricing-row">
                                    <span class="pricing-label"><strong>Grand Total:</strong></span>
                                    <span class="pricing-value"><strong>₹{{plan.price}}</strong></span>
                                </div>
                            </div>

                            <!-- Razorpay Payment Form -->
                            <form action="{% url 'sms_verify_payment' %}" method="post" id="razorpay-form">
                                {% csrf_token %}
                                <button id="pay-button" type="button" class="checkout-btn">
                                    <i class="fas fa-credit-card me-2"></i>
                                    Proceed to Payment
                                </button>

                                <!-- Hidden Fields to Store Payment Information -->
                                <input type="hidden" name="plan_id" value="{{ plan.id }}">
                                <input type="hidden" name="razorpay_order_id" id="razorpay_order_id">
                                <input type="hidden" name="razorpay_payment_id" id="razorpay_payment_id">
                                <input type="hidden" name="razorpay_signature" id="razorpay_signature">
                            </form>
                        </div>
                    </div>

                    <!-- Tax Information Card -->
                    <div class="glass-card">
                        <div class="glass-body">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-info-circle text-info me-3 mt-1"></i>
                                <div>
                                    <h6 class="mb-2"><strong>Tax Information</strong></h6>
                                    <p class="mb-0 small text-muted">
                                        Please note that our business turnover does not exceed the specified limit for GST registration, hence GST is not applicable to our products/services.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Razorpay Checkout Script -->
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>

    <!-- Bootstrap 5.3.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Modern Razorpay Integration
        document.addEventListener('DOMContentLoaded', function() {
            const payButton = document.getElementById('pay-button');

            payButton.addEventListener('click', function(e) {
                e.preventDefault();

                // Add loading state
                payButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                payButton.disabled = true;

                var options = {
                    "key": "{{ razorpay_key }}",
                    "amount": "{{ amount }}",
                    "currency": "{{ currency }}",
                    "name": "Librainian",
                    "description": "Library Membership - {{ plan.name }}",
                    "image": "/static/img/librainian-logo-black-transparent.png",
                    "order_id": "{{ order_id }}",
                    "handler": function (response) {
                        // Populate hidden fields with the payment response
                        document.getElementById('razorpay_order_id').value = response.razorpay_order_id;
                        document.getElementById('razorpay_payment_id').value = response.razorpay_payment_id;
                        document.getElementById('razorpay_signature').value = response.razorpay_signature;

                        // Show success message
                        payButton.innerHTML = '<i class="fas fa-check me-2"></i>Payment Successful';
                        payButton.style.background = 'linear-gradient(135deg, var(--secondary), var(--secondary-dark))';

                        // Submit the form after a brief delay
                        setTimeout(() => {
                            document.getElementById('razorpay-form').submit();
                        }, 1000);
                    },
                    "prefill": {
                        "name": "{{ user.username }}",
                        "email": "{{ user.email }}"
                    },
                    "theme": {
                        "color": "#6366f1"
                    },
                    "modal": {
                        "ondismiss": function() {
                            // Reset button state if payment is cancelled
                            payButton.innerHTML = '<i class="fas fa-credit-card me-2"></i>Proceed to Payment';
                            payButton.disabled = false;
                        }
                    }
                };

                var rzp1 = new Razorpay(options);
                rzp1.open();
            });
        });
    </script>
</body>
</html>