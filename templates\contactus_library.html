<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>New Contact Message | {{ library_name }} | Librainian</title>
    <meta name="robots" content="noindex, nofollow">
    <link rel="icon" href="/static/img/librainian-logo-black-transparent.png" type="image/x-icon">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <style>
        /* Modern Email Template Styles */
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #10b981;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-500: #6b7280;
            --gray-700: #374151;
            --gray-900: #111827;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--gray-900);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 1rem;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: var(--white);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .email-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            padding: 2rem;
            text-align: center;
            color: var(--white);
            position: relative;
        }

        .email-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 1;
        }

        .logo img {
            width: 50px;
            height: auto;
            filter: brightness(0) invert(1);
        }

        .email-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .email-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .email-content {
            padding: 2rem;
        }

        .greeting {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 1rem;
        }

        .message-text {
            color: var(--gray-700);
            margin-bottom: 2rem;
            line-height: 1.7;
        }

        .message-card {
            background: var(--gray-50);
            border-radius: 16px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid var(--primary);
            position: relative;
            overflow: hidden;
        }

        .message-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
        }

        .message-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }

        .message-item:last-child {
            margin-bottom: 0;
        }

        .message-icon {
            width: 40px;
            height: 40px;
            background: var(--primary);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .message-icon i {
            color: var(--white);
            font-size: 1rem;
        }

        .message-info {
            flex: 1;
        }

        .message-label {
            font-weight: 600;
            color: var(--gray-900);
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }

        .message-value {
            color: var(--gray-700);
            word-break: break-word;
        }

        .message-value a {
            color: var(--primary);
            text-decoration: none;
        }

        .message-value a:hover {
            text-decoration: underline;
        }

        .closing-text {
            color: var(--gray-700);
            margin: 2rem 0 1rem;
            line-height: 1.7;
        }

        .signature {
            font-weight: 600;
            color: var(--gray-900);
        }

        .email-footer {
            background: var(--gray-100);
            padding: 1.5rem 2rem;
            text-align: center;
            border-top: 1px solid var(--gray-200);
        }

        .footer-text {
            color: var(--gray-500);
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }

        .footer-text a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .footer-text a:hover {
            text-decoration: underline;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .social-link {
            width: 40px;
            height: 40px;
            background: var(--white);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-500);
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .social-link:hover {
            background: var(--primary);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 640px) {
            body {
                padding: 1rem 0.5rem;
            }

            .email-container {
                border-radius: 16px;
            }

            .email-header,
            .email-content,
            .email-footer {
                padding: 1.5rem 1rem;
            }

            .email-title {
                font-size: 1.5rem;
            }

            .message-card {
                padding: 1rem;
            }

            .message-icon {
                width: 35px;
                height: 35px;
                margin-right: 0.75rem;
            }
        }
    </style>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="email-container">
        <!-- Email Header -->
        <div class="email-header">
            <div class="logo">
                <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo">
            </div>
            <h1 class="email-title">New Contact Message</h1>
            <p class="email-subtitle">{{ library_name }} - Library Management System</p>
        </div>

        <!-- Email Content -->
        <div class="email-content">
            <div class="greeting">
                Dear {{ library_name }} Administrator,
            </div>

            <div class="message-text">
                You have received a new contact message from your library website. Please review the details below and respond to the inquiry at your earliest convenience.
            </div>

            <!-- Message Details Card -->
            <div class="message-card">
                <div class="message-item">
                    <div class="message-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="message-info">
                        <div class="message-label">Contact Name</div>
                        <div class="message-value">{{ name }}</div>
                    </div>
                </div>

                <div class="message-item">
                    <div class="message-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="message-info">
                        <div class="message-label">Email Address</div>
                        <div class="message-value">
                            <a href="mailto:{{ email }}">{{ email }}</a>
                        </div>
                    </div>
                </div>

                <div class="message-item">
                    <div class="message-icon">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <div class="message-info">
                        <div class="message-label">Date & Time</div>
                        <div class="message-value">{% now "F j, Y, g:i a" %}</div>
                    </div>
                </div>

                <div class="message-item">
                    <div class="message-icon">
                        <i class="fas fa-comment"></i>
                    </div>
                    <div class="message-info">
                        <div class="message-label">Message</div>
                        <div class="message-value">{{ message }}</div>
                    </div>
                </div>
            </div>

            <div class="closing-text">
                If you need any additional information or have questions about this inquiry, please don't hesitate to contact the sender directly using the email address provided above.
            </div>

            <div class="signature">
                Best regards,<br>
                <strong>Librainian Team</strong>
            </div>
        </div>

        <!-- Email Footer -->
        <div class="email-footer">
            <div class="footer-text">
                &copy; {% now "Y" %} {{ library_name }} - Powered by
                <a href="https://www.librainian.com">Librainian</a>. All rights reserved.
            </div>

            <div class="social-links">
                <a href="https://www.facebook.com/librainian" class="social-link" aria-label="Facebook">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <a href="https://twitter.com/librainian_app" class="social-link" aria-label="Twitter">
                    <i class="fab fa-twitter"></i>
                </a>
                <a href="https://www.instagram.com/librainian_app" class="social-link" aria-label="Instagram">
                    <i class="fab fa-instagram"></i>
                </a>
                <a href="https://www.linkedin.com/company/librainian" class="social-link" aria-label="LinkedIn">
                    <i class="fab fa-linkedin-in"></i>
                </a>
            </div>
        </div>
    </div>
</body>
</html>
