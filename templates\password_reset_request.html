<!DOCTYPE html>
<html lang="en">
<head>
    {% load static %}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Simple Meta Tags -->
    <title>Reset Password - Librainian</title>
    <meta name="description" content="Reset your Librainian password securely. Enter your email to receive a verification code.">
    <meta name="robots" content="noindex, nofollow">

    <!-- Modern Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <!-- Icons & Manifest -->
    <link rel="icon" type="image/png" sizes="32x32" href="{% static 'img/favicon-32x32.png' %}">
    <link rel="icon" type="image/png" sizes="16x16" href="{% static 'img/favicon-16x16.png' %}">
    <meta name="theme-color" content="#6366f1">

    <!-- External Resources -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous">

    <!-- Modern Glassmorphism CSS Framework -->
    <style>
        :root {
            /* Modern Color System */
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #8b5cf6;
            --secondary: #10b981;
            --accent: #f59e0b;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;

            /* Neutral Palette */
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;

            /* Glassmorphism Effects */
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            --glass-blur: blur(20px);

            /* Modern Gradients */
            --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

            /* Spacing & Sizing */
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

            /* Transitions */
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

            /* Typography */
            --font-primary: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-display: 'Comfortaa', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: var(--font-primary);
            background: var(--gradient-hero);
            background-attachment: fixed;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            line-height: 1.7;
            color: var(--gray-900);
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        /* Enhanced background with animated particles */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* Reset Container */
        .reset-container {
            width: 100%;
            max-width: 500px;
            padding: 2rem;
        }

        .reset-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-xl);
            box-shadow:
                0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 2px 4px rgba(255, 255, 255, 0.1) inset;
            padding: 3rem 2.5rem;
            position: relative;
            overflow: hidden;
            transition: var(--transition-slow);
            animation: slideInUp 0.6s ease-out;
        }

        .reset-card:hover {
            transform: translateY(-5px);
            box-shadow:
                0 35px 60px -12px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.15) inset,
                0 2px 4px rgba(255, 255, 255, 0.15) inset;
        }

        .reset-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(99, 102, 241, 0.1) 0%,
                rgba(139, 92, 246, 0.05) 50%,
                transparent 100%);
            pointer-events: none;
            opacity: 0.8;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Security Badge */
        .security-badge {
            position: absolute;
            top: -15px;
            right: -15px;
            background: var(--gradient-secondary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius-lg);
            font-size: 0.875rem;
            font-weight: 600;
            box-shadow: var(--shadow-lg);
            animation: float 3s ease-in-out infinite;
        }

        /* Brand Logo and Title */
        .brand-logo {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .brand-logo img {
            height: 60px;
            margin-bottom: 1rem;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .brand-title {
            font-family: var(--font-display);
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(255, 255, 255, 0.3);
        }

        .brand-subtitle {
            color: var(--gray-700);
            font-weight: 500;
            margin-bottom: 0;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        }

        /* Progress Steps */
        .progress-steps {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-weight: 600;
            color: var(--gray-800);
            box-shadow: var(--shadow-sm);
        }

        .step.active {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .step i {
            font-size: 1.125rem;
        }

        /* Form Styling */
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
            display: block;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        }

        .form-control {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.4);
            border-radius: var(--border-radius);
            padding: 1rem 1.25rem;
            font-size: 1rem;
            font-weight: 500;
            color: var(--gray-900);
            transition: var(--transition);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            width: 100%;
            box-shadow:
                0 4px 6px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow:
                0 0 0 3px rgba(99, 102, 241, 0.2),
                0 4px 12px rgba(99, 102, 241, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2) inset;
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .form-control:hover {
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.25);
        }

        .form-control::placeholder {
            color: var(--gray-600);
            font-weight: 400;
        }

        /* Input Group */
        .input-group {
            position: relative;
        }

        .input-group-text {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.4);
            border-right: none;
            border-radius: var(--border-radius) 0 0 var(--border-radius);
            color: var(--gray-700);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            display: flex;
            align-items: center;
            padding: 1rem 1.25rem;
        }

        .input-group .form-control {
            border-left: none;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
        }

        .input-group .form-control:focus {
            border-left: 2px solid var(--primary);
        }

        /* Reset Button */
        .reset-btn {
            width: 100%;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: var(--border-radius-lg);
            padding: 1.25rem 2rem;
            font-size: 1.125rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            box-shadow:
                0 10px 25px rgba(99, 102, 241, 0.3),
                0 4px 12px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .reset-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .reset-btn:hover::before {
            left: 100%;
        }

        .reset-btn:hover {
            transform: translateY(-3px);
            box-shadow:
                0 15px 35px rgba(99, 102, 241, 0.4),
                0 8px 20px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.15) inset;
        }

        .reset-btn:active {
            transform: translateY(-1px);
        }

        .reset-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Loading State */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Dark Mode Loading Styles */
        body.dark-mode .spinner {
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-top: 2px solid rgba(255, 255, 255, 0.9);
        }

        /* Alert Messages */
        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
            position: relative;
            z-index: 2;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success);
            border-left: 4px solid var(--success);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.2);
            color: var(--danger);
            border-left: 4px solid var(--danger);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
        }

        .alert-info {
            background: rgba(6, 182, 212, 0.2);
            color: var(--info);
            border-left: 4px solid var(--info);
            box-shadow: 0 4px 12px rgba(6, 182, 212, 0.2);
        }

        /* Links */
        .form-link {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-link:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        /* Footer Links */
        .footer-links {
            text-align: center;
            margin-top: 2rem;
            position: relative;
            z-index: 2;
        }

        .footer-links a {
            color: var(--gray-600);
            text-decoration: none;
            font-size: 0.875rem;
            margin: 0 1rem;
            transition: var(--transition);
        }

        .footer-links a:hover {
            color: var(--primary);
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            body::before {
                animation: none;
            }

            .reset-container {
                padding: 1rem;
                max-width: 100%;
            }

            .reset-card {
                padding: 2rem 1.5rem;
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
            }

            .brand-title {
                font-size: 1.5rem;
            }

            .security-badge {
                position: relative;
                top: 0;
                right: 0;
                margin-bottom: 1rem;
                display: inline-block;
            }

            .progress-steps {
                margin-bottom: 1.5rem;
            }

            .step {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }

            .footer-links a {
                display: block;
                margin: 0.5rem 0;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .reset-card,
            .security-badge,
            body::before {
                animation: none;
            }

            .reset-btn::before {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-card">
            <!-- Security Badge -->
            <div class="security-badge">
                <i class="fas fa-shield-alt me-1"></i>
                Secure Reset
            </div>

            <!-- Brand Logo and Title -->
            <div class="brand-logo">
                <img src="{% static 'img/logo_trans_name.png' %}" alt="Librainian - Secure Password Reset" loading="eager">
                <h1 class="brand-title">Reset Your Password</h1>
                <p class="brand-subtitle">Enter your email to receive a secure verification code</p>
            </div>

            <!-- Progress Steps -->
            <div class="progress-steps">
                <div class="step active">
                    <i class="fas fa-envelope"></i>
                    <span>Enter Email</span>
                </div>
            </div>

            <!-- Alert Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}" role="alert">
                        <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Password Reset Form -->
            <form method="post" novalidate id="resetForm">
                {% csrf_token %}

                <div class="form-group">
                    <label for="email" class="form-label">
                        <i class="fas fa-envelope me-2"></i>Email Address
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-at"></i>
                        </span>
                        <input type="email"
                               class="form-control"
                               id="email"
                               name="email"
                               placeholder="Enter your registered email address"
                               required
                               autocomplete="email"
                               aria-describedby="emailHelp">
                    </div>
                    <small id="emailHelp" class="form-text text-muted mt-2">
                        <i class="fas fa-info-circle me-1"></i>
                        We'll send a verification code to this email address
                    </small>
                </div>

                <div id="error-message" class="alert alert-danger d-none" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="error-text"></span>
                </div>

                <button type="submit" class="reset-btn" id="submitBtn">
                    <span class="btn-text">
                        <i class="fas fa-paper-plane me-2"></i>
                        Send Verification Code
                    </span>
                    <span class="loading d-none">
                        <div class="spinner"></div>
                        Sending...
                    </span>
                </button>
            </form>

            <!-- Help Information -->
            <div class="alert alert-info">
                <i class="fas fa-lightbulb me-2"></i>
                <strong>Need help?</strong> Make sure to check your spam folder if you don't receive the email within a few minutes.
            </div>

            <!-- Footer Links -->
            <div class="footer-links">
                <a href="/librarian/login/" class="form-link">
                    <i class="fas fa-arrow-left me-1"></i>Back to Login
                </a>
                <a href="/" class="form-link">
                    <i class="fas fa-home me-1"></i>Home
                </a>
            </div>

            <!-- Security Notice -->
            <div class="text-center mt-3">
                <small class="text-muted">
                    <i class="fas fa-lock me-1"></i>
                    Your request is secured with 256-bit SSL encryption
                </small>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Enhanced Password Reset Functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('resetForm');
            const emailInput = document.getElementById('email');
            const submitBtn = document.getElementById('submitBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const loadingText = submitBtn.querySelector('.loading');
            const errorMessage = document.getElementById('error-message');
            const errorText = document.getElementById('error-text');

            // Enhanced email validation
            const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

            // Real-time email validation
            emailInput.addEventListener('input', function() {
                const email = this.value.trim();
                hideError();

                if (email && !emailRegex.test(email)) {
                    this.style.borderColor = 'var(--danger)';
                } else if (email) {
                    this.style.borderColor = 'var(--success)';
                } else {
                    this.style.borderColor = '';
                }
            });

            // Form submission with enhanced UX
            form.addEventListener('submit', function(event) {
                event.preventDefault();

                const email = emailInput.value.trim();

                // Validate email
                if (!email) {
                    showError('Please enter your email address.');
                    emailInput.focus();
                    return;
                }

                if (!emailRegex.test(email)) {
                    showError('Please enter a valid email address.');
                    emailInput.focus();
                    return;
                }

                // Sanitize email (remove potentially harmful characters)
                const sanitizedEmail = email.replace(/[<>'"]/g, '');
                emailInput.value = sanitizedEmail;

                // Show loading state
                showLoading();

                // Submit form after a brief delay for UX
                setTimeout(() => {
                    form.submit();
                }, 500);
            });

            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert:not(#error-message):not(.alert-info)');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    alert.style.transition = 'opacity 0.5s ease-out';
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 500);
                }, 5000);
            });

            // Helper functions
            function showError(message) {
                errorText.textContent = message;
                errorMessage.classList.remove('d-none');
                emailInput.style.borderColor = 'var(--danger)';

                // Auto-hide error after 5 seconds
                setTimeout(hideError, 5000);
            }

            function hideError() {
                errorMessage.classList.add('d-none');
                emailInput.style.borderColor = '';
            }

            function showLoading() {
                submitBtn.disabled = true;
                btnText.classList.add('d-none');
                loadingText.classList.remove('d-none');
                submitBtn.style.cursor = 'not-allowed';
            }

            // Enhanced form validation feedback
            emailInput.addEventListener('blur', function() {
                const email = this.value.trim();
                if (email && !emailRegex.test(email)) {
                    showError('Please enter a valid email address.');
                }
            });

            // Prevent multiple submissions
            let isSubmitting = false;
            form.addEventListener('submit', function(event) {
                if (isSubmitting) {
                    event.preventDefault();
                    return;
                }
                isSubmitting = true;
            });

            // Add smooth focus transitions
            emailInput.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });

            emailInput.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
