{% extends "base.html" %}

{% block title %}Visitor Management - Librainian{% endblock %}

{% block page_title %}Visitors{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Visitors</li>
{% endblock %}

{% block content %}
<div class="visitors-content fade-in">
    <!-- Alert Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert-glass alert-{{ message.tags|default:'info' }} alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close-glass" data-bs-dismiss="alert" aria-label="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Visitor Registration Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        Register New Visitor
                    </h5>
                    <p class="card-subtitle mb-0">Add visitor information to the system</p>
                </div>
                <div class="modern-card-body">
                    <form method="POST" class="visitor-form-glass">
                        {% csrf_token %}
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="visitorName" class="form-label-glass">
                                    <i class="fas fa-user me-2"></i>Visitor Name
                                </label>
                                <input type="text" class="form-control-glass" id="visitorName" name="name" required>
                            </div>
                            <div class="col-md-4">
                                <label for="visitorPhone" class="form-label-glass">
                                    <i class="fas fa-phone me-2"></i>Phone Number
                                </label>
                                <input type="tel" class="form-control-glass" id="visitorPhone" name="phone" required>
                            </div>
                            <div class="col-md-4">
                                <label for="visitorEmail" class="form-label-glass">
                                    <i class="fas fa-envelope me-2"></i>Email Address <span class="text-muted">(Optional)</span>
                                </label>
                                <input type="email" class="form-control-glass" id="visitorEmail" name="email">
                            </div>
                        </div>
                        <div class="row g-3 mt-2">
                            <div class="col-md-6">
                                <label for="visitorPurpose" class="form-label-glass">
                                    <i class="fas fa-clipboard me-2"></i>Purpose of Visit
                                </label>
                                <select class="form-control-glass" id="visitorPurpose" name="purpose" required>
                                    <option value="">Select Purpose</option>
                                    <option value="Inquiry">General Inquiry</option>
                                    <option value="Admission">Admission Inquiry</option>
                                    <option value="Meeting">Meeting</option>
                                    <option value="Support">Technical Support</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="visitorNotes" class="form-label-glass">
                                    <i class="fas fa-sticky-note me-2"></i>Additional Notes
                                </label>
                                <textarea class="form-control-glass" id="visitorNotes" name="notes" rows="4" placeholder="Optional notes about the visit..."></textarea>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12 text-end">
                                <button type="submit" class="btn-submit-visitor">
                                    <i class="fas fa-plus me-2"></i>
                                    Register Visitor
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Visitor List -->
    <div class="row">
        <div class="col-12">
            <!-- Desktop Header (hidden on mobile) -->
            <div class="desktop-header-glass d-none d-md-block mb-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            Visitor Log
                        </h5>
                        <p class="card-subtitle mb-0">Track and manage all visitor entries</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="visitor-count-badge">
                            <i class="fas fa-eye me-1"></i>
                            {{ visitors|length|default:0 }} Today
                        </div>
                    </div>
                </div>
            </div>

            <!-- Desktop Table View -->
            <div class="d-none d-md-block">
                <!-- Desktop Search and Controls -->
                <div class="search-pagination-controls mb-3">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="search-box-glass">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="desktopSearchInput" class="form-control search-input-glass" placeholder="Search visitors..." onkeyup="filterVisitors('desktop')">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="items-per-page-glass">
                                <label class="form-label-glass me-2">Show:</label>
                                <select id="desktopItemsPerPage" class="form-select-glass" onchange="changeItemsPerPage('desktop')">
                                    <option value="5">5 per page</option>
                                    <option value="10" selected>10 per page</option>
                                    <option value="25">25 per page</option>
                                    <option value="50">50 per page</option>
                                    <option value="100">100 per page</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="pagination-info-glass">
                                <span id="desktopResultsInfo">Showing all visitors</span>
                            </div>
                        </div>
                    </div>
                </div>
                        <div class="table-responsive">
                            <table class="table-glass table-hover">
                                <thead>
                                    <tr>
                                        <th>Visitor Details</th>
                                        <th>Contact</th>
                                        <th>Notes</th>
                                        <th>Registration Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for visitor in visitors %}
                                    <tr class="visitor-row-glass" data-visitor-id="{{ visitor.id }}" data-visitor-slug="{{ visitor.slug }}">
                                        <td>
                                            <div class="visitor-info-glass">
                                                <div class="visitor-avatar-glass">
                                                    {{ visitor.name|first|upper }}
                                                </div>
                                                <div class="visitor-details-glass">
                                                    <div class="visitor-name-glass">{{ visitor.name }}</div>
                                                    <div class="visitor-id-glass">ID: #{{ visitor.id }}</div>
                                                    <div class="visitor-status-desktop mt-1">
                                                        <span class="status-badge-visitor status-{{ visitor.status|lower }}">
                                                            <i class="fas fa-{% if visitor.status == 'Checked In' %}sign-in-alt{% elif visitor.status == 'Checked Out' %}sign-out-alt{% else %}clock{% endif %} me-1"></i>
                                                            {{ visitor.status|default:"Waiting" }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="contact-info-visitor">
                                                <div class="contact-item-visitor">
                                                    <i class="fas fa-phone me-2"></i>
                                                    {{ visitor.contact }}
                                                    <a href="tel:{{ visitor.contact }}" class="contact-link-visitor">
                                                        <i class="fas fa-phone-alt"></i>
                                                    </a>
                                                </div>
                                                {% if visitor.email %}
                                                <div class="contact-item-visitor">
                                                    <i class="fas fa-envelope me-2"></i>
                                                    {{ visitor.email }}
                                                    <a href="mailto:{{ visitor.email }}" class="contact-link-visitor">
                                                        <i class="fas fa-envelope"></i>
                                                    </a>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="purpose-notes-visitor">
                                                {% if visitor.notes %}
                                                    <div class="notes-preview">
                                                        <i class="fas fa-sticky-note me-1"></i>
                                                        {{ visitor.notes }}
                                                    </div>
                                                {% else %}
                                                    <span class="text-muted">
                                                        <i class="fas fa-minus me-1"></i>
                                                        No notes
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="visit-time-visitor">
                                                <div class="visit-date">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    {{ visitor.date|date:"d/m/Y" }}
                                                </div>
                                                <div class="visit-time">
                                                    <i class="fas fa-user-plus me-1"></i>
                                                    ID: {{ visitor.inqid|default:"Pending" }}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="action-buttons-visitor">
                                                {% if visitor.status != 'Checked Out' %}
                                                <button class="btn-action-checkin" onclick="updateVisitorStatus({{ visitor.id }}, 'Checked In')" title="Check In">
                                                    <i class="fas fa-sign-in-alt"></i>
                                                </button>
                                                <button class="btn-action-checkout" onclick="updateVisitorStatus({{ visitor.id }}, 'Checked Out')" title="Check Out">
                                                    <i class="fas fa-sign-out-alt"></i>
                                                </button>
                                                {% endif %}
                                                <button class="btn-action-view" onclick="viewVisitor({{ visitor.id }})" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn-action-delete" onclick="deleteVisitor({{ visitor.id }})" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="empty-state-visitor">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <h5>No Visitors Today</h5>
                                                <p>No visitors have been registered today.</p>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <!-- Desktop Pagination -->
                        <div class="pagination-controls-glass mt-3">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="pagination-info-glass">
                                        <span id="desktopResultsInfo">Showing all visitors</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <nav aria-label="Desktop pagination">
                                        <ul class="pagination pagination-glass justify-content-end mb-0" id="desktopPagination">
                                            <!-- Pagination buttons will be generated by JavaScript -->
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>

            <!-- Mobile Card View (outside container) -->
            <div class="d-block d-md-none px-2">
                <!-- Mobile Search and Controls -->
                <div class="mobile-search-pagination mb-3">
                    <div class="search-box-glass mb-2">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="mobileSearchInput" class="form-control search-input-glass" placeholder="Search visitors..." onkeyup="filterVisitors('mobile')">
                    </div>
                    <div class="row align-items-center mb-2">
                        <div class="col-6">
                            <div class="items-per-page-glass">
                                <label class="form-label-glass me-2">Show:</label>
                                <select id="mobileItemsPerPage" class="form-select-glass" onchange="changeItemsPerPage('mobile')">
                                    <option value="5">5</option>
                                    <option value="10" selected>10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-6 text-end">
                            <div class="pagination-info-glass">
                                <span id="mobileResultsInfo">Showing all visitors</span>
                            </div>
                        </div>
                    </div>
                </div>
                        {% for visitor in visitors %}
                        <div class="visitor-card-mobile-glass mb-2 slide-up" data-visitor-id="{{ visitor.id }}" data-visitor-slug="{{ visitor.slug }}" style="animation-delay: {{ forloop.counter0|add:1|floatformat:1 }}s">
                            <div class="visitor-card-body-glass p-3">
                                <div class="visitor-header-mobile">
                                    <div class="visitor-avatar-mobile-glass">
                                        {{ visitor.name|first|upper }}
                                    </div>
                                    <div class="visitor-info-mobile">
                                        <h6 class="visitor-name-mobile-glass mb-1">{{ visitor.name }}</h6>
                                        <span class="visitor-id-mobile-glass">ID: #{{ visitor.id }}</span>
                                    </div>
                                    <div class="visitor-status-mobile">
                                        <span class="status-badge-mobile status-{{ visitor.status|lower }}">
                                            <i class="fas fa-{% if visitor.status == 'Checked In' %}sign-in-alt{% elif visitor.status == 'Checked Out' %}sign-out-alt{% else %}clock{% endif %} me-1"></i>
                                            {{ visitor.status|default:"Waiting" }}
                                        </span>
                                    </div>
                                </div>

                                <div class="visitor-details-mobile mt-2">
                                    <div class="contact-section-mobile mb-2">
                                        <div class="contact-item-mobile mb-1">
                                            <i class="fas fa-phone me-2"></i>
                                            <span>{{ visitor.contact }}</span>
                                            <a href="tel:{{ visitor.contact }}" class="contact-action-mobile">
                                                <i class="fas fa-phone-alt"></i>
                                            </a>
                                        </div>
                                        {% if visitor.email %}
                                        <div class="contact-item-mobile">
                                            <i class="fas fa-envelope me-2"></i>
                                            <span class="email-mobile-text">{{ visitor.email }}</span>
                                            <a href="mailto:{{ visitor.email }}" class="contact-action-mobile">
                                                <i class="fas fa-envelope"></i>
                                            </a>
                                        </div>
                                        {% endif %}
                                    </div>

                                    {% if visitor.notes %}
                                    <div class="notes-section-mobile mb-2">
                                        <div class="notes-preview-mobile">
                                            <i class="fas fa-sticky-note me-2"></i>
                                            <span>{{ visitor.notes|truncatewords:8 }}</span>
                                            {% if visitor.notes|wordcount > 8 %}...{% endif %}
                                        </div>
                                    </div>
                                    {% endif %}

                                    <div class="time-section-mobile">
                                        <div class="time-item-mobile mb-1">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ visitor.date|date:"d/m/Y" }}
                                        </div>
                                        <div class="time-item-mobile">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Registered on {{ visitor.date|date:"d M Y" }}
                                        </div>
                                    </div>
                                </div>

                                <div class="visitor-actions-mobile mt-2">
                                    {% if visitor.status != 'Checked Out' %}
                                    <button class="btn-mobile-action btn-checkin me-1" onclick="updateVisitorStatus({{ visitor.id }}, 'Checked In')">
                                        <i class="fas fa-sign-in-alt"></i>
                                        Check In
                                    </button>
                                    <button class="btn-mobile-action btn-checkout me-1" onclick="updateVisitorStatus({{ visitor.id }}, 'Checked Out')">
                                        <i class="fas fa-sign-out-alt"></i>
                                        Check Out
                                    </button>
                                    {% endif %}
                                    <button class="btn-mobile-action btn-view" onclick="viewVisitor({{ visitor.id }})">
                                        <i class="fas fa-eye"></i>
                                        View
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="empty-state-mobile-visitor">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <h5>No Visitors Today</h5>
                            <p>No visitors have been registered today.</p>
                        </div>
                        {% endfor %}

                        <!-- Mobile Pagination -->
                        <div class="mobile-pagination-controls mt-3">
                            <nav aria-label="Mobile pagination">
                                <ul class="pagination pagination-glass justify-content-center mb-0" id="mobilePagination">
                                    <!-- Pagination buttons will be generated by JavaScript -->
                                </ul>
                            </nav>
                        </div>
                    </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mt-4 g-3">
        <div class="col-lg-3 col-md-6">
            <div class="stats-card slide-up">
                <div class="stats-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="stats-value">{{ visitors|length|default:0 }}</h3>
                <p class="stats-label">Today's Visitors</p>
                <div class="stats-trend">
                    <small class="text-info">
                        <i class="fas fa-calendar-day me-1"></i>
                        Current day
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stats-card slide-up" style="animation-delay: 0.1s;">
                <div class="stats-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
                <h3 class="stats-value">{{ checked_in_count|default:0 }}</h3>
                <p class="stats-label">Checked In</p>
                <div class="stats-trend">
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>
                        Active visitors
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stats-card slide-up" style="animation-delay: 0.2s;">
                <div class="stats-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="stats-value">{{ waiting_count|default:0 }}</h3>
                <p class="stats-label">Waiting</p>
                <div class="stats-trend">
                    <small class="text-warning">
                        <i class="fas fa-hourglass-half me-1"></i>
                        Pending check-in
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stats-card slide-up" style="animation-delay: 0.3s;">
                <div class="stats-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <h3 class="stats-value">{{ checked_out_count|default:0 }}</h3>
                <p class="stats-label">Checked Out</p>
                <div class="stats-trend">
                    <small class="text-purple">
                        <i class="fas fa-check me-1"></i>
                        Completed visits
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Visitor List Specific Styles */
    .page-content {
        min-height: calc(100vh - 160px);
        padding: 2rem;
    }

    /* Using unified glass system from code_temp.html */
    .modern-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
    }

    .modern-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .modern-card-header {
        padding: 1.5rem 2rem 1rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
    }

    .modern-card-body {
        padding: 2rem;
        position: relative;
        z-index: 2;
    }

    .modern-card-header h5 {
        color: white;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        font-size: 1.1rem;
    }

    .card-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .visitor-count-badge {
        background: rgba(59, 130, 246, 0.3);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(59, 130, 246, 0.5);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-size: 0.875rem;
    }

    /* Form Controls */
    .form-control-glass {
        background: rgba(255, 255, 255, 0.15);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        color: white;
        padding: 0.75rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        font-size: 0.875rem;
        width: 100%;
        box-sizing: border-box;
    }

    .form-control-glass:focus {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    }

    .form-control-glass::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .form-control-glass option {
        background: rgba(102, 126, 234, 0.9);
        color: white;
    }

    .form-label-glass {
        color: white;
        font-weight: 600;
        margin-bottom: 0.5rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-size: 0.875rem;
    }

    .btn-submit-visitor {
        background: rgba(16, 185, 129, 0.3);
        border: 2px solid rgba(16, 185, 129, 0.5);
        color: white;
        padding: 1rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        width: 100%;
    }

    .btn-submit-visitor:hover {
        background: rgba(16, 185, 129, 0.5);
        border-color: rgba(16, 185, 129, 0.7);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }

    /* Table Styling */
    .table-glass {
        width: 100%;
        margin: 0;
        background: transparent;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table-glass th {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        font-weight: 700;
        border: none;
        padding: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        font-size: 0.95rem;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .table-glass th:first-child {
        min-width: 250px;
        width: 25%;
    }

    .table-glass th:nth-child(2) {
        width: 20%;
    }

    .table-glass th:nth-child(3) {
        width: 30%;
    }

    .table-glass th:nth-child(4) {
        width: 15%;
    }

    .table-glass th:last-child {
        width: 100px;
        text-align: center;
    }

    .table-glass td {
        color: white;
        border: none;
        padding: 1rem;
        vertical-align: middle;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .table-glass td:last-child {
        text-align: center;
        vertical-align: middle;
    }

    .visitor-row-glass {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .visitor-row-glass:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: scale(1.01);
    }

    /* Visitor Info */
    .visitor-info-glass {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .visitor-avatar-glass {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 800;
        font-size: 1.25rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.4);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .visitor-name-glass {
        font-weight: 700;
        font-size: 1rem;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .visitor-id-glass {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
        font-weight: 500;
    }

    /* Contact Info */
    .contact-info-visitor {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .contact-item-visitor {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.9);
    }

    .contact-link-visitor {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        padding: 0.4rem;
        border-radius: 50%;
        margin-left: auto;
        border: 2px solid #10b981;
        background: rgba(16, 185, 129, 0.1);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
    }

    .contact-link-visitor:hover {
        color: white;
        background: rgba(16, 185, 129, 0.3);
        border-color: #34d399;
        transform: scale(1.1);
    }

    /* Purpose Badges */
    .purpose-badge-visitor {
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-size: 0.85rem;
    }

    .purpose-inquiry {
        background: rgba(59, 130, 246, 0.3);
        border-color: rgba(59, 130, 246, 0.5);
        color: #3b82f6;
    }

    .purpose-admission {
        background: rgba(16, 185, 129, 0.3);
        border-color: rgba(16, 185, 129, 0.5);
        color: #10b981;
    }

    .purpose-meeting {
        background: rgba(139, 92, 246, 0.3);
        border-color: rgba(139, 92, 246, 0.5);
        color: #8b5cf6;
    }

    .purpose-support {
        background: rgba(245, 158, 11, 0.3);
        border-color: rgba(245, 158, 11, 0.5);
        color: #f59e0b;
    }

    .purpose-other {
        background: rgba(107, 114, 128, 0.3);
        border-color: rgba(107, 114, 128, 0.5);
        color: #6b7280;
    }

    /* Status Badges */
    .status-badge-visitor {
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-size: 0.85rem;
    }

    .status-waiting {
        background: rgba(245, 158, 11, 0.3);
        border-color: rgba(245, 158, 11, 0.5);
        color: #f59e0b;
    }

    .status-checked.in {
        background: rgba(16, 185, 129, 0.3);
        border-color: rgba(16, 185, 129, 0.5);
        color: #10b981;
    }

    .status-checked.out {
        background: rgba(139, 92, 246, 0.3);
        border-color: rgba(139, 92, 246, 0.5);
        color: #8b5cf6;
    }

    /* Visit Time */
    .visit-time-visitor {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .visit-date,
    .visit-time {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    /* Notes Preview */
    .notes-preview {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
        max-height: 4.2em; /* 3 lines * 1.4 line-height */
        word-wrap: break-word;
    }

    /* Action Buttons */
    .action-buttons-visitor {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        align-items: center;
        min-width: 80px;
    }

    .btn-action-checkin,
    .btn-action-checkout,
    .btn-action-view,
    .btn-action-delete {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        border: 1px solid;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }

    .btn-action-checkin {
        background: rgba(16, 185, 129, 0.3);
        border-color: rgba(16, 185, 129, 0.5);
        color: #10b981;
    }

    .btn-action-checkout {
        background: rgba(139, 92, 246, 0.3);
        border-color: rgba(139, 92, 246, 0.5);
        color: #8b5cf6;
    }

    .btn-action-view {
        background: rgba(59, 130, 246, 0.3);
        border-color: rgba(59, 130, 246, 0.5);
        color: #3b82f6;
    }

    .btn-action-delete {
        background: rgba(239, 68, 68, 0.3);
        border-color: rgba(239, 68, 68, 0.5);
        color: #ef4444;
    }

    /* Light mode action button visibility */
    [data-bs-theme="light"] .btn-action-checkin {
        background: rgba(16, 185, 129, 0.6);
        border-color: rgba(16, 185, 129, 0.8);
        color: #059669;
    }

    [data-bs-theme="light"] .btn-action-checkout {
        background: rgba(139, 92, 246, 0.6);
        border-color: rgba(139, 92, 246, 0.8);
        color: #7c3aed;
    }

    [data-bs-theme="light"] .btn-action-view {
        background: rgba(59, 130, 246, 0.6);
        border-color: rgba(59, 130, 246, 0.8);
        color: #2563eb;
    }

    [data-bs-theme="light"] .btn-action-delete {
        background: rgba(239, 68, 68, 0.6);
        border-color: rgba(239, 68, 68, 0.8);
        color: #dc2626;
    }

    .btn-action-checkin:hover,
    .btn-action-checkout:hover,
    .btn-action-view:hover,
    .btn-action-delete:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    /* Mobile Cards */
    .visitor-card-mobile-glass {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .visitor-card-mobile-glass:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .visitor-card-body-glass {
        padding: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .visitor-header-mobile {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .visitor-info-mobile {
        flex: 1;
        min-width: 0; /* Allow text to wrap */
    }

    .visitor-name-mobile-glass {
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
    }

    .visitor-avatar-mobile-glass {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 800;
        font-size: 1.25rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.4);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .visitor-name-mobile-glass {
        color: white;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        font-size: 1.1rem;
    }

    .visitor-id-mobile-glass {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
        font-weight: 500;
    }

    .visitor-status-mobile {
        flex-shrink: 0; /* Prevent status from shrinking */
        margin-left: auto;
    }

    .status-badge-mobile {
        padding: 0.25rem 0.75rem;
        border-radius: 8px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-size: 0.75rem;
        white-space: nowrap; /* Prevent status text from wrapping */
    }

    .visitor-details-mobile {
        margin-bottom: 1rem;
    }

    .contact-section-mobile {
        margin-bottom: 1rem;
    }

    .contact-item-mobile {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.9);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .contact-item-mobile i {
        width: 20px;
        color: rgba(255, 255, 255, 0.7);
    }

    .email-mobile-text {
        max-width: 180px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .contact-action-mobile {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        margin-left: auto;
        transition: all 0.3s ease;
        padding: 0.4rem;
        border-radius: 50%;
        border: 2px solid #10b981;
        background: rgba(16, 185, 129, 0.1);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
    }

    .contact-action-mobile:hover {
        color: white;
        background: rgba(16, 185, 129, 0.3);
        border-color: #34d399;
        transform: scale(1.1);
    }

    .purpose-section-mobile {
        margin-bottom: 1rem;
    }

    .purpose-badge-mobile {
        padding: 0.25rem 0.75rem;
        border-radius: 8px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-size: 0.75rem;
    }

    .time-section-mobile {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .time-item-mobile {
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .notes-section-mobile {
        margin-bottom: 1rem;
    }

    .notes-preview-mobile {
        color: white;
        font-size: 0.875rem;
        line-height: 1.4;
    }

    .notes-preview-mobile span {
        color: rgba(255, 255, 255, 0.9);
    }

    .visitor-actions-mobile {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .btn-mobile-action {
        flex: 1;
        min-width: 80px;
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        border: 1px solid;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 0.75rem;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .btn-checkin {
        background: rgba(16, 185, 129, 0.3);
        border-color: rgba(16, 185, 129, 0.5);
        color: #10b981;
    }

    .btn-checkout {
        background: rgba(139, 92, 246, 0.3);
        border-color: rgba(139, 92, 246, 0.5);
        color: #8b5cf6;
    }

    .btn-view {
        background: rgba(59, 130, 246, 0.3);
        border-color: rgba(59, 130, 246, 0.5);
        color: #3b82f6;
    }

    .btn-mobile-action:hover {
        transform: translateY(-1px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    /* Stats Cards, Alerts, and Empty States */
    .stats-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .stats-value {
        font-size: 2rem;
        font-weight: 800;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        margin: 0;
    }

    .stats-label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
        margin: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .empty-state-visitor,
    .empty-state-mobile-visitor {
        color: rgba(255, 255, 255, 0.8);
        text-align: center;
        padding: 3rem 1rem;
    }

    .empty-state-visitor i,
    .empty-state-mobile-visitor i {
        color: rgba(255, 255, 255, 0.5);
        margin-bottom: 1rem;
    }

    .empty-state-visitor h5,
    .empty-state-mobile-visitor h5 {
        color: white;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .alert-glass {
        background: rgba(255, 255, 255, 0.15);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        color: white;
        padding: 1rem 1.5rem;
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
    }

    .btn-close-glass {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        color: white;
        padding: 0.5rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        cursor: pointer;
        width: 32px;
        height: 32px;
    }

    @media (max-width: 767.98px) {
        .page-content {
            padding: 1rem 0.5rem;
        }

        .modern-card-body {
            padding: 1.5rem;
        }

        .visitor-avatar-mobile-glass {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .visitor-card-mobile-glass {
            margin: 0 0.5rem 1rem 0.5rem;
            border-radius: 12px;
        }

        .visitor-card-body-glass {
            padding: 12px;
        }

        .visitor-header-mobile {
            margin-bottom: 0.75rem;
        }

        .visitor-details-mobile {
            margin-bottom: 0.75rem;
        }

        .contact-section-mobile {
            margin-bottom: 0.5rem;
        }

        .contact-item-mobile {
            margin-bottom: 0.25rem;
            font-size: 0.8rem;
        }

        .time-section-mobile {
            margin-bottom: 0.5rem;
        }

        .visitor-actions-mobile {
            margin-top: 0.75rem;
        }

        .btn-mobile-action {
            padding: 0.4rem 0.6rem;
            font-size: 0.7rem;
        }
    }

    /* Search and Pagination Styles */
    .search-box-glass {
        position: relative;
        margin-bottom: 1rem;
    }

    .search-input-glass {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 25px;
        padding: 0.75rem 1rem 0.75rem 3rem;
        color: white;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .search-input-glass:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        color: white;
    }

    .search-input-glass::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: rgba(255, 255, 255, 0.7);
        z-index: 10;
    }

    .pagination-info-glass {
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Light mode pagination info visibility */
    [data-bs-theme="light"] .pagination-info-glass {
        color: rgba(0, 0, 0, 0.8);
    }

    .pagination-glass .page-link {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        margin: 0 0.25rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .pagination-glass .page-link:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
        color: white;
        transform: translateY(-2px);
    }

    .pagination-glass .page-item.active .page-link {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
    }

    .pagination-controls-glass {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-search-pagination {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-pagination-controls {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        margin: 1.5rem 0.5rem 0 0.5rem;
    }

    /* Desktop Header */
    .desktop-header-glass {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        padding: 1.5rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .desktop-header-glass h5 {
        color: white;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .desktop-header-glass .card-subtitle {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
    }

    /* Items Per Page Selector */
    .items-per-page-glass {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-label-glass {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0;
        white-space: nowrap;
    }

    .form-select-glass {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        color: white;
        padding: 0.5rem 0.75rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .form-select-glass:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        color: white;
    }

    .form-select-glass option {
        background: rgba(30, 30, 30, 0.95);
        color: white;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add staggered animation delays
        const cards = document.querySelectorAll('.slide-up');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${(index + 1) * 0.1}s`;
        });

        // Removed page load success message
    });

    function updateVisitorStatus(visitorId, status) {
        // Show confirmation dialog
        const confirmMessage = `Are you sure you want to mark this visitor as ${status}?`;
        if (!confirm(confirmMessage)) {
            return;
        }

        // Make AJAX request to update status
        fetch(`/visitors/update-status/${visitorId}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                status: status
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                if (window.modernDashboard) {
                    window.modernDashboard.showToast(
                        'Status Updated!',
                        `Visitor has been marked as ${status}.`,
                        'success'
                    );
                }
                // Reload page to reflect changes
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                // Show error message
                if (window.modernDashboard) {
                    window.modernDashboard.showToast(
                        'Update Failed!',
                        data.message || 'Failed to update visitor status.',
                        'error'
                    );
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Network Error!',
                    'Failed to connect to server.',
                    'error'
                );
            }
        });
    }

    function viewVisitor(visitorId) {
        // Find visitor slug from the table data
        const visitorRow = document.querySelector(`[data-visitor-id="${visitorId}"]`);
        if (visitorRow) {
            const visitorSlug = visitorRow.getAttribute('data-visitor-slug');
            if (visitorSlug) {
                window.location.href = `/visitors/${visitorSlug}/`;
                return;
            }
        }

        // Fallback: try to get visitor data via AJAX
        fetch(`/visitors/get-visitor-slug/${visitorId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.slug) {
                    window.location.href = `/visitors/${data.slug}/`;
                } else {
                    alert('Unable to view visitor details. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Unable to view visitor details. Please try again.');
            });
    }

    function deleteVisitor(visitorId) {
        // Show confirmation dialog
        if (!confirm('Are you sure you want to delete this visitor? This action cannot be undone.')) {
            return;
        }

        // Make AJAX request to delete visitor
        fetch(`/visitors/delete-visitor/${visitorId}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                if (window.modernDashboard) {
                    window.modernDashboard.showToast(
                        'Visitor Deleted!',
                        'Visitor has been successfully removed.',
                        'success'
                    );
                }
                // Reload page to reflect changes
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                // Show error message
                if (window.modernDashboard) {
                    window.modernDashboard.showToast(
                        'Delete Failed!',
                        data.message || 'Failed to delete visitor.',
                        'error'
                    );
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Network Error!',
                    'Failed to connect to server.',
                    'error'
                );
            }
        });
    }

    // Form validation and enhancement
    document.querySelector('.visitor-form-glass')?.addEventListener('submit', function(e) {
        const name = document.getElementById('visitorName').value.trim();
        const phone = document.getElementById('visitorPhone').value.trim();
        const purpose = document.getElementById('visitorPurpose').value;

        if (!name || !phone || !purpose) {
            e.preventDefault();
            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Validation Error!',
                    'Please fill in all required fields.',
                    'error'
                );
            }
            return;
        }

        // Phone number validation
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(phone)) {
            e.preventDefault();
            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Invalid Phone Number!',
                    'Please enter a valid phone number.',
                    'error'
                );
            }
            return;
        }

        // Show loading state
        const submitBtn = this.querySelector('.btn-submit-visitor');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Registering...';
        submitBtn.disabled = true;

        // Reset button after 3 seconds if form doesn't submit
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });

    // Search and Pagination Functions
    let currentPage = 1;
    let itemsPerPage = 10;
    let allVisitors = [];
    let filteredVisitors = [];
    let currentViewType = 'desktop';

    // Initialize visitors data
    document.addEventListener('DOMContentLoaded', function() {
        // Get all visitor rows/cards
        const desktopRows = document.querySelectorAll('.visitor-row-glass');
        const mobileCards = document.querySelectorAll('.visitor-card-mobile-glass');

        allVisitors = Array.from(desktopRows).map((row, index) => ({
            desktop: row,
            mobile: mobileCards[index],
            text: row.textContent.toLowerCase()
        }));

        filteredVisitors = [...allVisitors];
        updatePagination('desktop');
        updatePagination('mobile');
    });

    function filterVisitors(viewType) {
        const searchInput = document.getElementById(viewType + 'SearchInput');
        const searchTerm = searchInput.value.toLowerCase();

        filteredVisitors = allVisitors.filter(visitor =>
            visitor.text.includes(searchTerm)
        );

        currentPage = 1;
        currentViewType = viewType;
        updatePagination(viewType);
        displayVisitors(viewType);
    }

    function changeItemsPerPage(viewType) {
        const selector = document.getElementById(viewType + 'ItemsPerPage');
        itemsPerPage = parseInt(selector.value);
        currentPage = 1;
        currentViewType = viewType;
        updatePagination(viewType);
        displayVisitors(viewType);
    }

    function updatePagination(viewType) {
        const totalItems = filteredVisitors.length;
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        const startItem = (currentPage - 1) * itemsPerPage + 1;
        const endItem = Math.min(currentPage * itemsPerPage, totalItems);

        // Update info text
        const infoElement = document.getElementById(viewType + 'ResultsInfo');
        if (totalItems === 0) {
            infoElement.textContent = 'No visitors found';
        } else {
            infoElement.textContent = `Showing ${startItem}-${endItem} of ${totalItems} visitors`;
        }

        // Update pagination buttons
        const paginationElement = document.getElementById(viewType + 'Pagination');
        paginationElement.innerHTML = '';

        if (totalPages > 1) {
            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            const prevLink = document.createElement('a');
            prevLink.className = 'page-link';
            prevLink.href = '#';
            prevLink.innerHTML = '<i class="fas fa-chevron-left me-1"></i>Prev';
            if (currentPage > 1) {
                prevLink.onclick = (e) => { e.preventDefault(); changePage(currentPage - 1, viewType); };
            } else {
                prevLink.onclick = (e) => { e.preventDefault(); };
            }
            prevLi.appendChild(prevLink);
            paginationElement.appendChild(prevLi);

            // Page numbers (show fewer on mobile)
            const maxVisiblePages = viewType === 'mobile' ? 3 : 5;
            const halfVisible = Math.floor(maxVisiblePages / 2);
            let startPage = Math.max(1, currentPage - halfVisible);
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            // Adjust start if we're near the end
            if (endPage - startPage < maxVisiblePages - 1) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // First page and ellipsis
            if (startPage > 1) {
                const firstLi = document.createElement('li');
                firstLi.className = 'page-item';
                const firstLink = document.createElement('a');
                firstLink.className = 'page-link';
                firstLink.href = '#';
                firstLink.textContent = '1';
                firstLink.onclick = (e) => { e.preventDefault(); changePage(1, viewType); };
                firstLi.appendChild(firstLink);
                paginationElement.appendChild(firstLi);

                if (startPage > 2) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'page-item disabled';
                    ellipsisLi.innerHTML = '<span class="page-link">...</span>';
                    paginationElement.appendChild(ellipsisLi);
                }
            }

            // Page numbers
            for (let i = startPage; i <= endPage; i++) {
                const pageLi = document.createElement('li');
                pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
                const pageLink = document.createElement('a');
                pageLink.className = 'page-link';
                pageLink.href = '#';
                pageLink.textContent = i;
                pageLink.onclick = (e) => { e.preventDefault(); changePage(i, viewType); };
                pageLi.appendChild(pageLink);
                paginationElement.appendChild(pageLi);
            }

            // Last page and ellipsis
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'page-item disabled';
                    ellipsisLi.innerHTML = '<span class="page-link">...</span>';
                    paginationElement.appendChild(ellipsisLi);
                }

                const lastLi = document.createElement('li');
                lastLi.className = 'page-item';
                const lastLink = document.createElement('a');
                lastLink.className = 'page-link';
                lastLink.href = '#';
                lastLink.textContent = totalPages;
                lastLink.onclick = (e) => { e.preventDefault(); changePage(totalPages, viewType); };
                lastLi.appendChild(lastLink);
                paginationElement.appendChild(lastLi);
            }

            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            const nextLink = document.createElement('a');
            nextLink.className = 'page-link';
            nextLink.href = '#';
            nextLink.innerHTML = 'Next<i class="fas fa-chevron-right ms-1"></i>';
            if (currentPage < totalPages) {
                nextLink.onclick = (e) => { e.preventDefault(); changePage(currentPage + 1, viewType); };
            } else {
                nextLink.onclick = (e) => { e.preventDefault(); };
            }
            nextLi.appendChild(nextLink);
            paginationElement.appendChild(nextLi);
        }
    }

    function changePage(page, viewType) {
        const totalPages = Math.ceil(filteredVisitors.length / itemsPerPage);
        if (page < 1 || page > totalPages) return;

        currentPage = page;
        updatePagination(viewType);
        displayVisitors(viewType);
    }

    function displayVisitors(viewType) {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;

        filteredVisitors.forEach((visitor, index) => {
            const shouldShow = index >= startIndex && index < endIndex;

            if (viewType === 'desktop') {
                visitor.desktop.style.display = shouldShow ? '' : 'none';
            } else {
                visitor.mobile.style.display = shouldShow ? '' : 'none';
            }
        });

        // Hide visitors not in filtered results
        allVisitors.forEach(visitor => {
            if (!filteredVisitors.includes(visitor)) {
                if (viewType === 'desktop') {
                    visitor.desktop.style.display = 'none';
                } else {
                    visitor.mobile.style.display = 'none';
                }
            }
        });
    }
</script>
{% endblock %}