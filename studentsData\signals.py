from django.db.models.signals import pre_save
from django.dispatch import receiver
from django.utils import timezone
from datetime import datetime
from studentsData.models import StudentData


@receiver(pre_save, sender=StudentData)
def set_student_due_color(sender, instance, **kwargs):
    if instance.pk:
        try:
            today = timezone.now().date()
            latest_invoice = instance.invoice_set.order_by("-due_date").first()
        except instance.DoesNotExist:
            latest_invoice = None
    else:
        latest_invoice = None

    if latest_invoice and latest_invoice.due_date:
        due_date = latest_invoice.due_date

        # Check if due_date is a string or a date object
        if isinstance(due_date, str):
            try:
                due_date = datetime.strptime(due_date, "%Y-%m-%d").date()
            except ValueError:
                # Handle the case where the date string is not in the expected format
                due_date = today
        elif isinstance(due_date, datetime):
            due_date = due_date.date()

        delta = (due_date - today).days

        # Determine color based on the difference in days
        if delta <= -10:
            instance.color = "dark-grey"
        elif 1 <= delta <= 6:
            instance.color = "blue"
        elif delta == 0:
            instance.color = "green"
        elif -9 <= delta <= -1:
            instance.color = "red"
        else:
            instance.color = "white"
    else:
        instance.color = "white"
