<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="robots" content="noindex, nofollow">
    <meta name="google" content="notranslate">
    <title>Exclusive Offer | Librainian Marketing</title>

    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --text-color: #1e293b;
            --text-light: #6b7280;
            --text-white: #ffffff;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 10px 20px rgba(0, 0, 0, 0.2);
            --shadow-glass: 0 8px 32px rgba(31, 38, 135, 0.37);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            padding: 2rem 1rem;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-gradient);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Email container with glass effect */
        .email-container {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: var(--border-radius);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-glass);
            max-width: 650px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
            animation: slideUp 0.8s ease-out;
        }

        .email-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        /* Header section */
        .email-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            padding: 2.5rem 2rem;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .email-header h2 {
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }

        /* Body section */
        .email-body {
            padding: 2rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(8px);
            position: relative;
            z-index: 1;
        }

        .email-body p {
            font-size: 1rem;
            line-height: 1.6;
            color: var(--text-color);
            margin-bottom: 1.25rem;
        }

        .email-body strong {
            color: var(--secondary-color);
            font-weight: 600;
        }

        /* Offer details card */
        .offer-details {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #334155 100%);
            color: var(--text-white);
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
            box-shadow: var(--shadow-md);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .offer-details p {
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .offer-details strong {
            color: var(--text-white);
            font-weight: 600;
        }

        /* CTA Button */
        .btn_theme {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: var(--text-white);
            border: none;
            border-radius: 50px;
            padding: 1rem 2.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            margin: 1.5rem 0;
        }

        .btn_theme:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: var(--text-white);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        /* Footer section */
        .email-footer {
            background: rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .email-footer p {
            font-size: 0.9rem;
            color: var(--text-light);
            margin: 0;
            font-weight: 500;
            opacity: 0.8;
        }

        /* Animations */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            body {
                padding: 1rem 0.5rem;
            }

            .email-container {
                max-width: 100%;
                margin: 0;
                border-radius: 12px;
            }

            .email-header {
                padding: 2rem 1.5rem;
            }

            .email-header h2 {
                font-size: 1.6rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .email-body {
                padding: 1.5rem;
            }

            .offer-details {
                padding: 1.5rem;
                margin: 1.5rem 0;
            }

            .btn_theme {
                width: 100%;
                justify-content: center;
                padding: 1rem 2rem;
            }
        }

        @media (max-width: 480px) {
            .email-header h2 {
                font-size: 1.4rem;
            }

            .offer-details p {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>

<body>

    <div class="email-container">
        <div class="email-header">
            <h2>
                <i class="fas fa-gift"></i>
                Exclusive Offer Just for You!
            </h2>
        </div>

        <div class="email-body">
            <p>Dear <strong>[Student Name]</strong>,</p>

            <p>🎉 We are excited to offer you an <strong>exclusive discount</strong> on your course registration! This is a limited-time opportunity designed specifically for students like you.</p>

            <div class="offer-details">
                <p>
                    <i class="fas fa-star"></i>
                    <strong>Special Offer Details:</strong>
                </p>
                <p>
                    <i class="fas fa-id-card"></i>
                    <strong>Registration Number:</strong> [Registration Number]
                </p>
                <p>
                    <i class="fas fa-money-bill-wave"></i>
                    <strong>Offer Amount:</strong> [Offer Amount]
                </p>
                <p>
                    <i class="fas fa-ticket-alt"></i>
                    <strong>Coupon Code:</strong> [Coupon Code]
                </p>
                <p>
                    <i class="fas fa-calendar-alt"></i>
                    <strong>Expiry Date:</strong> [Expiry Date]
                </p>
            </div>
            <p>To take advantage of this limited-time offer, simply enter the coupon code during registration. Don’t
                miss out on this opportunity to save on your educational journey!</p>
            <p>Ready to get started? Click the button below to redeem your exclusive offer:</p>

            <div class="text-center">
                <a href="#" class="btn btn_theme" role="button" aria-label="Redeem your exclusive offer">
                    <i class="fas fa-rocket"></i>
                    Redeem Offer Now
                </a>
            </div>

            <p>If you have any questions or need assistance, feel free to reach out to our dedicated support team. We're here to help you succeed!</p>

            <p>Best regards,<br>
                <strong>The Librainian Team</strong><br>
                <em>Your Partner in Educational Excellence</em>
            </p>
        </div>
    </div>

        <div class="email-footer">
            <p>
                <i class="fas fa-shield-alt me-2"></i>
                <strong>LIBRAINIAN</strong> - Secure Library Management System
            </p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced email functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Track email view
            if (typeof gtag === 'function') {
                gtag('event', 'marketing_email_view', {
                    'event_category': 'Marketing',
                    'event_label': 'Exclusive Offer Email'
                });
            }

            // Add click tracking to CTA button
            const ctaButton = document.querySelector('.btn_theme');
            if (ctaButton) {
                ctaButton.addEventListener('click', function(e) {
                    // Track CTA click
                    if (typeof gtag === 'function') {
                        gtag('event', 'cta_click', {
                            'event_category': 'Marketing',
                            'event_label': 'Redeem Offer Button'
                        });
                    }
                });
            }
        });
    </script>
</body>

</html>