{% extends 'base.html' %}
{% load static %}

{% block title %}Create Merged Shift{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Create Merged Shift</h3>
                    <div class="card-tools">
                        <a href="{% url 'merged_shifts_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" id="mergedShiftForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Merged Shift Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           placeholder="e.g., Morning + Evening Combo" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="price">Price (₹) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="price" name="price" 
                                           step="0.01" min="0" placeholder="0.00" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="discount">Discount (%)</label>
                                    <input type="number" class="form-control" id="discount" name="discount" 
                                           step="0.01" min="0" max="100" placeholder="0.00" value="0">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Select Shifts to Merge <span class="text-danger">*</span></label>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                Select multiple shifts to create a merged shift. Only seats available in ALL selected shifts will be bookable.
                                Overnight shifts (e.g., 22:00 - 06:00) are fully supported.
                            </div>
                            
                            {% if shifts %}
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th width="50">Select</th>
                                                <th>Shift Name</th>
                                                <th>Time Range</th>
                                                <th>Duration</th>
                                                <th>Price</th>
                                                <th>Type</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for shift in shifts %}
                                            <tr>
                                                <td>
                                                    <input type="checkbox" name="shifts" value="{{ shift.id }}" 
                                                           class="shift-checkbox" id="shift_{{ shift.id }}">
                                                </td>
                                                <td>
                                                    <label for="shift_{{ shift.id }}" class="mb-0">
                                                        {{ shift.name }}
                                                    </label>
                                                </td>
                                                <td>
                                                    <span class="badge {% if shift.is_overnight_shift %}badge-warning{% else %}badge-info{% endif %}">
                                                        {{ shift.formatted_time_range }}
                                                    </span>
                                                </td>
                                                <td>{{ shift.get_duration_hours|floatformat:1 }} hrs</td>
                                                <td>₹{{ shift.price }}</td>
                                                <td>
                                                    {% if shift.is_overnight_shift %}
                                                        <span class="badge badge-warning">Overnight</span>
                                                    {% else %}
                                                        <span class="badge badge-success">Regular</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    No shifts available. Please create some shifts first.
                                </div>
                            {% endif %}
                        </div>

                        <div id="selectedShiftsPreview" class="form-group" style="display: none;">
                            <label>Selected Shifts Preview</label>
                            <div id="previewContent" class="alert alert-light border">
                                <!-- Preview content will be populated by JavaScript -->
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-plus"></i> Create Merged Shift
                            </button>
                            <a href="{% url 'merged_shifts_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.shift-checkbox');
    const previewDiv = document.getElementById('selectedShiftsPreview');
    const previewContent = document.getElementById('previewContent');
    const form = document.getElementById('mergedShiftForm');
    const submitBtn = document.getElementById('submitBtn');

    // Update preview when checkboxes change
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updatePreview);
    });

    function updatePreview() {
        const selected = Array.from(checkboxes).filter(cb => cb.checked);
        
        if (selected.length === 0) {
            previewDiv.style.display = 'none';
            return;
        }

        let html = '<h6>Selected Shifts:</h6><ul class="mb-0">';
        let totalDuration = 0;
        let hasOvernight = false;

        selected.forEach(checkbox => {
            const row = checkbox.closest('tr');
            const name = row.cells[1].textContent.trim();
            const timeRange = row.cells[2].textContent.trim();
            const duration = parseFloat(row.cells[3].textContent);
            const isOvernight = timeRange.includes('(+1 day)');
            
            totalDuration += duration;
            if (isOvernight) hasOvernight = true;

            html += `<li><strong>${name}</strong> - ${timeRange}</li>`;
        });

        html += '</ul>';
        html += `<div class="mt-2"><small class="text-muted">Total Duration: ${totalDuration.toFixed(1)} hours`;
        if (hasOvernight) {
            html += ' <span class="badge badge-warning ml-1">Contains Overnight Shifts</span>';
        }
        html += '</small></div>';

        previewContent.innerHTML = html;
        previewDiv.style.display = 'block';
    }

    // Form validation
    form.addEventListener('submit', function(e) {
        const selectedShifts = Array.from(checkboxes).filter(cb => cb.checked);
        
        if (selectedShifts.length < 2) {
            e.preventDefault();
            alert('Please select at least 2 shifts to create a merged shift.');
            return false;
        }

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
        submitBtn.disabled = true;
    });
});
</script>

<style>
.table th {
    background-color: #f8f9fa;
}
.shift-checkbox {
    transform: scale(1.2);
}
label[for^="shift_"] {
    cursor: pointer;
}
.badge-warning {
    background-color: #ffc107;
    color: #212529;
}
</style>
{% endblock %}
