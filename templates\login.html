<!DOCTYPE html>
<html lang="en" itemscope itemtype="https://schema.org/WebPage">
<head>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5L8WZWCC');</script>
<!-- End Google Tag Manager -->
    {% load static %}
    {% load socialaccount %}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO & LLM Optimized Meta Tags -->
    <title>Secure Login - Librainian Library Management System | Access Your Dashboard</title>
    <meta name="description" content="Secure login to Librainian - the #1 library management system trusted by 600+ libraries worldwide. Access your dashboard with enterprise-grade security and manage your library operations efficiently.">
    <meta name="keywords" content="library login, secure librarian login, library management system login, library dashboard access, digital library login, library software access, secure library portal, library automation login">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="author" content="Librainian Team">
    <meta name="language" content="English">
    <link rel="canonical" href="https://www.librainian.com/librarian/login/">

    <!-- Enhanced Open Graph for Social Media -->
    <meta property="og:title" content="Secure Login - Librainian Library Management System">
    <meta property="og:description" content="Access your secure library dashboard with Librainian - trusted by 600+ libraries worldwide. Enterprise-grade security for your library operations.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.librainian.com/librarian/login/">
    <meta property="og:image" content="https://www.librainian.com/static/img/link_cover.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="Librainian Secure Login Portal">
    <meta property="og:site_name" content="Librainian">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Optimization -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:creator" content="@librainian_app">
    <meta name="twitter:title" content="Secure Login - Librainian Library Management System">
    <meta name="twitter:description" content="Access your secure library dashboard with enterprise-grade security. Trusted by 600+ libraries worldwide.">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/link_cover.jpg">
    <meta name="twitter:image:alt" content="Librainian Secure Login Portal">

    <!-- Performance & Resource Hints -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//www.google-analytics.com">

    <!-- Modern Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <!-- Icons & Manifest -->
    <link rel="icon" type="image/x-icon" href="{% static 'favicon.ico' %}">
    <link rel="icon" type="image/png" sizes="512x512" href="{% static 'img/ms-icon-512x512.png' %}">
    <link rel="apple-touch-icon" href="{% static 'img/apple-touch-icon.png' %}">
    <link rel="manifest" href="{% static 'js/manifest.json' %}">
    <meta name="theme-color" content="#6366f1" id="theme-color-meta">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default" id="status-bar-meta">

    <!-- External Resources -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous">

    <!-- WebPage Schema for Login -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Secure Login - Librainian Library Management System",
        "description": "Secure login to Librainian - the #1 library management system trusted by 600+ libraries worldwide. Access your dashboard with enterprise-grade security.",
        "url": "https://librainian.com/{% if role == 'sublibrarian' %}sublibrarian{% else %}librarian{% endif %}/login/",
        "isPartOf": {
            "@type": "WebSite",
            "name": "Librainian",
            "url": "https://librainian.com/"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://librainian.com/static/img/logo_trans_name.png",
                "width": 300,
                "height": 100
            },
            "url": "https://librainian.com/"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Librainian Login Portal",
            "description": "Secure authentication portal for library management system access",
            "applicationCategory": "Library Management",
            "operatingSystem": "Web Browser"
        },
        "potentialAction": {
            "@type": "LoginAction",
            "target": {
                "@type": "EntryPoint",
                "urlTemplate": "https://librainian.com/{% if role == 'sublibrarian' %}sublibrarian{% else %}librarian{% endif %}/login/",
                "actionPlatform": [
                    "https://schema.org/DesktopWebPlatform",
                    "https://schema.org/MobileWebPlatform"
                ]
            }
        }
    }
    </script>

    <!-- BreadcrumbList Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://librainian.com/"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "{% if role == 'sublibrarian' %}Sub-Librarian{% else %}Librarian{% endif %} Login",
                "item": "https://librainian.com/{% if role == 'sublibrarian' %}sublibrarian{% else %}librarian{% endif %}/login/"
            }
        ]
    }
    </script>

    <!-- Organization Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Librainian",
        "url": "https://librainian.com/",
        "logo": {
            "@type": "ImageObject",
            "url": "https://librainian.com/static/img/logo_trans_name.png",
            "width": 300,
            "height": 100
        },
        "description": "Leading provider of secure library management solutions trusted by 600+ libraries worldwide.",
        "sameAs": [
            "https://www.facebook.com/people/Librainian/61562707884730/",
            "https://www.instagram.com/librainian.app/",
            "https://www.linkedin.com/company/pinak-venture/",
            "https://www.youtube.com/channel/UCPf3m0ZR8XN7DVMOM6OJ8Qw"
        ],
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+91-**********",
            "contactType": "customer service",
            "availableLanguage": ["English", "Hindi"]
        }
    }
    </script>

    <!-- Enhanced JSON-LD Schema Markup for Login Page -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Secure Login - Librainian Library Management System",
        "description": "Secure login portal for Librainian - the #1 library management system trusted by 600+ libraries worldwide. Access your dashboard with enterprise-grade security.",
        "url": "https://www.librainian.com/librarian/login/",
        "inLanguage": "en-US",
        "isPartOf": {
            "@type": "WebSite",
            "name": "Librainian",
            "url": "https://www.librainian.com/",
            "potentialAction": {
                "@type": "SearchAction",
                "target": "https://www.librainian.com/search?q={search_term_string}",
                "query-input": "required name=search_term_string"
            }
        },
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://www.librainian.com/"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Login",
                    "item": "https://www.librainian.com/librarian/login/"
                }
            ]
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Librainian Login Portal",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Web Browser, iOS, Android",
            "description": "Secure login portal for accessing Librainian library management system with enterprise-grade security features.",
            "url": "https://www.librainian.com/librarian/login/",
            "potentialAction": {
                "@type": "LoginAction",
                "target": {
                    "@type": "EntryPoint",
                    "urlTemplate": "https://www.librainian.com/librarian/login/",
                    "inLanguage": "en-US",
                    "actionPlatform": [
                        "http://schema.org/DesktopWebPlatform",
                        "http://schema.org/MobileWebPlatform"
                    ]
                },
                "result": {
                    "@type": "UserAccount",
                    "description": "Access to Librainian library management dashboard"
                }
            }
        },
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "url": "https://www.librainian.com",
            "logo": {
                "@type": "ImageObject",
                "url": "https://www.librainian.com/static/img/logo_trans_name.png"
            }
        },
        "datePublished": "2024-07-01",
        "dateModified": "2024-12-15"
    }
    </script>

    <!-- Organization Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Librainian",
        "alternateName": "Librainian Library Management System",
        "url": "https://www.librainian.com",
        "logo": "https://www.librainian.com/static/img/logo_trans_name.png",
        "description": "Leading provider of cloud-based library management solutions for modern libraries and educational institutions.",
        "contactPoint": [
            {
                "@type": "ContactPoint",
                "telephone": "+91-**********",
                "contactType": "customer service",
                "availableLanguage": ["English", "Hindi"]
            },
            {
                "@type": "ContactPoint",
                "email": "<EMAIL>",
                "contactType": "customer service"
            }
        ],
        "sameAs": [
            "https://www.facebook.com/people/Librainian/61562707884730/",
            "https://www.instagram.com/librainian.app/",
            "https://www.linkedin.com/company/pinak-venture/",
            "https://www.youtube.com/channel/UCPf3m0ZR8XN7DVMOM6OJ8Qw"
        ]
    }
    </script>

    <!-- Import master CSS variables from code_temp.html -->
    {% include "code_temp.html" %}

    <!-- Modern Glassmorphism CSS Framework -->
    <style>
        :root {
            /* Login-specific overrides only */

            /* Typography */
            --font-primary: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-display: 'Comfortaa', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: var(--font-primary);
            background: var(--gradient-hero);
            background-attachment: fixed;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            line-height: 1.7;
            color: var(--gray-900);
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        /* Enhanced background with animated particles */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
            animation: float 6s ease-in-out infinite;
        }

        /* Login specific glass overrides - using unified system */

        /* Login Container */
        .login-container {
            width: 100%;
            max-width: 450px;
            padding: 2rem;
            position: relative;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-xl);
            box-shadow:
                0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 2px 4px rgba(255, 255, 255, 0.1) inset;
            padding: 3rem 2.5rem;
            position: relative;
            overflow: hidden;
            transition: var(--transition-slow);
        }

        .login-card:hover {
            transform: translateY(-5px);
            box-shadow:
                0 35px 60px -12px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.15) inset,
                0 2px 4px rgba(255, 255, 255, 0.15) inset;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(99, 102, 241, 0.1) 0%,
                rgba(139, 92, 246, 0.05) 50%,
                transparent 100%);
            pointer-events: none;
            opacity: 0.8;
        }

        .login-card::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.1) 50%,
                transparent 70%);
            transform: rotate(-45deg);
            animation: shimmer 3s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(-45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(-45deg); }
        }

        /* Security Badge with Enhanced Visibility */
        .security-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: var(--gradient-secondary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius-lg);
            font-size: 0.875rem;
            font-weight: 600;
            box-shadow: var(--shadow-lg);
            z-index: 20 !important;
            animation: float 3s ease-in-out infinite, pulseBadgeLogin 2s ease-in-out infinite alternate;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        /* Enhanced floating animation for login security badge */
        @keyframes pulseBadgeLogin {
            0% {
                box-shadow: var(--shadow-lg), 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            100% {
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px 5px rgba(99, 102, 241, 0.3);
            }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        /* Logo and Branding */
        .brand-logo {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .brand-logo img {
            height: 60px;
            margin-bottom: 1rem;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .brand-title {
            font-family: var(--font-display);
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .brand-subtitle {
            color: var(--gray-600);
            font-weight: 500;
            margin-bottom: 0;
        }

        /* Form Styling */
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.4);
            border-radius: var(--border-radius);
            padding: 1rem 1.25rem;
            font-size: 1rem;
            font-weight: 500;
            color: var(--gray-900);
            transition: var(--transition);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            width: 100%;
            box-shadow:
                0 4px 6px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow:
                0 0 0 3px rgba(99, 102, 241, 0.2),
                0 4px 12px rgba(99, 102, 241, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2) inset;
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .form-control:hover {
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.25);
        }

        .form-control::placeholder {
            color: var(--gray-500);
            font-weight: 400;
        }

        /* Password Toggle */
        .password-group {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--gray-500);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .password-toggle:hover {
            color: var(--primary);
            background: rgba(99, 102, 241, 0.1);
        }

        /* Login Button */
        .login-btn {
            width: 100%;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: var(--border-radius-lg);
            padding: 1.25rem 2rem;
            font-size: 1.125rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            box-shadow:
                0 10px 25px rgba(99, 102, 241, 0.3),
                0 4px 12px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow:
                0 15px 35px rgba(99, 102, 241, 0.4),
                0 8px 20px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.15) inset;
        }

        .login-btn:active {
            transform: translateY(-1px);
        }

        /* Google Login Button */
        .google-btn {
            width: 100%;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-lg);
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            color: var(--gray-800);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            text-decoration: none;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow:
                0 4px 12px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        }

        .google-btn:hover {
            border-color: rgba(255, 255, 255, 0.5);
            color: var(--gray-900);
            transform: translateY(-2px);
            box-shadow:
                0 8px 20px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2) inset;
            text-decoration: none;
            background: rgba(255, 255, 255, 0.3);
        }

        .google-btn img {
            width: 24px;
            height: 24px;
        }

        /* Links */
        .form-links {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .form-link {
            color: rgba(217, 217, 236, 0.9);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: var(--transition);
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
        }

        .form-link:hover {
            color: rgba(217, 217, 236, 1);
            text-decoration: underline;
            text-shadow: 0 2px 4px rgba(255, 255, 255, 0.5);
        }

        /* Remember Me */
        .form-check {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .form-check-input {
            width: 1.25rem;
            height: 1.25rem;
            border: 2px solid var(--gray-300);
            border-radius: 4px;
            background: white;
            cursor: pointer;
            margin: 0;
            flex-shrink: 0;
            vertical-align: middle;
        }

        .form-check-input:checked {
            background: var(--primary);
            border-color: var(--primary);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
        }

        .form-check-label {
            color: rgba(217, 217, 236, 0.9);
            font-weight: 500;
            cursor: pointer;
            line-height: 1.25rem;
            margin: 0;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
        }

        /* SSL Security Text */
        .text-muted {
            color: rgba(217, 217, 236, 0.9) !important;
        }

        /* Mobile checkbox enhancements */
        @media (max-width: 768px) {
            .form-check-input {
                width: 1.5rem;
                height: 1.5rem;
                border-width: 3px;
            }

            .form-check-label {
                font-size: 1rem;
                line-height: 1.5rem;
            }

            .form-check {
                gap: 1rem;
                margin-bottom: 2rem;
            }
        }

        /* Alert Messages */
        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
            position: relative;
            z-index: 2;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border-left: 4px solid var(--success);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border-left: 4px solid var(--danger);
        }

        /* Enhanced Glassmorphism Effects */
        .form-label {
            color: var(--gray-800);
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        }

        .brand-title {
            color: var(--gray-900);
            text-shadow: 0 2px 4px rgba(255, 255, 255, 0.3);
        }

        .brand-subtitle {
            color: var(--gray-700);
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        }

        /* Subtle entrance animation */
        .login-card {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced focus states */
        .form-control:focus + .form-label,
        .form-control:not(:placeholder-shown) + .form-label {
            color: var(--primary);
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            body::before {
                animation: none; /* Disable animation on mobile for performance */
            }

            .login-container {
                padding: 1rem;
                max-width: 100%;
            }

            .login-card {
                padding: 2rem 1.5rem;
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
            }

            .login-card::after {
                display: none; /* Disable shimmer on mobile */
            }

            .brand-title {
                font-size: 1.5rem;
            }

            .security-badge {
                position: relative;
                top: 0;
                right: 0;
                margin-bottom: 1rem;
                display: inline-block;
                z-index: -1;
            }

            .form-links {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .login-card {
                background: rgba(255, 255, 255, 0.95);
                border: 3px solid var(--gray-800);
            }

            .form-control {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid var(--gray-600);
            }
        }

        /* Dark Mode Styles for Login Page */
        body.dark-mode {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            color: white;
        }

        body.dark-mode .login-card {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .brand-title {
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .brand-subtitle {
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .form-label {
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .form-control {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        body.dark-mode .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        body.dark-mode .form-control:focus {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary);
            color: white;
        }

        body.dark-mode .form-control:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.3);
        }

        body.dark-mode .password-toggle {
            color: rgba(255, 255, 255, 0.6);
        }

        body.dark-mode .password-toggle:hover {
            color: var(--primary);
        }

        body.dark-mode .google-btn {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        body.dark-mode .google-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
            color: white;
        }

        body.dark-mode .form-link {
            color: rgba(217, 217, 236, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .form-link:hover {
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        body.dark-mode .form-check-label {
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .form-check-input {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
        }

        body.dark-mode .form-check-input:checked {
            background: var(--primary);
            border-color: var(--primary);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
        }

        body.dark-mode .text-muted {
            color: rgba(217, 217, 236, 0.9) !important;
        }

        body.dark-mode .security-badge {
            background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
            color: white;
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .login-card,
            .security-badge,
            body::before,
            .login-card::after {
                animation: none;
            }

            .login-btn::before {
                display: none;
            }
        }
    </style>
</head>

</head>

<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5L8WZWCC"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

    <div class="login-container">
        <!-- Security Badge -->
        <div class="security-badge">
            <i class="fas fa-shield-alt me-1"></i>
            Secure Login
        </div>
        <div class="login-card">

            <!-- Brand Logo and Title -->
            <div class="brand-logo">
                <img id="loginLogo" src="{% static 'img/logo_trans_name.png' %}" alt="Librainian - Secure Library Management System" loading="eager">
                <h1 class="brand-title">Welcome Back</h1>
                <p class="brand-subtitle">Access your secure library dashboard</p>
            </div>

            <!-- Alert Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}" role="alert">
                        <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Login Form -->
            <form method="post" action="{% url 'lib_login' %}" novalidate>
                {% csrf_token %}

                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-2"></i>Username
                    </label>
                    <input type="text"
                           class="form-control"
                           id="username"
                           name="username"
                           placeholder="Enter your username"
                           required
                           autocomplete="username"
                           pattern="^[a-zA-Z0-9@._-]+$"
                           title="Username can only contain letters, numbers, and @._-"
                           aria-describedby="usernameHelp">
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-2"></i>Password
                    </label>
                    <div class="password-group">
                        <input type="password"
                               class="form-control"
                               id="password"
                               name="password"
                               placeholder="Enter your password"
                               required
                               autocomplete="current-password"
                               aria-describedby="passwordHelp">
                        <button type="button" class="password-toggle" onclick="togglePassword()" aria-label="Toggle password visibility">
                            <i class="fas fa-eye" id="toggleIcon"></i>
                        </button>
                    </div>
                </div>

                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                    <label class="form-check-label" for="remember">
                        Keep me signed in
                    </label>
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-shield-check me-2"></i>
                    Secure Sign In
                </button>
            </form>

            <!-- Google Login -->
            <a href="{% url 'google_login' %}" class="google-btn">
                <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="24" height="24">
                Continue with Google
            </a>

            <!-- Form Links -->
            <div class="form-links">
                <a href="{% url 'password_reset_request' %}" class="form-link">
                    <i class="fas fa-key me-1"></i>Forgot Password?
                </a>
                <a href="{% url 'lib_signup' %}" class="form-link">
                    <i class="fas fa-user-plus me-1"></i>Create Account
                </a>
            </div>

            <!-- Security Notice -->
            <div class="text-center mt-3">
                <small class="text-muted">
                    <i class="fas fa-lock me-1"></i>
                    Your connection is secured with 256-bit SSL encryption
                </small>
            </div>

            <!-- Footer Links -->
            <div class="text-center mt-3">
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="/" class="form-link small">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                    <a href="/blogs/p/privacy-policy/" class="form-link small">Privacy Policy</a>
                    <a href="/blogs/p/terms-of-use/" class="form-link small">Terms of Use</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Modern Interactions -->
    <script>
        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    alert.style.transition = 'opacity 0.5s ease-out';
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 500);
                }, 5000);
            });
        });

        // Form validation enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const inputs = form.querySelectorAll('input[required]');

            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value.trim() === '') {
                        this.style.borderColor = 'var(--danger)';
                    } else {
                        this.style.borderColor = 'var(--success)';
                    }
                });

                input.addEventListener('input', function() {
                    if (this.style.borderColor === 'var(--danger)' && this.value.trim() !== '') {
                        this.style.borderColor = 'var(--success)';
                    }
                });
            });
        });

        // Page analytics (lightweight version)
        window.addEventListener("load", function () {
            const path = window.location.pathname;
            let pageData = JSON.parse(localStorage.getItem("page_data")) || {};
            pageData[path] = pageData[path] ? pageData[path] + 1 : 1;
            localStorage.setItem("page_data", JSON.stringify(pageData));
        });

        // Send analytics data periodically
        function sendPageData() {
            const pageData = JSON.parse(localStorage.getItem("page_data")) || {};
            if (Object.keys(pageData).length > 0) {
                fetch(location.origin + "/librarian/track-page-view/", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRFToken": "{{ csrf_token }}",
                    },
                    body: JSON.stringify(pageData),
                })
                .then(() => localStorage.removeItem("page_data"))
                .catch((error) => {
                    console.error("Error sending page data:", error);
                    localStorage.removeItem("page_data");
                });
            }
        }

        // Send data every 30 seconds (reduced frequency for better performance)
        setInterval(sendPageData, 30000);

        // Dark mode initialization
        document.addEventListener('DOMContentLoaded', function() {
            const darkModeEnabled = localStorage.getItem('darkMode') === 'enabled';
            if (darkModeEnabled) {
                document.body.classList.add('dark-mode');
                applyDarkModeStyles(true);
            } else {
                // Ensure light mode logo is set
                applyDarkModeStyles(false);
            }
        });

        // Dark mode styles function
        function applyDarkModeStyles(isDarkMode) {
            const root = document.documentElement;
            const loginLogo = document.getElementById('loginLogo');

            if (isDarkMode) {
                // Dark mode color overrides
                root.style.setProperty('--bg-primary', '#111827');
                root.style.setProperty('--bg-secondary', '#1f2937');
                root.style.setProperty('--bg-tertiary', '#374151');
                root.style.setProperty('--text-primary', '#ffffff');
                root.style.setProperty('--text-secondary', '#d1d5db');
                root.style.setProperty('--text-muted', '#9ca3af');
                root.style.setProperty('--border-color', '#4b5563');
                root.style.setProperty('--shadow-color', 'rgba(0, 0, 0, 0.3)');

                // Dark glassmorphism
                root.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.05)');
                root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.1)');
                root.style.setProperty('--glass-shadow', '0 8px 32px rgba(0, 0, 0, 0.3)');

                // Dark gradients
                root.style.setProperty('--gradient-hero', 'linear-gradient(135deg, #1f2937 0%, #111827 100%)');
                root.style.setProperty('--gradient-glass', 'linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)');

                // Switch to white logo
                if (loginLogo) {
                    loginLogo.src = loginLogo.src.replace('logo_trans_name.png', 'librainian-logo-white-trans.png');
                }
            } else {
                // Light mode - switch back to original logo
                if (loginLogo) {
                    loginLogo.src = loginLogo.src.replace('librainian-logo-white-trans.png', 'logo_trans_name.png');
                }
            }

            // Update browser theme and status bar
            updateBrowserTheme(isDarkMode);
        }

        // Update browser theme and status bar
        function updateBrowserTheme(isDarkMode) {
            // Update theme-color meta tag
            const themeColorMeta = document.getElementById('theme-color-meta');
            const statusBarMeta = document.getElementById('status-bar-meta');

            if (themeColorMeta) {
                themeColorMeta.setAttribute('content', isDarkMode ? '#111827' : '#6366f1');
            }

            if (statusBarMeta) {
                statusBarMeta.setAttribute('content', isDarkMode ? 'black-translucent' : 'default');
            }
        }
    </script>
</body>
</html>