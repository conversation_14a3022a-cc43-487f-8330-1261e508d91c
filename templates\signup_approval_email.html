{% extends "email_base.html" %}

{% block email_title %}Librarian Signup Approval - Librainian{% endblock %}

{% block email_subject %}Librarian Signup Approval Required{% endblock %}

{% block email_description %}Notification about a new librarian signup that requires manager approval.{% endblock %}

{% block preview_text %}New librarian signup from {{ librarian.library_name }} requires your approval.{% endblock %}

{% block header_icon %}👤{% endblock %}

{% block email_header_title %}Librarian Signup Approval{% endblock %}

{% block email_header_subtitle %}New librarian registration requires your review{% endblock %}

{% block email_styles %}
<style>
    .approval-details {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-radius: 16px;
        padding: 25px;
        margin: 25px 0;
        border: 2px solid #0ea5e9;
        position: relative;
        overflow: hidden;
    }

    .approval-details::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(14, 165, 233, 0.1), transparent);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(30deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(30deg); }
    }

    .approval-icon {
        font-size: 48px;
        text-align: center;
        margin-bottom: 20px;
        position: relative;
        z-index: 1;
    }

    .approval-title {
        font-size: 20px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 20px 0;
        text-align: center;
        position: relative;
        z-index: 1;
    }

    .detail-table {
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: 12px;
        padding: 20px;
        margin: 20px 0;
        position: relative;
        z-index: 1;
        border: 1px solid rgba(14, 165, 233, 0.2);
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid rgba(14, 165, 233, 0.2);
    }

    .detail-row:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: #0ea5e9;
        font-size: 14px;
        min-width: 140px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .detail-value {
        font-weight: 500;
        color: #2c3e50;
        font-size: 16px;
        text-align: right;
        flex: 1;
        margin-left: 15px;
    }

    .action-button {
        display: inline-block;
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
        color: #ffffff !important;
        text-decoration: none;
        padding: 16px 32px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
        box-shadow: 0 4px 16px rgba(14, 165, 233, 0.3);
        margin: 20px 0;
    }

    .action-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
        text-decoration: none;
        color: #ffffff !important;
    }

    .action-section {
        text-align: center;
        margin: 30px 0;
        position: relative;
        z-index: 1;
    }

    .urgent-notice {
        background-color: #fef3c7;
        border: 1px solid #fbbf24;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        border-left: 4px solid #f59e0b;
    }

    .notice-title {
        font-size: 16px;
        font-weight: 600;
        color: #92400e;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
    }

    .notice-title::before {
        content: "⏰";
        margin-right: 8px;
        font-size: 18px;
    }

    .notice-text {
        font-size: 14px;
        color: #92400e;
        margin: 0;
        line-height: 1.5;
    }

    @media only screen and (max-width: 600px) {
        .detail-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
        }

        .detail-value {
            text-align: left !important;
            margin-left: 0;
        }

        .approval-details {
            padding: 20px !important;
            margin: 20px 0 !important;
        }

        .action-button {
            padding: 14px 28px !important;
            font-size: 14px !important;
        }
    }
</style>
{% endblock %}

{% block email_content %}
<h2 class="greeting">Dear {{ manager.user.first_name }}!</h2>
<p class="message">
    A new librarian has signed up and is awaiting your approval to join the Librainian platform. Please review the details below and take appropriate action.
</p>

<!-- Approval Details Section -->
<div class="approval-details">
    <div class="approval-icon">👤</div>
    <h3 class="approval-title">Librarian Registration Details</h3>

    <div class="detail-table">
        <div class="detail-row">
            <span class="detail-label">Library Name</span>
            <span class="detail-value">{{ librarian.library_name }}</span>
        </div>

        <div class="detail-row">
            <span class="detail-label">Email Address</span>
            <span class="detail-value">{{ librarian.user.email }}</span>
        </div>

        <div class="detail-row">
            <span class="detail-label">Manager Name</span>
            <span class="detail-value">{{ librarian.manager.user.first_name }} {{ librarian.manager.user.last_name }}</span>
        </div>

        <div class="detail-row">
            <span class="detail-label">Manager Email</span>
            <span class="detail-value">{{ librarian.manager.user.email }}</span>
        </div>
    </div>

    <div class="action-section">
        <a href="{{ approval_url }}" class="action-button">Review and Approve</a>
    </div>
</div>

<!-- Urgent Notice -->
<div class="urgent-notice">
    <h4 class="notice-title">Action Required</h4>
    <p class="notice-text">
        Please review this signup request promptly. The librarian is waiting for approval to start using the system and serve their library community.
    </p>
</div>

<p class="message">
    Thank you for your prompt attention to this matter. If you have any questions about this signup request, please contact our support team.
</p>
{% endblock %}
