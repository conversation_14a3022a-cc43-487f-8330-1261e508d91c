{% extends "base.html" %}

{% block title %}Register - Librainian{% endblock %}

{% block extra_css %}
<style>
    .register-container {
        min-height: 100vh;
        padding: 2rem 0;
    }

    .register-options {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-top: 2rem;
    }

    .register-option {
        background: rgba(255, 255, 255, 0.15);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 2.5rem 1.5rem;
        text-align: center;
        text-decoration: none;
        color: white;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
    }

    .register-option::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .register-option:hover::before {
        left: 100%;
    }

    .register-option:hover {
        transform: translateY(-5px);
        border-color: rgba(255, 255, 255, 0.4);
        background: rgba(255, 255, 255, 0.2);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        text-decoration: none;
        color: white;
    }

    .register-option-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
        opacity: 0.9;
    }

    .register-option-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .register-option-description {
        font-size: 0.9rem;
        opacity: 0.8;
        line-height: 1.4;
    }

    /* Student Register specific styling */
    .student-register {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.2));
    }

    .student-register:hover {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(37, 99, 235, 0.3));
    }

    .student-register .register-option-icon {
        color: #60a5fa;
    }

    /* Invoice Register specific styling */
    .invoice-register {
        background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(147, 51, 234, 0.2));
    }

    .invoice-register:hover {
        background: linear-gradient(135deg, rgba(168, 85, 247, 0.3), rgba(147, 51, 234, 0.3));
    }

    .invoice-register .register-option-icon {
        color: #c084fc;
    }

    /* Dark mode adjustments */
    [data-theme="dark"] .register-option {
        background: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.1);
    }

    [data-theme="dark"] .register-option:hover {
        background: rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.2);
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
        .register-options {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .register-container {
            padding: 1rem;
        }

        .register-option {
            padding: 2rem 1rem;
        }

        .register-option-icon {
            font-size: 2.5rem;
        }
    }

    @media (max-width: 575px) {
        .register-container {
            padding: 0.5rem;
        }

        .register-option {
            padding: 1.5rem 0.75rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="register-container">
    <div class="container">
        <div class="register-options">
            <!-- Student Register -->
            <a href="/{{ role }}/student-register/" class="register-option student-register">
                <i class="fas fa-users register-option-icon"></i>
                <div class="register-option-title">Student Register</div>
                <div class="register-option-description">
                    View and manage all registered students with detailed information and search capabilities
                </div>
            </a>

            <!-- Invoice Register -->
            <a href="/{{ role }}/invoice-register/" class="register-option invoice-register">
                <i class="fas fa-file-invoice-dollar register-option-icon"></i>
                <div class="register-option-title">Invoice Register</div>
                <div class="register-option-description">
                    Access organized invoice records by students with comprehensive billing history
                </div>
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add staggered animation for options
        const options = document.querySelectorAll('.register-option');
        options.forEach((option, index) => {
            option.style.opacity = '0';
            option.style.transform = 'translateY(20px)';

            setTimeout(() => {
                option.style.transition = 'all 0.5s ease';
                option.style.opacity = '1';
                option.style.transform = 'translateY(0)';
            }, 100 + (index * 150));
        });
    });
</script>
{% endblock %}
