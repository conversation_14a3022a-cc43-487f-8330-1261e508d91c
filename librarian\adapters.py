from datetime import timedelta
from django.utils import timezone  # Changed this import
from allauth.account.adapter import DefaultAccountAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from django.template.defaultfilters import slugify

from membership.models import Membership, Plan
from wallet_and_transactions.models import Wallet
from .models import Librarian_param
from manager.models import Manager_param
from django.contrib.auth.models import Group


class CustomAccountAdapter(DefaultAccountAdapter):
    def save_user(self, request, user, form, commit=True):
        """
        This is called when saving user via allauth registration.
        We override this to create a Librarian instance after user save.
        """
        user = super().save_user(request, user, form, commit)

        try:
            # Get default manager
            manager = Manager_param.objects.get(id=1)

            # Create associated Librarian
            librarian = Librarian_param.objects.create(
                user=user,
                manager=manager,
                library_name=f"{user.username}'s Library",
                librarian_phone_num=********,
                librarian_address="Default Address",
                librarian_role="Librarian",
                is_librarian=True,
                slug=slugify(user.username),
            )

            # Add user to librarian group
            try:
                group = Group.objects.get(name="librarian")
                user.groups.add(group)
            except Group.DoesNotExist:
                group = Group.objects.create(name="librarian")
                user.groups.add(group)

            # Create free membership using utility function
            free_plan = get_or_create_free_plan()
            current_date = timezone.now()  # Get current datetime
            start_date = current_date.date()  # Convert to date
            expiry_date = start_date + timedelta(days=15)

            # Create membership directly
            Membership.objects.create(
                plan=free_plan,
                librarian=librarian,
                start_date=start_date,
                expiry_date=expiry_date,
            )

            # Create wallet directly
            Wallet.objects.create(user=user)

        except Exception as e:
            raise

        return user


class CustomSocialAccountAdapter(DefaultSocialAccountAdapter):
    def save_user(self, request, sociallogin, form=None):
        """
        This is called when saving user via social account login.
        We override this to create a Librarian instance after user save.
        """
        user = super().save_user(request, sociallogin, form)

        try:
            # Get default manager
            manager = Manager_param.objects.get(id=1)

            # Get or create librarian
            librarian, created = Librarian_param.objects.get_or_create(
                user=user,
                defaults={
                    "manager": manager,
                    "library_name": f"{user.username}'s Library",
                    "librarian_phone_num": ********,
                    "librarian_address": "Default Address",
                    "librarian_role": "Librarian",
                    "is_librarian": True,
                    "slug": slugify(user.username),
                },
            )

            if created:
                # Add user to librarian group
                try:
                    group = Group.objects.get(name="librarian")
                    user.groups.add(group)
                except Group.DoesNotExist:
                    group = Group.objects.create(name="librarian")
                    user.groups.add(group)

                # Create free membership using utility function
                free_plan = get_or_create_free_plan()
                current_date = timezone.now()  # Get current datetime
                start_date = current_date.date()  # Convert to date
                expiry_date = start_date + timedelta(days=10)

                # Create membership directly
                Membership.objects.create(
                    plan=free_plan,
                    librarian=librarian,
                    start_date=start_date,
                    expiry_date=expiry_date,
                )

                # Create wallet directly
                Wallet.objects.create(user=user)

        except Exception as e:
            raise

        return user
