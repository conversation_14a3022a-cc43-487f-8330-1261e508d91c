<style>
    /* MASTER CSS VARIABLES - DO NOT DUPLICATE IN OTHER TEMPLATES */
    :root {
        /* Modern Color Palette */
        --primary: #6366f1;
        --primary-dark: #4f46e5;
        --primary-light: #8b5cf6;
        --secondary: #10b981;
        --accent: #f59e0b;
        --danger: #ef4444;
        --warning: #f59e0b;
        --success: #10b981;
        --info: #06b6d4;

        /* Light Mode Colors */
        --white: #ffffff;
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-400: #9ca3af;
        --gray-500: #6b7280;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --gray-900: #111827;

        /* Theme Colors (Dynamic) */
        --bg-primary: var(--white);
        --bg-secondary: var(--gray-50);
        --bg-tertiary: var(--gray-100);
        --text-primary: var(--gray-900);
        --text-secondary: var(--gray-600);
        --text-muted: var(--gray-400);
        --border-color: var(--gray-200);
        --shadow-color: rgba(0, 0, 0, 0.1);

        /* Layout */
        --sidebar-width: 280px;
        --topbar-height: 80px;
        --bottom-menu-height: 70px;

        /* Spacing & Effects */
        --border-radius: 12px;
        --border-radius-lg: 20px;
        --border-radius-xl: 32px;
        --border-radius-sm: 8px;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

        /* Transitions */
        --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        /* Glassmorphism Effects */
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-bg-light: rgba(255, 255, 255, 0.95);
        --glass-bg-medium: rgba(255, 255, 255, 0.08);
        --glass-border: rgba(255, 255, 255, 0.2);
        --glass-border-light: rgba(255, 255, 255, 0.3);
        --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        --glass-shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        --glass-blur: blur(20px);
        --glass-blur-strong: blur(25px);

        /* Background Gradients */
        --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        --gradient-secondary: linear-gradient(135deg, var(--secondary) 0%, #059669 100%);
        --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

        /* Glassmorphism Variables */
        --glass-bg: rgba(255, 255, 255, 0.15);
        --glass-border: rgba(255, 255, 255, 0.3);
        --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        --glass-backdrop: blur(20px);
    }

    /* Dark Mode Variables */
    body.dark-mode {
        --bg-primary: var(--gray-900);
        --bg-secondary: var(--gray-800);
        --bg-tertiary: var(--gray-700);
        --text-primary: var(--white);
        --text-secondary: var(--gray-300);
        --text-muted: var(--gray-400);
        --border-color: var(--gray-600);
        --shadow-color: rgba(0, 0, 0, 0.3);

        /* Dark Mode Gradients */
        --gradient-hero: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);

        /* Dark Glassmorphism */
        --glass-bg: rgba(255, 255, 255, 0.05);
        --glass-border: rgba(255, 255, 255, 0.1);
        --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    * {
        box-sizing: border-box;
    }

    body.modern-dashboard {
        font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: var(--gradient-hero);
        background-attachment: fixed;
        margin: 0;
        padding: 0;
        line-height: 1.6;
        color: var(--text-primary);
        font-size: 14px;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        transition: background 0.3s ease, color 0.3s ease;
    }

    /* Glass Background Effects */
    body.modern-dashboard::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    /* Loading Screen */
    .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.3s ease;
    }

    /* Immediate dark mode detection for loading screen */
    .loading-screen.dark-mode-immediate {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    }

    .loading-spinner {
        text-align: center;
        color: white;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-text {
        font-weight: 500;
        margin: 0;
    }

    /* Dark Mode Loading Screen */
    body.dark-mode .loading-screen {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    }

    body.dark-mode .loading-spinner {
        color: rgba(255, 255, 255, 0.95);
    }

    body.dark-mode .spinner {
        border: 4px solid rgba(255, 255, 255, 0.2);
        border-top: 4px solid rgba(255, 255, 255, 0.9);
    }

    /* Dashboard Layout */
    .dashboard-layout {
        display: flex;
        min-height: 100vh;
        position: relative;
    }

    /* Sidebar Styles */
    .sidebar-desktop {
        width: var(--sidebar-width);
        background: white;
        border-right: 1px solid var(--gray-200);
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 1070;
        transition: var(--transition-slow);
        overflow-y: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .sidebar-desktop::-webkit-scrollbar {
        display: none;
    }

    .sidebar-mobile {
        width: var(--sidebar-width);
        background: var(--bg-primary);
        position: fixed;
        top: 0;
        left: -100%;
        height: 100vh;
        z-index: 1080;
        transition: var(--transition-slow);
        overflow-y: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        box-shadow: var(--shadow-xl);
        padding-bottom: var(--bottom-menu-height);
    }

    .sidebar-mobile::-webkit-scrollbar {
        display: none;
    }

    .sidebar-mobile.active {
        left: 0;
    }

    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1040;
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
    }

    .sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* Main Content */
    .main-content {
        flex: 1;
        margin-left: var(--sidebar-width);
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        transition: var(--transition-slow);
    }

    .topbar-container {
        position: sticky;
        top: 0;
        z-index: 1050;
        background: transparent;
        border-bottom: none;
        box-shadow: none;
    }

    .page-content {
        flex: 1;
        padding: 2rem;
        padding-bottom: calc(2rem + var(--bottom-menu-height));
        overflow-x: hidden;
        position: relative;
        z-index: 1;
    }

    /* Bottom Menu Mobile */
    .bottom-menu-mobile {
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: var(--bottom-menu-height) !important;
        background: white !important;
        border-top: 1px solid var(--gray-200) !important;
        z-index: 9999 !important;
        box-shadow: 0 -4px 6px -1px rgb(0 0 0 / 0.1) !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        transform: translateY(0) !important;
        transition: transform 0.3s ease !important;
    }

    /* Override any conflicting footer menu styles */
    .footer-menu {
        display: none !important;
    }

    /* Ensure bottom menu stays visible on mobile */
    @media (max-width: 991.98px) {
        .bottom-menu-mobile {
            display: block !important;
            z-index: 9999 !important;
        }

        /* Ensure body has proper padding to prevent content overlap */
        body {
            padding-bottom: var(--bottom-menu-height) !important;
        }

        /* Ensure main content doesn't overlap */
        .main-content,
        .page-content {
            margin-bottom: var(--bottom-menu-height) !important;
        }
    }

    /* Additional mobile-specific fixes */
    @media (max-width: 767.98px) {
        .bottom-menu-mobile {
            height: 70px !important;
            min-height: 70px !important;
        }

        /* Prevent any footer or other elements from interfering */
        .footer,
        footer {
            margin-bottom: 70px !important;
            padding-bottom: 0 !important;
        }

        /* Ensure sidebar has proper padding for scrolling */
        .sidebar-mobile {
            padding-bottom: 70px !important;
        }

        /* Ensure sidebar content can scroll properly */
        .sidebar-mobile .sidebar-content {
            padding-bottom: 2rem !important;
        }

        /* Ensure consistent background for padding area */
        .sidebar-mobile {
            background: var(--bg-primary) !important;
        }

        body.dark-mode .sidebar-mobile {
            background: var(--bg-secondary) !important;
        }
    }

    /* Glassmorphism Base Classes */
    .glass {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        box-shadow: var(--glass-shadow);
        border-radius: var(--border-radius-lg);
        transition: var(--transition-slow);
    }

    .glass-card {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        -webkit-backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--glass-shadow-lg);
        transition: var(--transition-slow);
        position: relative;
        overflow: hidden;
    }

    .glass-card:hover {
        transform: translateY(-3px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .glass-enhanced {
        background: var(--glass-bg-medium);
        backdrop-filter: var(--glass-blur-strong);
        -webkit-backdrop-filter: var(--glass-blur-strong);
        border: 1px solid rgba(255, 255, 255, 0.15);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        border-radius: var(--border-radius-lg);
        transition: var(--transition-slow);
    }

    /* Modern Card Styles */
    .modern-card {
        background: var(--glass-bg-light);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--glass-shadow);
        border: 1px solid var(--glass-border);
        overflow: hidden;
        transition: var(--transition-slow);
        margin-bottom: 1.5rem;
        position: relative;
    }

    .modern-card:hover {
        box-shadow: var(--glass-shadow-lg);
        transform: translateY(-3px);
    }

    .modern-card-header {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .modern-card-body {
        padding: 1.5rem;
        background: transparent;
    }

    .modern-card-footer {
        padding: 1rem 1.5rem;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    /* Stats Cards */
    .stats-card {
        background: var(--glass-bg-light);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border-radius: var(--border-radius-lg);
        padding: 1.5rem;
        box-shadow: var(--glass-shadow);
        border: 1px solid var(--glass-border);
        transition: var(--transition-slow);
        position: relative;
        overflow: hidden;
    }

    .stats-card:hover {
        box-shadow: var(--glass-shadow-lg);
        transform: translateY(-3px);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--primary-light));
    }

    .stats-value {
        font-size: 2rem;
        font-weight: 800;
        color: var(--gray-900);
        margin: 0;
        font-family: 'Plus Jakarta Sans', sans-serif;
    }

    .stats-label {
        font-size: 0.875rem;
        color: var(--gray-600);
        font-weight: 500;
        margin: 0.25rem 0 0 0;
    }

    .stats-icon {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
    }

    /* Button Styles */
    .btn-modern {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: var(--border-radius);
        font-weight: 600;
        font-size: 0.875rem;
        text-decoration: none;
        transition: var(--transition);
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-primary-modern:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
        color: white;
    }

    .btn-secondary-modern {
        background: var(--gray-100);
        color: var(--gray-700);
        border: 1px solid var(--gray-200);
    }

    .btn-secondary-modern:hover {
        background: var(--gray-200);
        border-color: var(--gray-300);
    }

    /* Form Styles */
    .form-control-modern {
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius);
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        transition: var(--transition);
        background: white;
    }

    .form-control-modern:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
    }

    /* Table Styles */
    .modern-table {
        background: var(--glass-bg-light);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        box-shadow: var(--glass-shadow);
        border: 1px solid var(--glass-border);
    }

    .modern-table .table {
        margin: 0;
        border-radius: 0;
        background: transparent;
    }

    .modern-table .table thead th {
        background: rgba(255, 255, 255, 0.2);
        border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        font-weight: 600;
        color: var(--gray-800);
        padding: 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .modern-table .table tbody td {
        padding: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        vertical-align: middle;
        color: var(--gray-800);
    }

    .modern-table .table tbody tr:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    /* Glass Table Alternative */
    .table-glass {
        background: var(--glass-bg);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        box-shadow: var(--glass-shadow);
    }

    .table-glass th {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        font-weight: 600;
        border: none;
        padding: 1rem;
    }

    .table-glass td {
        color: rgba(255, 255, 255, 0.9);
        border-color: rgba(255, 255, 255, 0.1);
        padding: 1rem;
    }

    .table-glass tbody tr:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    /* Responsive Design */
    @media (max-width: 991.98px) {
        .sidebar-desktop {
            display: none;
        }
        
        .main-content {
            margin-left: 0;
        }
        
        .page-content {
            padding: 1rem;
            padding-bottom: calc(1rem + var(--bottom-menu-height));
        }
    }

    @media (max-width: 767.98px) {
        .page-content {
            padding: 0.75rem;
            padding-bottom: calc(0.75rem + var(--bottom-menu-height));
        }
        
        .stats-card {
            padding: 1rem;
        }
        
        .stats-icon {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
        
        .stats-value {
            font-size: 1.5rem;
        }
    }

    /* Dark Mode Specific Styles */
    body.dark-mode .modern-card {
        background: var(--glass-bg);
        border-color: var(--glass-border);
        color: var(--text-primary);
    }

    /* Dark Mode Bottom Menu */
    body.dark-mode .bottom-menu-mobile {
        background: var(--glass-bg) !important;
        border-top-color: var(--glass-border) !important;
        backdrop-filter: var(--glass-blur) !important;
        -webkit-backdrop-filter: var(--glass-blur) !important;
    }

    /* Dark Mode Mobile Sidebar */
    body.dark-mode .sidebar-mobile {
        background: var(--bg-secondary) !important;
    }

    body.dark-mode .modern-card-header {
        background: rgba(255, 255, 255, 0.05);
        border-bottom-color: var(--glass-border);
        color: var(--text-primary);
    }

    body.dark-mode .modern-card-body {
        color: var(--text-primary);
    }

    body.dark-mode .table-glass {
        background: var(--glass-bg);
        border-color: var(--glass-border);
    }

    body.dark-mode .table-glass th {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-primary);
    }

    body.dark-mode .table-glass td {
        color: var(--text-primary);
        border-color: var(--glass-border);
    }

    body.dark-mode .form-control-glass {
        background: rgba(255, 255, 255, 0.05);
        border-color: var(--glass-border);
        color: var(--text-primary);
    }

    body.dark-mode .form-control-glass::placeholder {
        color: var(--text-muted);
    }

    body.dark-mode .badge-glass {
        background: rgba(255, 255, 255, 0.1);
        border-color: var(--glass-border);
        color: var(--text-primary);
    }

    body.dark-mode .alert-glass {
        background: var(--glass-bg);
        border-color: var(--glass-border);
        color: var(--text-primary);
    }

    body.dark-mode .empty-state-glass {
        color: var(--text-secondary);
    }

    body.dark-mode .empty-state-glass h5 {
        color: var(--text-primary);
    }

    body.dark-mode .mobile-student-card,
    body.dark-mode .mobile-pending-card,
    body.dark-mode .mobile-shift-card,
    body.dark-mode .mobile-invoice-card {
        background: var(--glass-bg);
        border-color: var(--glass-border);
        color: var(--text-primary);
    }

    body.dark-mode .mobile-field label {
        color: var(--text-secondary);
    }

    body.dark-mode .mobile-value {
        color: var(--text-primary);
    }

    body.dark-mode .contact-item-mobile {
        background: rgba(255, 255, 255, 0.05);
        color: var(--text-primary);
    }

    body.dark-mode .student-name-mobile,
    body.dark-mode .shift-name-mobile {
        color: var(--text-primary);
    }

    body.dark-mode .student-id-mobile,
    body.dark-mode .shift-time-mobile {
        color: var(--text-secondary);
    }

    /* Utility Classes */
    .text-gradient {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    }

    .bg-gradient-secondary {
        background: linear-gradient(135deg, var(--secondary) 0%, #059669 100%);
    }

    .shadow-modern {
        box-shadow: var(--shadow-lg);
    }

    .border-radius-modern {
        border-radius: var(--border-radius-lg);
    }

    /* Animation Classes */
    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    .slide-up {
        animation: slideUp 0.5s ease-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideUp {
        from { 
            opacity: 0;
            transform: translateY(20px);
        }
        to { 
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Custom Scrollbar */
    .custom-scrollbar {
        scrollbar-width: thin;
        scrollbar-color: var(--gray-300) transparent;
    }

    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: transparent;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: var(--gray-300);
        border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: var(--gray-400);
    }

    /* Additional Glass Utility Classes */
    .glass-navbar {
        background: rgba(255, 255, 255, 0.804);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border-bottom: 1px solid rgba(135, 134, 134, 0.571);
        box-shadow: var(--glass-shadow);
        transition: var(--transition);
    }

    .glass-sidebar {
        background: var(--glass-bg-light);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border-right: 1px solid var(--glass-border);
        box-shadow: var(--glass-shadow);
    }

    .glass-button {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius);
        transition: var(--transition);
    }

    .glass-button:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
        box-shadow: var(--glass-shadow);
    }

    .glass-input {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius);
        transition: var(--transition);
    }

    .glass-input:focus {
        background: rgba(255, 255, 255, 0.95);
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    /* Glass Modal */
    .glass-modal {
        background: var(--glass-bg-light);
        backdrop-filter: var(--glass-blur-strong);
        -webkit-backdrop-filter: var(--glass-blur-strong);
        border: 1px solid var(--glass-border-light);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--glass-shadow-lg);
    }

    /* Ensure no white backgrounds override glass design */
    .page-content,
    .main-content,
    .dashboard-layout {
        background: transparent !important;
    }

    /* Fix any Bootstrap overrides - excluding dropdown menus */
    .card,
    .modal-content,
    .popover,
    .tooltip-inner {
        background: var(--glass-bg-light) !important;
        backdrop-filter: var(--glass-blur) !important;
        -webkit-backdrop-filter: var(--glass-blur) !important;
        border: 1px solid var(--glass-border) !important;
    }

    /* Dropdown menus should have proper backgrounds, not glassmorphism */
    .dropdown-menu {
        background: #ffffff !important;
        border: 1px solid #e5e7eb !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
    }

    /* Dark mode dropdown menus */
    body.dark-mode .dropdown-menu {
        background: #1f2937 !important;
        border-color: #374151 !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
    }

    /* Chart containers */
    canvas {
        background: transparent !important;
    }
</style>
