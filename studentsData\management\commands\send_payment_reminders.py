from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from studentsData.models import Invoice, Payment
from Library.utils import send_dynamic_email
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Send payment reminders for overdue partial payments'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days-overdue',
            type=int,
            default=7,
            help='Number of days overdue to trigger reminder (default: 7)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending emails'
        )

    def handle(self, *args, **options):
        days_overdue = options['days_overdue']
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS(f'Starting payment reminder process...')
        )
        
        # Calculate the cutoff date
        cutoff_date = timezone.now().date() - timedelta(days=days_overdue)
        
        # Find invoices with partial payments that are overdue
        overdue_invoices = Invoice.objects.filter(
            payment_status='Partially Paid',
            due_date__lt=cutoff_date,
            is_active=True
        ).select_related('student').prefetch_related('payment_set')
        
        reminder_count = 0
        
        for invoice in overdue_invoices:
            try:
                # Get the latest payment for next commitment date
                latest_payment = invoice.payment_set.first()
                
                # Check if we should send reminder based on next commitment date
                should_send_reminder = True
                if latest_payment and latest_payment.next_commitment_date:
                    # If next commitment date is in the future, don't send reminder yet
                    if latest_payment.next_commitment_date > timezone.now().date():
                        should_send_reminder = False
                
                if should_send_reminder:
                    if dry_run:
                        self.stdout.write(
                            f'Would send reminder to {invoice.student.name} '
                            f'for invoice {invoice.invoice_id} '
                            f'(₹{invoice.remaining_due} remaining)'
                        )
                    else:
                        self.send_payment_reminder(invoice)
                        reminder_count += 1
                        
                        self.stdout.write(
                            f'Sent reminder to {invoice.student.name} '
                            f'for invoice {invoice.invoice_id}'
                        )
                        
            except Exception as e:
                logger.error(f'Error sending reminder for invoice {invoice.invoice_id}: {str(e)}')
                self.stdout.write(
                    self.style.ERROR(
                        f'Error sending reminder for invoice {invoice.invoice_id}: {str(e)}'
                    )
                )
        
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Dry run completed. Would have sent {len(overdue_invoices)} reminders.'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Payment reminder process completed. Sent {reminder_count} reminders.'
                )
            )

    def send_payment_reminder(self, invoice):
        """Send payment reminder email to student"""
        try:
            # Calculate days overdue
            days_overdue = (timezone.now().date() - invoice.due_date).days
            
            # Get payment history
            payments = invoice.payment_set.all()
            
            subject = f"Payment Reminder - Invoice #{invoice.invoice_id}"
            template_name = "payment_reminder_email.html"
            context = {
                "student": invoice.student,
                "invoice": invoice,
                "days_overdue": days_overdue,
                "payments": payments,
                "library_name": invoice.student.librarian.library_name,
            }
            
            send_dynamic_email(
                subject,
                template_name,
                context,
                invoice.student.email
            )
            
            # Log the reminder
            logger.info(f'Payment reminder sent to {invoice.student.email} for invoice {invoice.invoice_id}')
            
        except Exception as e:
            logger.error(f'Failed to send payment reminder for invoice {invoice.invoice_id}: {str(e)}')
            raise
