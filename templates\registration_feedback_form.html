<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Feedback - {{librarian.library_name}}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Light mode colors */
            --primary-color: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --secondary-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --surface-color: rgba(255, 255, 255, 0.25);
            --surface-hover: rgba(255, 255, 255, 0.35);
            --background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --border-color: rgba(255, 255, 255, 0.2);
            --shadow-light: rgba(255, 255, 255, 0.25);
            --shadow-dark: rgba(0, 0, 0, 0.1);
        }

        /* Dark mode detection and variables */
        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #818cf8;
                --primary-light: #a5b4fc;
                --primary-dark: #6366f1;
                --secondary-color: #22d3ee;
                --success-color: #34d399;
                --warning-color: #fbbf24;
                --error-color: #f87171;
                --surface-color: rgba(17, 24, 39, 0.25);
                --surface-hover: rgba(17, 24, 39, 0.35);
                --background-color: linear-gradient(135deg, #1e293b 0%, #334155 100%);
                --text-primary: #f9fafb;
                --text-secondary: #d1d5db;
                --text-light: #9ca3af;
                --border-color: rgba(255, 255, 255, 0.1);
                --shadow-light: rgba(255, 255, 255, 0.1);
                --shadow-dark: rgba(0, 0, 0, 0.3);
            }
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-primary);
            line-height: 1.6;
            background: var(--background-color) fixed;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            backdrop-filter: blur(10px);
        }

        .feedback-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .feedback-container {
            background: var(--surface-color);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 24px;
            box-shadow: 
                0 8px 32px var(--shadow-dark),
                inset 0 1px 0 var(--shadow-light);
            padding: 40px;
            max-width: 900px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        /* Glassmorphism accent bar */
        .feedback-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 24px 24px 0 0;
        }

        .feedback-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .feedback-header .material-icons {
            font-size: 48px;
            color: var(--primary-color);
            background: var(--surface-hover);
            backdrop-filter: blur(10px);
            border-radius: 50%;
            padding: 16px;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 16px var(--shadow-dark);
            margin-bottom: 16px;
        }

        .feedback-header h2 {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.875rem;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px var(--shadow-dark);
        }

        .feedback-header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin: 0;
        }

        .question-card {
            background: var(--surface-color);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 16px var(--shadow-dark);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px var(--shadow-dark);
        }

        .question-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .question-number {
            background: var(--primary-color);
            color: white;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
        }

        /* Slider styling */
        .slider-container {
            margin: 20px 0;
        }

        .slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: var(--surface-hover);
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
        }

        .slider::-moz-range-thumb {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
        }

        .slider-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .slider-value {
            text-align: center;
            margin-top: 12px;
            font-weight: 600;
            color: var(--primary-color);
            font-size: 1.125rem;
        }

        /* Radio button styling */
        .radio-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: var(--surface-hover);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .radio-option:hover {
            background: var(--surface-color);
            transform: translateX(4px);
        }

        .radio-option input[type="radio"] {
            margin-right: 12px;
            accent-color: var(--primary-color);
        }

        .radio-option.selected {
            background: rgba(99, 102, 241, 0.1);
            border-color: var(--primary-color);
        }

        /* Star rating */
        .star-rating {
            display: flex;
            gap: 8px;
            justify-content: center;
            margin: 16px 0;
        }

        .star-rating .star {
            font-size: 2rem;
            color: #e0e0e0;
            cursor: pointer;
            transition: color 0.2s, transform 0.2s;
            user-select: none;
        }

        .star-rating .star.filled {
            color: #fbbf24;
            text-shadow: 0 1px 2px rgba(0,0,0,0.15);
            transform: scale(1.1);
        }

        .star-rating .star:hover,
        .star-rating .star.hovered {
            color: #ffd700;
            transform: scale(1.15);
        }

        /* Toggle switch */
        .toggle-container {
            display: flex;
            align-items: center;
            gap: 16px;
            margin: 16px 0;
        }

        .toggle-switch {
            position: relative;
            width: 60px;
            height: 32px;
            background: var(--surface-hover);
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: var(--primary-color);
        }

        .toggle-slider {
            position: absolute;
            top: 4px;
            left: 4px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(28px);
        }

        /* Textarea styling */
        .form-textarea {
            width: 100%;
            min-height: 100px;
            padding: 14px 16px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            background: var(--surface-color);
            backdrop-filter: blur(10px);
            color: var(--text-primary);
            font-family: inherit;
            resize: vertical;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        /* Conditional fields */
        .conditional-field {
            margin-top: 16px;
            opacity: 0;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .conditional-field.show {
            opacity: 1;
            max-height: 200px;
        }

        /* Submit button */
        .submit-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: 1px solid var(--border-color);
            padding: 16px 32px;
            font-weight: 600;
            letter-spacing: 0.025em;
            border-radius: 12px;
            box-shadow: 
                0 4px 16px rgba(99, 102, 241, 0.3),
                inset 0 1px 0 var(--shadow-light);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: white;
            backdrop-filter: blur(10px);
            width: 100%;
            font-size: 1.125rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            transform: translateY(-2px);
            box-shadow: 
                0 6px 20px rgba(99, 102, 241, 0.4),
                inset 0 1px 0 var(--shadow-light);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .feedback-wrapper {
                padding: 16px;
            }

            .feedback-container {
                padding: 24px 20px;
                border-radius: 20px;
            }

            .feedback-header h2 {
                font-size: 1.5rem;
            }

            .question-card {
                padding: 20px;
            }

            .radio-group {
                gap: 8px;
            }

            .star {
                font-size: 1.75rem;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from { 
                opacity: 0; 
                transform: translateY(30px) scale(0.95); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0) scale(1); 
            }
        }

        .feedback-container {
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .question-card {
            animation: fadeInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            opacity: 0;
        }

        .question-card:nth-child(1) { animation-delay: 0.1s; }
        .question-card:nth-child(2) { animation-delay: 0.15s; }
        .question-card:nth-child(3) { animation-delay: 0.2s; }
        .question-card:nth-child(4) { animation-delay: 0.25s; }
        .question-card:nth-child(5) { animation-delay: 0.3s; }
        .question-card:nth-child(6) { animation-delay: 0.35s; }
        .question-card:nth-child(7) { animation-delay: 0.4s; }
        .question-card:nth-child(8) { animation-delay: 0.45s; }
        .question-card:nth-child(9) { animation-delay: 0.5s; }
    </style>
</head>
<body>
    <div class="feedback-wrapper">
        <div class="feedback-container">
            <div class="feedback-header">
                <span class="material-icons">feedback</span>
                <h2>Registration Feedback</h2>
                <p>Help us improve your experience at {{librarian.library_name}}</p>
            </div>

            {% if messages %}
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            {% endif %}

            <form method="POST" id="feedbackForm">
                {% csrf_token %}

                <!-- Question 1: How easy was it to fill out the registration form? -->
                <div class="question-card">
                    <div class="question-title">
                        <span class="question-number">1</span>
                        How easy was it to fill out the registration form?
                    </div>
                    <div class="slider-container">
                        <input type="range" min="0" max="10" value="5" class="slider" id="easeSlider" name="ease_rating" required>
                        <div class="slider-labels">
                            <span>Very Difficult</span>
                            <span>Extremely Easy</span>
                        </div>
                        <div class="slider-value" id="easeValue">5</div>
                    </div>
                </div>

                <!-- Question 2: How long did the registration take you? -->
                <div class="question-card">
                    <div class="question-title">
                        <span class="question-number">2</span>
                        How long did the registration take you?
                    </div>
                    <div class="radio-group">
                        <label class="radio-option">
                            <input type="radio" name="time_taken" value="less_than_1" required>
                            <span>Less than 1 minute</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="time_taken" value="1_to_2" required>
                            <span>1–2 minutes</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="time_taken" value="3_to_5" required>
                            <span>3–5 minutes</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="time_taken" value="more_than_5" required>
                            <span>More than 5 minutes</span>
                        </label>
                    </div>
                </div>

                <!-- Question 3: Did you face any issues while filling the form? -->
                <div class="question-card">
                    <div class="question-title">
                        <span class="question-number">3</span>
                        Did you face any issues while filling the form?
                    </div>
                    <div class="toggle-container">
                        <span style="color: var(--text-secondary);">No</span>
                        <div class="toggle-switch" id="issuesToggle" onclick="toggleIssues()">
                            <div class="toggle-slider"></div>
                        </div>
                        <span style="color: var(--text-secondary);">Yes</span>
                        <input type="hidden" name="faced_issues" id="facedIssuesInput" value="false">
                    </div>
                    <div class="conditional-field" id="issueDescription">
                        <label style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 8px; display: block;">
                            Please describe the issue:
                        </label>
                        <textarea class="form-textarea" name="issue_description" placeholder="Describe the issues you faced..." maxlength="500"></textarea>
                    </div>
                </div>

                <!-- Question 4: How visually appealing was the form design? -->
                <div class="question-card">
                    <div class="question-title">
                        <span class="question-number">4</span>
                        How visually appealing was the form design?
                    </div>
                    <div class="star-rating" id="designRating">
                        <span class="star" data-rating="1">&#9733;</span>
                        <span class="star" data-rating="2">&#9733;</span>
                        <span class="star" data-rating="3">&#9733;</span>
                        <span class="star" data-rating="4">&#9733;</span>
                        <span class="star" data-rating="5">&#9733;</span>
                    </div>
                    <input type="hidden" name="design_rating" id="designRatingInput" required>
                    <div style="text-align: center; color: var(--text-secondary); font-size: 0.9rem; margin-top: 8px;">
                        <span id="designRatingText">Click to rate</span>
                    </div>
                </div>

                <!-- Question 5: Which device did you use to fill out the form? -->
                <div class="question-card">
                    <div class="question-title">
                        <span class="question-number">5</span>
                        Which device did you use to fill out the form?
                    </div>
                    <div class="radio-group">
                        <label class="radio-option">
                            <input type="radio" name="device_used" value="mobile" required>
                            <span>📱 Mobile</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="device_used" value="tablet" required>
                            <span>📱 Tablet</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="device_used" value="laptop_desktop" required>
                            <span>💻 Laptop/Desktop</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="device_used" value="other" required>
                            <span>🔧 Other</span>
                        </label>
                    </div>
                </div>

                <!-- Question 6: Was the QR code easy to scan and access? -->
                <div class="question-card">
                    <div class="question-title">
                        <span class="question-number">6</span>
                        Was the QR code easy to scan and access?
                    </div>
                    <div class="toggle-container">
                        <span style="color: var(--text-secondary);">No</span>
                        <div class="toggle-switch active" id="qrToggle" onclick="toggleQR()">
                            <div class="toggle-slider"></div>
                        </div>
                        <span style="color: var(--text-secondary);">Yes</span>
                        <input type="hidden" name="qr_easy_to_scan" id="qrEasyInput" value="true">
                    </div>
                    <div class="conditional-field" id="qrProblemDescription">
                        <label style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 8px; display: block;">
                            What was the problem?
                        </label>
                        <textarea class="form-textarea" name="qr_problem_description" placeholder="Describe the QR code problem..." maxlength="300"></textarea>
                    </div>
                </div>

                <!-- Question 7: What improvements would you like to see? -->
                <div class="question-card">
                    <div class="question-title">
                        <span class="question-number">7</span>
                        What improvements would you like to see?
                    </div>
                    <label style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 8px; display: block;">
                        Optional, but we'd love to hear your suggestions!
                    </label>
                    <textarea class="form-textarea" name="suggestions" placeholder="Share your suggestions for improvement..." maxlength="1000"></textarea>
                </div>

                <!-- Question 8: Would you recommend this system to others? -->
                <div class="question-card">
                    <div class="question-title">
                        <span class="question-number">8</span>
                        Would you recommend this system to others?
                    </div>
                    <div class="radio-group">
                        <label class="radio-option">
                            <input type="radio" name="would_recommend" value="true" required>
                            <span>👍 Yes, I would recommend it</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="would_recommend" value="false" required>
                            <span>👎 No, I would not recommend it</span>
                        </label>
                    </div>
                </div>

                <!-- Submit Button -->
                <div style="margin-top: 32px;">
                    <button type="submit" class="submit-btn">
                        <span class="material-icons">send</span>
                        Submit Feedback
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced Feedback Form JavaScript
        (function() {
            'use strict';

            // Slider functionality
            const easeSlider = document.getElementById('easeSlider');
            const easeValue = document.getElementById('easeValue');

            easeSlider.addEventListener('input', function() {
                easeValue.textContent = this.value;
                const percentage = (this.value / 10) * 100;
                easeValue.style.color = `hsl(${percentage * 1.2}, 70%, 50%)`;
            });

            // Radio button selection styling
            const radioOptions = document.querySelectorAll('.radio-option');
            radioOptions.forEach(option => {
                const radio = option.querySelector('input[type="radio"]');
                radio.addEventListener('change', function() {
                    // Remove selected class from all options in the same group
                    const groupName = this.name;
                    document.querySelectorAll(`input[name="${groupName}"]`).forEach(r => {
                        r.closest('.radio-option').classList.remove('selected');
                    });
                    // Add selected class to current option
                    option.classList.add('selected');
                });
            });

            // Star rating functionality
            const stars = document.querySelectorAll('.star-rating .star');
            const designRatingInput = document.getElementById('designRatingInput');
            const designRatingText = document.getElementById('designRatingText');

            function updateStars(rating) {
                stars.forEach((star, i) => {
                    if (i < rating) {
                        star.classList.add('filled');
                    } else {
                        star.classList.remove('filled');
                    }
                });
            }

            stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                    const rating = parseInt(this.dataset.rating);
                    designRatingInput.value = rating;
                    updateStars(rating);

                    // Update rating text
                    const ratingTexts = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
                    designRatingText.textContent = ratingTexts[rating];
                });

                star.addEventListener('mouseenter', function() {
                    const rating = parseInt(this.dataset.rating);
                    stars.forEach((s, i) => {
                        if (i < rating) {
                            s.classList.add('hovered');
                        } else {
                            s.classList.remove('hovered');
                        }
                    });
                });
            });

            document.getElementById('designRating').addEventListener('mouseleave', function() {
                stars.forEach(s => s.classList.remove('hovered'));
                const currentRating = parseInt(designRatingInput.value) || 0;
                updateStars(currentRating);
            });

            // Form validation
            const form = document.getElementById('feedbackForm');
            form.addEventListener('submit', function(e) {
                let isValid = true;
                const errors = [];

                // Check required fields
                if (!easeSlider.value) {
                    errors.push('Please rate the ease of filling the form');
                    isValid = false;
                }

                if (!document.querySelector('input[name="time_taken"]:checked')) {
                    errors.push('Please select how long the registration took');
                    isValid = false;
                }

                if (!designRatingInput.value) {
                    errors.push('Please rate the visual appeal of the form');
                    isValid = false;
                }

                if (!document.querySelector('input[name="device_used"]:checked')) {
                    errors.push('Please select the device you used');
                    isValid = false;
                }

                if (!document.querySelector('input[name="would_recommend"]:checked')) {
                    errors.push('Please indicate if you would recommend this system');
                    isValid = false;
                }

                // Check conditional fields
                const facedIssues = document.getElementById('facedIssuesInput').value === 'true';
                if (facedIssues) {
                    const issueDesc = document.querySelector('textarea[name="issue_description"]').value.trim();
                    if (!issueDesc) {
                        errors.push('Please describe the issues you faced');
                        isValid = false;
                    }
                }

                const qrEasy = document.getElementById('qrEasyInput').value === 'true';
                if (!qrEasy) {
                    const qrDesc = document.querySelector('textarea[name="qr_problem_description"]').value.trim();
                    if (!qrDesc) {
                        errors.push('Please describe the QR code problem');
                        isValid = false;
                    }
                }

                if (!isValid) {
                    e.preventDefault();
                    alert('Please complete all required fields:\n\n' + errors.join('\n'));
                    return false;
                }

                // Add loading state
                const submitBtn = document.querySelector('.submit-btn');
                submitBtn.innerHTML = '<span class="material-icons">hourglass_empty</span> Submitting...';
                submitBtn.disabled = true;
            });

            // Auto-detect device type
            const deviceRadios = document.querySelectorAll('input[name="device_used"]');
            const userAgent = navigator.userAgent.toLowerCase();
            let detectedDevice = 'laptop_desktop'; // default

            if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
                detectedDevice = 'mobile';
            } else if (/tablet|ipad/i.test(userAgent)) {
                detectedDevice = 'tablet';
            }

            // Pre-select detected device
            const detectedRadio = document.querySelector(`input[name="device_used"][value="${detectedDevice}"]`);
            if (detectedRadio) {
                detectedRadio.checked = true;
                detectedRadio.closest('.radio-option').classList.add('selected');
            }

            // Smooth scroll to top on page load
            window.scrollTo({ top: 0, behavior: 'smooth' });

        })();

        // Toggle functions for conditional fields
        function toggleIssues() {
            const toggle = document.getElementById('issuesToggle');
            const input = document.getElementById('facedIssuesInput');
            const field = document.getElementById('issueDescription');

            toggle.classList.toggle('active');
            const isActive = toggle.classList.contains('active');

            input.value = isActive ? 'true' : 'false';

            if (isActive) {
                field.classList.add('show');
            } else {
                field.classList.remove('show');
                field.querySelector('textarea').value = '';
            }
        }

        function toggleQR() {
            const toggle = document.getElementById('qrToggle');
            const input = document.getElementById('qrEasyInput');
            const field = document.getElementById('qrProblemDescription');

            toggle.classList.toggle('active');
            const isActive = toggle.classList.contains('active');

            input.value = isActive ? 'true' : 'false';

            if (!isActive) {
                field.classList.add('show');
            } else {
                field.classList.remove('show');
                field.querySelector('textarea').value = '';
            }
        }
    </script>
</body>
</html>
