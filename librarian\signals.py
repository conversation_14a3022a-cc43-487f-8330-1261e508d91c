from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from .models import AnalyticsCache, Librarian_param
from studentsData.models import StudentData, Invoice
from visitorsData.models import Visitor


@receiver(post_save, sender=StudentData)
def student_data_changed(sender, instance, created, **kwargs):
    """
    Signal handler for when StudentData is created or updated.
    Marks analytics cache for recalculation.
    """
    try:
        # Get or create analytics cache for the librarian
        cache, created_cache = AnalyticsCache.objects.get_or_create(
            librarian=instance.librarian
        )
        cache.mark_student_activity()
        
        # If this is a new student registration, also mark it
        if created:
            cache.force_recalculate = True
            cache.save(update_fields=['force_recalculate'])
            
    except Exception as e:
        # Log error but don't break the main operation
        pass


@receiver(post_delete, sender=StudentData)
def student_data_deleted(sender, instance, **kwargs):
    """
    Signal handler for when StudentData is deleted.
    Marks analytics cache for recalculation.
    """
    try:
        if instance.librarian:
            cache, created = AnalyticsCache.objects.get_or_create(
                librarian=instance.librarian
            )
            cache.mark_student_activity()
            cache.force_recalculate = True
            cache.save(update_fields=['force_recalculate'])
            
    except Exception as e:
        pass


@receiver(post_save, sender=Invoice)
def invoice_changed(sender, instance, created, **kwargs):
    """
    Signal handler for when Invoice is created or updated.
    Marks analytics cache for recalculation.
    """
    try:
        # Get librarian from the student
        librarian = instance.student.librarian
        if librarian:
            cache, created_cache = AnalyticsCache.objects.get_or_create(
                librarian=librarian
            )
            cache.mark_invoice_activity()
            
            # If this is a new invoice, force recalculation
            if created:
                cache.force_recalculate = True
                cache.save(update_fields=['force_recalculate'])
                
    except Exception as e:
        pass


@receiver(post_delete, sender=Invoice)
def invoice_deleted(sender, instance, **kwargs):
    """
    Signal handler for when Invoice is deleted.
    Marks analytics cache for recalculation.
    """
    try:
        librarian = instance.student.librarian
        if librarian:
            cache, created = AnalyticsCache.objects.get_or_create(
                librarian=librarian
            )
            cache.mark_invoice_activity()
            cache.force_recalculate = True
            cache.save(update_fields=['force_recalculate'])
            
    except Exception as e:
        pass


@receiver(post_save, sender=Visitor)
def visitor_changed(sender, instance, created, **kwargs):
    """
    Signal handler for when Visitor is created or updated.
    Marks analytics cache for recalculation.
    """
    try:
        if instance.librarian:
            cache, created_cache = AnalyticsCache.objects.get_or_create(
                librarian=instance.librarian
            )
            cache.mark_visitor_activity()
            
            # If this is a new visitor, force recalculation
            if created:
                cache.force_recalculate = True
                cache.save(update_fields=['force_recalculate'])
                
    except Exception as e:
        pass


@receiver(post_delete, sender=Visitor)
def visitor_deleted(sender, instance, **kwargs):
    """
    Signal handler for when Visitor is deleted.
    Marks analytics cache for recalculation.
    """
    try:
        if instance.librarian:
            cache, created = AnalyticsCache.objects.get_or_create(
                librarian=instance.librarian
            )
            cache.mark_visitor_activity()
            cache.force_recalculate = True
            cache.save(update_fields=['force_recalculate'])
            
    except Exception as e:
        pass


def invalidate_analytics_cache(librarian):
    """
    Utility function to manually invalidate analytics cache for a librarian.
    Useful for manual cache management.
    """
    try:
        cache, created = AnalyticsCache.objects.get_or_create(librarian=librarian)
        cache.force_refresh()
        return True
    except Exception as e:
        return False


def get_or_create_analytics_cache(librarian):
    """
    Utility function to get or create analytics cache for a librarian.
    """
    try:
        cache, created = AnalyticsCache.objects.get_or_create(librarian=librarian)
        return cache
    except Exception as e:
        return None
