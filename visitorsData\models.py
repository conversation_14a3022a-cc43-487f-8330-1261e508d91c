from django.db import models
from django.utils import timezone
from django.utils.text import slugify
from librarian.models import *
from subLibrarian.models import Sublibrarian_param
from studentsData.models import *


def get_current_date():
    return timezone.now().date()


class Visitor(models.Model):
    librarian = models.ForeignKey(
        Librarian_param, on_delete=models.CASCADE, null=True, blank=True
    )
    sublibrarian = models.ForeignKey(
        Sublibrarian_param, on_delete=models.CASCADE, null=True, blank=True
    )
    date = models.DateField(default=get_current_date, blank=True, null=True)
    name = models.CharField(max_length=100)
    inqid = models.CharField(max_length=50, unique=True, blank=True)
    contact = models.BigIntegerField()
    email = models.EmailField(max_length=100, blank=True, null=True)
    shift = models.ManyToManyField(Shift, blank=True)
    notes = models.TextField(blank=True, null=True)
    callback = models.DateField(blank=True, null=True)
    status = models.CharField(
        max_length=20,
        default="pending",
    )
    slug = models.SlugField(max_length=250, unique=True, blank=True)

    def __str__(self):
        return f"{self.name} ({self.inqid})"

    def generate_inqid(self):
        librarian_name = self.librarian.user.first_name.replace(" ", "_").lower()
        today = timezone.now()
        date_str = today.strftime("%m%y")

        # Try to generate a unique inqid
        for attempt in range(1, 1000):  # Try up to 999 attempts
            inqid = f"inq_{librarian_name}_{date_str}_{attempt:03d}"

            # Check if this inqid already exists
            if not Visitor.objects.filter(inqid=inqid).exists():
                return inqid

        # If we couldn't generate a unique ID after 999 attempts
        raise ValueError(
            "The maximum number of inquiries for this day has been reached."
        )

    def save(self, *args, **kwargs):
        if not self.inqid:
            self.inqid = self.generate_inqid()

        # Generate slug from name and inqid if not provided
        if not self.slug:
            self.slug = slugify(f"{self.name}-{self.inqid}")

        super().save(*args, **kwargs)
