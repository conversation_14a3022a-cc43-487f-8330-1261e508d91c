# Merged Shift System - Usage Guide

## Overview

The Merged Shift system allows you to create a single entity that links multiple existing shifts together. This is useful for students who want to book seats across multiple time slots with special pricing and discount rules.

**✨ NEW: Full Overnight Shift Support!** The system now properly handles overnight shifts (e.g., 22:00 - 06:00) in all operations including creation, booking, attendance, and merging.

## Key Features

### 1. MergedShift Entity
- Links to multiple existing Shifts
- Has its own price and discount rules
- Calculates final price after applying discounts
- Tracks which shifts are included

### 2. Smart Seat Availability
- **Intersection Logic**: Only shows seats that are free in ALL linked shifts
- **Single Booking**: When booking a merged shift, the same seat is reserved across all linked shifts
- **Atomic Operations**: All bookings succeed or fail together

### 3. Flexible Attendance
- **Time Window Flexibility**: Attendance can be marked within ANY of the linked shift time windows
- **Per-Shift Recording**: Attendance is recorded separately for each shift for clarity
- **Automatic Validation**: System checks if check-in/out times fall within valid shift windows

## Database Models

### MergedShift
```python
- librarian: <PERSON><PERSON><PERSON> to Librarian_param
- name: <PERSON><PERSON><PERSON><PERSON> (name for the merged shift)
- shifts: ManyToManyField to <PERSON>ft (linked shifts)
- price: DecimalField (price for merged shift)
- discount: DecimalField (discount percentage 0-100)
- created_at: DateTimeField
- is_active: BooleanField
```

### MergedShiftBooking
```python
- student: ForeignKey to StudentData
- merged_shift: ForeignKey to MergedShift
- seat_number: CharField (seat reserved across all shifts)
- booking_date: DateField
- expire_date: DateField
```

### ShiftAttendance
```python
- student: ForeignKey to StudentData
- shift: ForeignKey to Shift
- merged_shift_booking: ForeignKey to MergedShiftBooking (optional)
- date: DateField
- check_in_time: TimeField (optional)
- check_out_time: TimeField (optional)
- is_present: BooleanField
- notes: TextField (optional)
```

## Usage Examples

### 1. Creating a Merged Shift
```python
# Create a merged shift for morning + evening slots
merged_shift = MergedShift.objects.create(
    librarian=librarian,
    name="Morning + Evening Combo",
    price=1500.00,
    discount=10.0  # 10% discount
)

# Add shifts to the merge
morning_shift = Shift.objects.get(name="Morning 6-10")
evening_shift = Shift.objects.get(name="Evening 6-10")
merged_shift.shifts.set([morning_shift, evening_shift])
```

### 2. Booking a Seat in Merged Shift
```python
from studentsData.utils import book_merged_shift_seat

# Book seat A-1 for a student in the merged shift
merged_booking, individual_bookings = book_merged_shift_seat(
    student=student,
    merged_shift=merged_shift,
    seat_number="A-1",
    expire_date=datetime.date(2024, 12, 31)
)

# This will:
# 1. Reserve seat A-1 in the morning shift
# 2. Reserve seat A-1 in the evening shift
# 3. Create a MergedShiftBooking record
# 4. Create individual Booking records for each shift
```

### 3. Marking Attendance
```python
from studentsData.utils import mark_merged_shift_attendance

# Mark attendance - student can check in during any shift window
attendances = mark_merged_shift_attendance(
    student=student,
    merged_shift=merged_shift,
    date=datetime.date.today(),
    check_in_time=datetime.time(7, 30),  # During morning shift
    check_out_time=datetime.time(21, 0), # During evening shift
    notes="Student attended both sessions"
)

# This creates attendance records for both shifts
```

### 4. Checking Availability
```python
from studentsData.utils import get_merged_shift_availability

# Get availability info for a merged shift
availability_info = get_merged_shift_availability(merged_shift)

print(f"Available seats: {availability_info['available_count']}")
print(f"Total duration: {availability_info['total_duration_minutes']} minutes")
print(f"Final price: ₹{availability_info['final_price']}")
```

## Business Rules

### Seat Booking Rules
1. **Single Shift**: Behaves as usual - book any available seat in that shift
2. **Merged Shift**: Only seats available in ALL linked shifts can be booked
3. **Same Seat Across Shifts**: The same seat number is reserved in each linked shift
4. **Atomic Booking**: All individual shift bookings must succeed, or none are created

### Attendance Rules
1. **Flexible Time Windows**: Student can check in/out during any linked shift's time window
2. **Per-Shift Recording**: Attendance is recorded separately for each shift
3. **Validation**: System validates if check-in/out times fall within valid shift windows
4. **Notes**: If time is outside normal windows, it's noted in the attendance record

### Pricing Rules
1. **Custom Pricing**: Merged shifts have their own price (not sum of individual shifts)
2. **Discount Support**: Percentage-based discounts can be applied
3. **Final Price Calculation**: `final_price = price - (price * discount / 100)`

## API Endpoints

- `GET /students/merged-shifts/` - List all merged shifts
- `GET /students/merged-shifts/create/` - Create new merged shift form
- `POST /students/merged-shifts/create/` - Create new merged shift
- `GET /students/merged-shifts/<id>/book/` - Book seat form
- `POST /students/merged-shifts/<id>/book/` - Book seat in merged shift
- `GET /students/merged-shifts/<id>/attendance/` - Mark attendance form
- `POST /students/merged-shifts/<id>/attendance/` - Mark attendance

## Migration

Run the following command to apply the database changes:

```bash
python manage.py makemigrations studentsData
python manage.py migrate
```

## Overnight Shift Support

### What's New
The system now fully supports overnight shifts (shifts that cross midnight):

- **Creation**: You can create shifts like "22:00 - 06:00" without validation errors
- **Time Validation**: Properly handles time checks across midnight boundary
- **Overlap Detection**: Correctly identifies overlaps with overnight shifts
- **Attendance**: Students can check in/out during any part of an overnight shift
- **Merged Shifts**: Can combine overnight shifts with regular shifts

### Examples of Overnight Shifts
```python
# Night shift: 10 PM to 6 AM (8 hours)
night_shift = Shift.objects.create(
    name="Night Study",
    start_time=time(22, 0),  # 10:00 PM
    end_time=time(6, 0),     # 6:00 AM (next day)
    price=800.00
)

# Late night shift: 11 PM to 7 AM (8 hours)
late_night = Shift.objects.create(
    name="Late Night",
    start_time=time(23, 0),  # 11:00 PM
    end_time=time(7, 0),     # 7:00 AM (next day)
    price=850.00
)
```

### Overnight Shift Validation
```python
# Check if a time falls within an overnight shift
night_shift = Shift.objects.get(name="Night Study")

# These times are valid for a 22:00-06:00 shift:
print(night_shift.is_time_within_shift(time(22, 30)))  # True - 10:30 PM
print(night_shift.is_time_within_shift(time(1, 0)))    # True - 1:00 AM
print(night_shift.is_time_within_shift(time(5, 30)))   # True - 5:30 AM

# These times are invalid:
print(night_shift.is_time_within_shift(time(12, 0)))   # False - 12:00 PM
print(night_shift.is_time_within_shift(time(8, 0)))    # False - 8:00 AM
```

### Merged Shifts with Overnight Components
```python
# Create a merged shift combining day and night shifts
day_shift = Shift.objects.get(name="Morning 6-12")      # 6 AM - 12 PM
night_shift = Shift.objects.get(name="Night 22-6")      # 10 PM - 6 AM

merged_shift = MergedShift.objects.create(
    name="Day + Night Combo",
    price=1200.00,
    discount=15.0
)
merged_shift.shifts.set([day_shift, night_shift])

# Students can attend during either time window
mark_merged_shift_attendance(
    student=student,
    merged_shift=merged_shift,
    date=today,
    check_in_time=time(23, 0),   # 11 PM (night shift)
    check_out_time=time(11, 0),  # 11 AM (day shift)
)
```

## Notes

- The system maintains backward compatibility with existing single-shift bookings
- Merged shifts are library-specific (each librarian manages their own)
- **✅ Overnight shifts are fully supported** in all operations
- All operations are atomic to prevent data inconsistency
- Frontend validation now allows overnight shift creation
- Time validation properly handles midnight boundary crossings
