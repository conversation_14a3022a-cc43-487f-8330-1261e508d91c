{% extends "base.html" %}

{% block title %}Seat Selection - {{ student.name }} - Librainian{% endblock %}

{% block page_title %}Seat Selection{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="/students/">Students</a></li>
<li class="breadcrumb-item active" aria-current="page">Seat Selection</li>
{% endblock %}

{% block extra_css %}
<style>

    /* Seat Selection Styles */
    .seat-selection-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .student-info-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 1.5rem;
        color: white;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .student-info-card h2 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        text-align: center;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .info-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .info-label {
        font-size: 0.875rem;
        opacity: 0.8;
        margin-bottom: 0.25rem;
        font-weight: 500;
    }

    .info-value {
        font-size: 1rem;
        font-weight: 600;
    }
        

    /* Seat Selection Interface */
    .seat-interface-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .selected-seats-display {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        color: white;
        min-height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    .selected-seat-badge {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        padding: 0.5rem 1rem;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .shift-filters {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        margin-bottom: 2rem;
        justify-content: center;
    }

    .shift-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(99, 102, 241, 0.3);
        color: #6366f1;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .shift-btn:hover,
    .shift-btn.active {
        background: #6366f1;
        color: white;
        border-color: #6366f1;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    }

    /* Seat Grid */
    .seat-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1rem;
        padding: 2rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 16px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .seat {
        width: 100%;
        height: 80px;
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(156, 163, 175, 0.3);
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .seat::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(99, 102, 241, 0.1) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .seat:hover::before {
        opacity: 1;
    }

    .seat.available {
        background: rgba(16, 185, 129, 0.1);
        border-color: #10b981;
        color: #065f46;
    }

    .seat.available:hover {
        background: #10b981;
        color: white;
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
    }

    .seat.selected {
        background: #6366f1;
        border-color: #6366f1;
        color: white;
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    }

    .seat.unavailable {
        background: rgba(239, 68, 68, 0.1);
        border-color: #ef4444;
        color: #991b1b;
        cursor: not-allowed;
        opacity: 0.6;
    }

    .seat-number {
        font-size: 0.875rem;
        font-weight: 700;
    }

    .seat-icon {
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
    }

    /* Confirm Button */
    .confirm-btn {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border: none;
        border-radius: 12px;
        padding: 1rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        width: 100%;
        max-width: 300px;
        margin: 2rem auto 0;
        display: block;
    }

    .confirm-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    }

    .confirm-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    /* Desktop Text Enhancement - ONLY for Book Seat Page */
    @media (min-width: 768px) {
        #book-seat-page-unique h5,
        #book-seat-page-unique .text-center h5,
        #book-seat-page-unique .text-muted,
        #book-seat-page-unique .seat-number,
        #book-seat-page-unique .shift-btn,
        #book-seat-page-unique .confirm-btn,
        #book-seat-page-unique .selected-seats-display,
        #book-seat-page-unique .student-info-card h2,
        #book-seat-page-unique .info-item strong,
        #book-seat-page-unique .info-item span,
        #book-seat-page-unique .seat-interface-card h5,
        #book-seat-page-unique .seat-interface-card .text-center h5,
        #book-seat-page-unique .seat-interface-card .text-muted {
            color: white !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        #book-seat-page-unique .text-muted,
        #book-seat-page-unique .seat-interface-card .text-muted {
            color: rgba(255, 255, 255, 0.8) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .seat-selection-container,
        .seat-interface-card {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .seat-grid {
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 0.75rem;
            padding: 1rem;
        }

        .seat {
            height: 60px;
        }

        .seat-icon {
            font-size: 1.25rem;
        }

        .seat-number {
            font-size: 0.75rem;
        }

        .shift-filters {
            gap: 0.5rem;
        }

        .shift-btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .info-grid {
            grid-template-columns: 1fr;
        }
    }

    </style>
{% endblock %}

{% block content %}
<div id="book-seat-page-unique" class="seat-selection-container fade-in">
    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    {% endif %}

    <!-- Student Information Card -->
    <div class="student-info-card">
        <h2><i class="fas fa-user-graduate me-2"></i>Student Information - {{ student.name }}</h2>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Student Name</div>
                <div class="info-value">{{ student.name }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Contact Number</div>
                <div class="info-value">{{ student.mobile }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Email Address</div>
                <div class="info-value">{{ student.email }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Gender</div>
                <div class="info-value">{{ student.gender }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Invoice Amount</div>
                <div class="info-value">₹{{ invoice.total_amount }}</div>
            </div>
        </div>
    </div>

    <!-- Seat Selection Interface -->
    <div class="seat-interface-card">
        <!-- Selected Seats Display -->
        <div class="selected-seats-display" id="selectedSeatsDisplay">
            <div class="text-center">
                <i class="fas fa-chair me-2"></i>
                <span>No seats selected yet</span>
            </div>
        </div>

        <!-- Shift Filters -->
        <div class="text-center mb-3">
            <h5 class="mb-3"><i class="fas fa-filter me-2"></i>Filter by Shift</h5>
            <div class="shift-filters">
                <button class="shift-btn active" onclick="filterSeats('all')" data-shift="all">
                    <i class="fas fa-th me-1"></i>All Shifts
                </button>
                {% for shift in shifts %}
                    <button class="shift-btn" onclick="filterSeats('{{ shift }}')" data-shift="{{ shift }}">
                        <i class="fas fa-clock me-1"></i>{{ shift }}
                    </button>
                {% endfor %}
            </div>

            <!-- Multi-shift selection info -->
            <div class="alert alert-info mt-3" style="background: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.3); border-radius: 8px; padding: 0.75rem; font-size: 0.875rem;">
                <i class="fas fa-info-circle me-2" style="color: #3b82f6;"></i>
                <strong>Multi-Shift Selection:</strong> When "All Shifts" is selected, choosing a seat will automatically select the same seat number across all available shifts.
            </div>
        </div>

        <!-- Seat Selection Title -->
        <div class="text-center mb-4">
            <h5><i class="fas fa-chair me-2"></i>Select Your Seats</h5>
            <p class="text-muted mb-0">Click on available seats to select them</p>
        </div>

        <!-- Seat Grid -->
        <div class="seat-grid" id="seatGrid">
            {% for seat in seats %}
                <div
                    class="seat {% if seat.is_available %}available{% else %}unavailable{% endif %}"
                    data-seat-id="{{ seat.id }}"
                    data-seat-number="{{ seat.seat_number }}"
                    data-shift="{{ seat.shift }}"
                    onclick="selectSeat(this)"
                    style="display: none;"
                >
                    <i class="fas fa-chair seat-icon"></i>
                    <span class="seat-number">{{ seat.seat_number }}</span>
                </div>
            {% endfor %}
        </div>

        <!-- Submit Form -->
        <form action="" method="POST" id="seatForm" class="text-center">
            {% csrf_token %}
            <input type="hidden" name="selectedSeats" id="selectedSeats">
            <button type="submit" class="confirm-btn" id="confirmBtn" disabled>
                <i class="fas fa-check-circle me-2"></i>Confirm Seat Selection
            </button>
        </form>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Modern Seat Selection System
    class SeatSelection {
        constructor() {
            this.selectedSeats = new Set();
            this.currentFilter = 'all';
            this.init();
        }

        init() {
            this.setupEventListeners();
            this.filterSeats('all');
            this.updateDisplay();
        }

        setupEventListeners() {
            // Form submission
            document.getElementById('seatForm').addEventListener('submit', (e) => {
                if (this.selectedSeats.size === 0) {
                    e.preventDefault();
                    this.showToast('Please select at least one seat', 'warning');
                }
            });
        }

        filterSeats(selectedShift) {
            // Update active filter button
            document.querySelectorAll('.shift-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-shift="${selectedShift}"]`).classList.add('active');

            this.currentFilter = selectedShift;

            // Show/hide seats based on filter
            document.querySelectorAll('.seat').forEach(seat => {
                const seatShift = seat.dataset.shift;
                if (selectedShift === 'all' || seatShift === selectedShift) {
                    seat.style.display = 'flex';
                } else {
                    seat.style.display = 'none';
                }
            });
        }

        selectSeat(element) {
            // Check if seat is unavailable
            if (element.classList.contains('unavailable')) {
                this.showToast('This seat is not available', 'error');
                return;
            }

            const seatId = element.dataset.seatId;
            const seatNumber = element.dataset.seatNumber;
            const shift = element.dataset.shift;

            // Check if "All Shifts" filter is active
            const isAllShiftsFilter = this.currentFilter === 'all';

            // Toggle selection
            if (element.classList.contains('selected')) {
                // Deselecting - remove this seat and potentially matching seats in other shifts
                element.classList.remove('selected');
                this.selectedSeats.delete(seatId);

                // If "All Shifts" is selected, deselect same seat number in all shifts
                if (isAllShiftsFilter) {
                    this.deselectSameSeatInAllShifts(seatNumber, seatId);
                }
            } else {
                // Selecting - add this seat and potentially matching seats in other shifts
                element.classList.add('selected');
                this.selectedSeats.add(seatId);

                // If "All Shifts" is selected, select same seat number in all shifts
                if (isAllShiftsFilter) {
                    this.selectSameSeatInAllShifts(seatNumber, seatId);
                }
            }

            this.updateDisplay();
            this.updateHiddenInput();
        }

        selectSameSeatInAllShifts(seatNumber, excludeSeatId) {
            // Find all seats with the same seat number in different shifts
            const allSeats = document.querySelectorAll('.seat');
            allSeats.forEach(seat => {
                if (seat.dataset.seatNumber === seatNumber &&
                    seat.dataset.seatId !== excludeSeatId &&
                    seat.classList.contains('available')) {

                    seat.classList.add('selected');
                    this.selectedSeats.add(seat.dataset.seatId);
                }
            });
        }

        deselectSameSeatInAllShifts(seatNumber, excludeSeatId) {
            // Find all seats with the same seat number in different shifts
            const allSeats = document.querySelectorAll('.seat');
            allSeats.forEach(seat => {
                if (seat.dataset.seatNumber === seatNumber &&
                    seat.dataset.seatId !== excludeSeatId) {

                    seat.classList.remove('selected');
                    this.selectedSeats.delete(seat.dataset.seatId);
                }
            });
        }

        updateDisplay() {
            const displayDiv = document.getElementById('selectedSeatsDisplay');
            const confirmBtn = document.getElementById('confirmBtn');

            if (this.selectedSeats.size === 0) {
                displayDiv.innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-chair me-2"></i>
                        <span>No seats selected yet</span>
                    </div>
                `;
                confirmBtn.disabled = true;
            } else {
                const selectedElements = Array.from(this.selectedSeats).map(seatId => {
                    const seatElement = document.querySelector(`[data-seat-id="${seatId}"]`);
                    const seatNumber = seatElement.dataset.seatNumber;
                    const shift = seatElement.dataset.shift;
                    return `
                        <div class="selected-seat-badge">
                            <i class="fas fa-chair me-1"></i>
                            ${seatNumber} - ${shift}
                        </div>
                    `;
                }).join('');

                displayDiv.innerHTML = selectedElements;
                confirmBtn.disabled = false;
            }
        }

        updateHiddenInput() {
            document.getElementById('selectedSeats').value = Array.from(this.selectedSeats).join(',');
        }

        showToast(message, type = 'info') {
            // Create toast notification
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(toast);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 3000);
        }
    }

    // Global functions for backward compatibility
    function filterSeats(shift) {
        window.seatSelection.filterSeats(shift);
    }

    function selectSeat(element) {
        window.seatSelection.selectSeat(element);
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        window.seatSelection = new SeatSelection();
    });
</script>

{% endblock %}
