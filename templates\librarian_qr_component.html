<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="robots" content="noindex, nofollow">
    <title>QR Code Component</title>

    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <!-- Import master CSS variables from code_temp.html -->
    {% include "code_temp.html" %}

    <style>
        :root {
            /* QR Component specific variable aliases for compatibility */
            --primary-color: var(--primary);
            --primary-dark: var(--primary-dark);
            --secondary-color: var(--gray-800);
            --text-color: var(--gray-900);
            --text-light: var(--gray-500);
            --text-white: var(--white);
            --glass-bg: var(--glass-bg);
            --glass-border: var(--glass-border);
            --shadow-sm: var(--shadow-sm);
            --shadow-md: var(--shadow-md);
            --shadow-glass: var(--glass-shadow);
            --border-radius: var(--border-radius);
            --transition: var(--transition);
        }

        * {
            box-sizing: border-box;
        }

        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
        }

        /* Glass morphism container */
        .qr-code-container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            flex: 1;
            padding: 2rem 1rem;
            position: relative;
        }

        /* Glass card design */
        .qr-card {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: var(--border-radius);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-glass);
            padding: 2rem;
            text-align: center;
            max-width: 400px;
            width: 100%;
            position: relative;
            transition: var(--transition);
            animation: slideUp 0.6s ease-out;
        }

        .qr-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            border-radius: var(--border-radius);
            pointer-events: none;
        }

        .qr-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(31, 38, 135, 0.5);
        }

        .qr-card h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-white);
            position: relative;
            z-index: 1;
        }

        .qr-image-container {
            position: relative;
            margin: 1.5rem 0;
            z-index: 1;
        }

        .qr-card img {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            max-width: 100%;
            height: auto;
            transition: var(--transition);
            background: white;
            padding: 0.5rem;
            box-shadow: var(--shadow-md);
        }

        .qr-card img:hover {
            transform: scale(1.05);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .qr-card p {
            font-size: 0.95rem;
            margin-top: 1rem;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.5;
            position: relative;
            z-index: 1;
        }

        /* Modern button design */
        .download-btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            border: none;
            border-radius: 50px;
            padding: 0.75rem 2rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
            position: relative;
            z-index: 1;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
            background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
        }

        .download-btn:active {
            transform: translateY(0);
        }

        .download-btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
        }

        /* Footer styling */
        footer {
            margin-top: auto;
            padding: 1.5rem;
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.875rem;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(8px);
        }

        /* Animations */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .qr-code-container {
                padding: 1rem 0.5rem;
            }

            .qr-card {
                max-width: 350px;
                padding: 1.5rem;
            }

            .qr-card h1 {
                font-size: 1.3rem;
            }

            .download-btn {
                padding: 0.6rem 1.5rem;
                font-size: 0.95rem;
            }
        }

        @media (max-width: 480px) {
            .qr-card {
                max-width: 100%;
                margin: 0 0.5rem;
            }

            .qr-card img {
                border-width: 2px;
            }
        }

        /* Dark mode compatibility */
        @media (prefers-color-scheme: dark) {
            .qr-card img {
                background: #f8f9fa;
            }
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus indicators */
        .download-btn:focus-visible {
            outline: 2px solid var(--text-white);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <div class="qr-code-container">
        <div class="qr-card">
            <h1>
                <i class="fas fa-qrcode me-2"></i>
                QR Code for {{ librarian.library_name }}
            </h1>

            <div class="qr-image-container">
                <img id="qrImage"
                     src="data:image/png;base64,{{ qr_code }}"
                     alt="QR Code for {{ librarian.library_name }}"
                     class="img-fluid"
                     loading="lazy">
            </div>

            <p>
                <i class="fas fa-mobile-alt me-2"></i>
                Scan the QR code above to view the details
            </p>

            <button class="download-btn"
                    onclick="downloadQR()"
                    aria-label="Download QR Code"
                    type="button">
                <i class="fas fa-download"></i>
                Download QR Code
            </button>
        </div>
    </div>

    <footer>
        <div class="container">
            <p class="mb-0">
                <i class="fas fa-shield-alt me-2"></i>
                &copy; {% now "Y" %} Librainian - Secure Library Management System. All Rights Reserved.
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced download function with error handling and analytics
        function downloadQR() {
            try {
                const qrImage = document.getElementById('qrImage');
                if (!qrImage || !qrImage.src) {
                    throw new Error('QR code image not found');
                }

                const link = document.createElement('a');
                link.href = qrImage.src;
                link.download = '{{ librarian.library_name|slugify }}_qr_code.png';
                link.style.display = 'none';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success feedback
                showToast('QR Code downloaded successfully!', 'success');

                // Track download event
                if (typeof gtag === 'function') {
                    gtag('event', 'qr_download', {
                        'event_category': 'Engagement',
                        'event_label': '{{ librarian.library_name }} QR Component'
                    });
                }
            } catch (error) {
                console.error('Download failed:', error);
                showToast('Download failed. Please try again.', 'error');
            }
        }

        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast-notification toast-${type}`;
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
            `;

            // Add toast styles
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : '#ef4444'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                animation: slideInRight 0.3s ease-out;
                font-size: 0.9rem;
                max-width: 300px;
            `;

            document.body.appendChild(toast);

            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (toast.parentNode) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // Add CSS animations for toast
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize component
        document.addEventListener('DOMContentLoaded', function() {
            // Focus management for accessibility
            const downloadBtn = document.querySelector('.download-btn');
            if (downloadBtn) {
                downloadBtn.focus();
            }

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    if (e.target.classList.contains('download-btn')) {
                        e.preventDefault();
                        downloadQR();
                    }
                }
            });

            // Track component view
            if (typeof gtag === 'function') {
                gtag('event', 'qr_component_view', {
                    'event_category': 'Engagement',
                    'event_label': '{{ librarian.library_name }} QR Component'
                });
            }
        });
    </script>
</body>

